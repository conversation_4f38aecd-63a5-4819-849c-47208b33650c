# Contributing to OCTAVE

We welcome contributions to the OCTAVE specification and examples!

## How to Contribute

### Reporting Issues
- Use GitHub Issues to report problems or suggest improvements
- Include examples showing the issue
- Specify which LLM you tested with

### Submitting Changes

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-addition`)
3. Make your changes
4. Test your examples with multiple LLMs (<PERSON>T<PERSON>4, <PERSON>, <PERSON>)
5. Commit with clear messages
6. Push to your fork
7. Open a Pull Request

### What We're Looking For

- **New Examples**: Real-world compression scenarios
- **Specification Clarifications**: Making the spec clearer
- **Guide Improvements**: Better explanations for LLM usage
- **Bug Fixes**: Issues in examples or documentation

### Guidelines

- All OCTAVE examples must be validated
- Compression claims must be measurable
- Changes to core specifications require discussion first
- Keep the minimalist philosophy - less is more

## Questions?

Open an issue for discussion before making large changes.

Thank you for helping make OCTAVE better!