# OCTAVE Test Prompts

Copy-paste prompts for testing OCTAVE primer effectiveness.

## Quick Test Battery

### 1. Basic Status
```
Describe a system under heavy load
```

### 2. Mythological Pattern
```
Analyze a project that started well but is now failing due to overconfidence
```

### 3. Tension Operator
```
Show the tension between system reliability and feature delivery speed
```

### 4. Nested Structure
```
Model a microservices architecture with authentication, database, and API services
```

### 5. Operators Combined
```
Describe a deployment pipeline and a strategy combining wisdom with decisive action
```

### 6. No Prose Test
```
Explain OCTAVE format
```

## Advanced Test Cases

### 7. Complex Analysis
```
Diagnose a system where database locks are causing cascading failures across multiple services
```

### 8. Multiple Forces
```
Describe a startup facing time pressure, resource constraints, and scaling challenges
```

### 9. Architectural Decision
```
Present options for migrating from monolith to microservices with tradeoffs
```

### 10. Incident Response
```
Document a security incident from detection through resolution
```

## Edge Cases

### 11. Numbers and Metrics
```
Report on system performance metrics including latency percentiles and error rates
```

### 12. Boolean States
```
List feature flags and their current activation status
```

### 13. Empty Values
```
Show a configuration with some undefined optional settings
```

## Semantic Activation Tests

### 14. Domain Assignment
```
Assign mythological domains to components of a modern tech stack
```

### 15. Pattern Recognition
```
Identify the mythological pattern in a system that keeps requiring more resources
```

### 16. Force Dynamics
```
What forces are at play when a deadline approaches but quality standards must be maintained
```