[package]
name = "standalone-api"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core async runtime
tokio = { version = "1.0", features = ["full"] }

# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }
redis = { version = "0.24", features = ["tokio-comp"] }

# Utilities
anyhow = "1.0"
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Shared crates (only the ones that compile)
database = { path = "../crates/database" }
blockchain-engine = { path = "../crates/blockchain-engine" }
shared-utils = { path = "../crates/shared-utils" }
data-scraper = { path = "../crates/data-scraper" }
