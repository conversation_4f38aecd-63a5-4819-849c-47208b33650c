//! Standalone API Server for Web3 Gaming Scraper
//! 
//! A minimal, working API server that demonstrates the core functionality
//! without complex dependencies that have compilation issues.

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    routing::get,
    Router,
};
use blockchain_engine::solana::{SolanaClient, RateLimitConfig, ConnectionConfig};
use blockchain_engine::multi_chain::{MultiChainClient, ChainConfig};
use database::DatabaseManager;
use serde::{Deserialize, Serialize};
use shared_utils::{Chain, Address, Web3GamingError};
use std::sync::Arc;
use tokio::net::TcpListener;
use tracing::{info, error, warn};

// Application state
#[derive(Clone)]
struct AppState {
    db: Arc<DatabaseManager>,
    solana_client: Arc<SolanaClient>,
    multi_chain_client: Arc<MultiChainClient>,
}

// Response types
#[derive(Serialize)]
struct HealthResponse {
    status: String,
    database: String,
    cache: String,
    timestamp: String,
    version: String,
}

#[derive(Serialize)]
struct StatsResponse {
    total_queries: u64,
    cache_hits: u64,
    cache_misses: u64,
    uptime: String,
    database_status: String,
}

#[derive(Serialize)]
struct ChainInfo {
    name: String,
    id: String,
    supported: bool,
    rpc_status: String,
}

#[derive(Deserialize)]
struct ChainQuery {
    chain: Option<String>,
    limit: Option<u32>,
}

#[derive(Deserialize)]
struct TestQuery {
    key: Option<String>,
    value: Option<String>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("standalone_api=debug,database=debug,shared_utils=debug")
        .init();
    
    info!("🚀 Starting Standalone Web3 Gaming API Server");
    
    // Get configuration from environment
    let postgres_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://postgres:postgres@localhost:5432/gaming_tracker".to_string());
    let redis_url = std::env::var("REDIS_URL")
        .unwrap_or_else(|_| "redis://localhost:6379/0".to_string());
    let solana_rpc_url = std::env::var("SOLANA_RPC_URL")
        .unwrap_or_else(|_| "https://api.mainnet-beta.solana.com".to_string());
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "8001".to_string())
        .parse::<u16>()
        .unwrap_or(8001);
    
    info!("📊 Database URL: {}", postgres_url);
    info!("🔄 Redis URL: {}", redis_url);
    info!("⚡ Solana RPC URL: {}", solana_rpc_url);
    info!("🌐 Port: {}", port);
    
    // Initialize database
    let db = match DatabaseManager::new(&postgres_url, &redis_url).await {
        Ok(db) => {
            info!("✅ Database connection established successfully");
            Arc::new(db)
        }
        Err(e) => {
            error!("❌ Failed to connect to database: {}", e);
            warn!("💡 Make sure your Docker containers are running:");
            warn!("   PostgreSQL: docker run -d --name postgres_gaming -p 5432:5432 -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=gaming_tracker postgres:15");
            warn!("   Redis: docker run -d --name redis_gaming -p 6379:6379 redis:7-alpine");
            return Err(e.into());
        }
    };
    
    // Test database connection
    let health = db.health_check().await;
    info!("🏥 Health check - PostgreSQL: {}, Redis: {}",
          health.postgres_healthy, health.redis_healthy);

    // Initialize Solana client with enhanced configuration
    let rate_limit_config = RateLimitConfig {
        requests_per_second: 50, // Conservative for mainnet
        burst_capacity: 100,
    };
    let connection_config = ConnectionConfig {
        max_connections: 5,
        timeout_seconds: 30,
        retry_attempts: 3,
        retry_delay_ms: 1000,
    };

    let solana_client = Arc::new(SolanaClient::with_config(
        solana_rpc_url.clone(),
        rate_limit_config,
        connection_config,
    ));

    info!("⚡ Solana client initialized with rate limiting and connection pooling");

    // Initialize multi-chain client
    let mut multi_chain_client = MultiChainClient::new();

    // Add Solana support
    let solana_config = ChainConfig {
        rpc_url: solana_rpc_url.clone(),
        chain_id: None,
        block_time_ms: 400, // Solana ~400ms block time
        max_retries: 3,
        timeout_seconds: 30,
        rate_limit_per_second: 50,
    };
    multi_chain_client.add_chain(shared_utils::Chain::Solana, solana_config).await
        .expect("Failed to add Solana chain");

    // Add Ethereum support (placeholder)
    let ethereum_rpc_url = std::env::var("ETHEREUM_RPC_URL")
        .unwrap_or_else(|_| "https://eth-mainnet.alchemyapi.io/v2/demo".to_string());
    let ethereum_config = ChainConfig {
        rpc_url: ethereum_rpc_url,
        chain_id: Some(1),
        block_time_ms: 12000, // Ethereum ~12s block time
        max_retries: 3,
        timeout_seconds: 30,
        rate_limit_per_second: 30,
    };
    multi_chain_client.add_chain(shared_utils::Chain::Ethereum, ethereum_config).await
        .expect("Failed to add Ethereum chain");

    // Add Polygon support (placeholder)
    let polygon_rpc_url = std::env::var("POLYGON_RPC_URL")
        .unwrap_or_else(|_| "https://polygon-rpc.com".to_string());
    let polygon_config = ChainConfig {
        rpc_url: polygon_rpc_url,
        chain_id: Some(137),
        block_time_ms: 2000, // Polygon ~2s block time
        max_retries: 3,
        timeout_seconds: 20,
        rate_limit_per_second: 50,
    };
    multi_chain_client.add_chain(shared_utils::Chain::Polygon, polygon_config).await
        .expect("Failed to add Polygon chain");

    // Add Arbitrum support (placeholder)
    let arbitrum_rpc_url = std::env::var("ARBITRUM_RPC_URL")
        .unwrap_or_else(|_| "https://arb1.arbitrum.io/rpc".to_string());
    let arbitrum_config = ChainConfig {
        rpc_url: arbitrum_rpc_url,
        chain_id: Some(42161),
        block_time_ms: 1000, // Arbitrum ~1s block time
        max_retries: 3,
        timeout_seconds: 15,
        rate_limit_per_second: 60,
    };
    multi_chain_client.add_chain(shared_utils::Chain::Arbitrum, arbitrum_config).await
        .expect("Failed to add Arbitrum chain");

    let multi_chain_client = Arc::new(multi_chain_client);
    info!("🌐 Multi-chain client initialized with 4 blockchain networks");

    // Create application state
    let state = AppState {
        db,
        solana_client,
        multi_chain_client,
    };
    
    // Build the router with CORS
    let app = Router::new()
        .route("/", get(root))
        .route("/health", get(health_check))
        .route("/stats", get(stats))
        .route("/chains", get(list_chains))
        .route("/test-cache", get(test_cache))
        .route("/test-cache-custom", get(test_cache_custom))
        .route("/database-info", get(database_info))
        .route("/solana/health", get(solana_health))
        .route("/solana/slot", get(solana_current_slot))
        .route("/solana/account/:address", get(solana_account_info))
        .route("/solana/analyze/:address", get(solana_analyze_gaming))
        .route("/solana/transaction/:signature", get(solana_transaction_analysis))
        .route("/solana/gaming-transactions", get(solana_recent_gaming_transactions))
        .route("/solana/contract/:program_id", get(solana_contract_analysis))
        .route("/solana/discover-contracts", get(solana_discover_gaming_contracts))
        .route("/solana/detection-stats", get(solana_detection_stats))
        .route("/multi-chain/health", get(multi_chain_health))
        .route("/multi-chain/block/:chain/:number", get(multi_chain_get_block))
        .route("/multi-chain/transaction/:chain/:hash", get(multi_chain_get_transaction))
        .route("/multi-chain/account/:chain/:address", get(multi_chain_get_account))
        .route("/multi-chain/balance/:chain/:address", get(multi_chain_get_balance))
        .with_state(state)
        .layer(
            tower_http::cors::CorsLayer::new()
                .allow_origin(tower_http::cors::Any)
                .allow_methods(tower_http::cors::Any)
                .allow_headers(tower_http::cors::Any)
        )
        .layer(tower_http::trace::TraceLayer::new_for_http());
    
    // Start the server
    let addr = format!("0.0.0.0:{}", port);
    info!("🌐 Server starting on http://{}", addr);
    
    let listener = TcpListener::bind(&addr).await?;
    info!("✅ Server listening on {}", addr);
    info!("🎮 Web3 Gaming Scraper API is ready!");
    info!("📖 Available endpoints:");
    info!("   GET  /           - API information");
    info!("   GET  /health     - Health check");
    info!("   GET  /stats      - Database statistics");
    info!("   GET  /chains     - Supported blockchains");
    info!("   GET  /test-cache - Test cache functionality");
    info!("   GET  /database-info - Database connection info");
    info!("   GET  /solana/health - Solana RPC health check");
    info!("   GET  /solana/slot - Current Solana slot");
    info!("   GET  /solana/account/:address - Solana account info");
    info!("   GET  /solana/analyze/:address - Gaming analysis");
    info!("   GET  /solana/transaction/:signature - Transaction gaming analysis");
    info!("   GET  /solana/gaming-transactions - Recent gaming transactions");
    info!("   GET  /solana/contract/:program_id - Gaming contract analysis");
    info!("   GET  /solana/discover-contracts - Discover gaming contracts");
    info!("   GET  /solana/detection-stats - Gaming detection statistics");
    info!("   GET  /multi-chain/health - Multi-chain health check");
    info!("   GET  /multi-chain/block/:chain/:number - Get block by chain and number");
    info!("   GET  /multi-chain/transaction/:chain/:hash - Get transaction by chain and hash");
    info!("   GET  /multi-chain/account/:chain/:address - Get account info by chain");
    info!("   GET  /multi-chain/balance/:chain/:address - Get balance by chain");
    
    axum::serve(listener, app).await?;
    
    Ok(())
}

// Route handlers
async fn root() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "name": "🎮 Web3 Gaming Scraper API",
        "version": "0.1.0",
        "status": "running",
        "description": "Standalone API server for Web3 gaming data analysis",
        "endpoints": {
            "/": "API information",
            "/health": "Health check with database status",
            "/stats": "Database and cache statistics", 
            "/chains": "List of supported blockchain networks",
            "/test-cache": "Test Redis cache functionality",
            "/test-cache-custom": "Test cache with custom key/value",
            "/database-info": "Database connection information",
            "/solana/health": "Solana RPC health and connection stats",
            "/solana/slot": "Current Solana blockchain slot",
            "/solana/account/:address": "Get Solana account information",
            "/solana/analyze/:address": "Analyze address for gaming relevance",
            "/solana/transaction/:signature": "Analyze transaction for gaming activity",
            "/solana/gaming-transactions": "Get recent gaming transactions",
            "/solana/contract/:program_id": "Analyze program for gaming features",
            "/solana/discover-contracts": "Discover gaming contracts on Solana",
            "/solana/detection-stats": "Gaming contract detection statistics",
            "/multi-chain/health": "Health check for all blockchain networks",
            "/multi-chain/block/:chain/:number": "Get block information across chains",
            "/multi-chain/transaction/:chain/:hash": "Get transaction across chains",
            "/multi-chain/account/:chain/:address": "Get account information across chains",
            "/multi-chain/balance/:chain/:address": "Get token balance across chains"
        },
        "features": [
            "PostgreSQL database integration",
            "Redis caching layer",
            "Multi-chain blockchain support (Solana, Ethereum, Polygon, Arbitrum)",
            "Enhanced Solana blockchain integration",
            "Rate-limited RPC connections",
            "Gaming-specific contract analysis",
            "Unified cross-chain interface",
            "Real-time health monitoring",
            "Performance metrics"
        ],
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

async fn health_check(
    State(state): State<AppState>,
) -> Json<HealthResponse> {
    let health = state.db.health_check().await;
    let status = if health.postgres_healthy && health.redis_healthy {
        "healthy"
    } else {
        "degraded"
    };

    let response = HealthResponse {
        status: status.to_string(),
        database: if health.postgres_healthy { "connected" } else { "disconnected" }.to_string(),
        cache: if health.redis_healthy { "connected" } else { "disconnected" }.to_string(),
        timestamp: chrono::Utc::now().to_rfc3339(),
        version: "0.1.0".to_string(),
    };
    Json(response)
}

async fn stats(
    State(state): State<AppState>,
) -> Json<StatsResponse> {
    let metrics = state.db.get_metrics();
    
    // Test database connectivity
    let health = state.db.health_check().await;
    let db_status = if health.postgres_healthy && health.redis_healthy {
        "fully_operational"
    } else if health.postgres_healthy {
        "postgres_only"
    } else if health.redis_healthy {
        "redis_only"
    } else {
        "disconnected"
    };
    
    Json(StatsResponse {
        total_queries: metrics.total_queries.load(std::sync::atomic::Ordering::Relaxed),
        cache_hits: metrics.cache_hits.load(std::sync::atomic::Ordering::Relaxed),
        cache_misses: metrics.cache_misses.load(std::sync::atomic::Ordering::Relaxed),
        uptime: "runtime_tracking_not_implemented".to_string(),
        database_status: db_status.to_string(),
    })
}

async fn list_chains() -> Json<serde_json::Value> {
    let chains = vec![
        ChainInfo {
            name: "Ethereum".to_string(),
            id: "ethereum".to_string(),
            supported: true,
            rpc_status: "configured".to_string(),
        },
        ChainInfo {
            name: "Polygon".to_string(),
            id: "polygon".to_string(),
            supported: true,
            rpc_status: "configured".to_string(),
        },
        ChainInfo {
            name: "Solana".to_string(),
            id: "solana".to_string(),
            supported: true,
            rpc_status: "priority_chain".to_string(),
        },
        ChainInfo {
            name: "Arbitrum".to_string(),
            id: "arbitrum".to_string(),
            supported: true,
            rpc_status: "configured".to_string(),
        },
        ChainInfo {
            name: "Base".to_string(),
            id: "base".to_string(),
            supported: false,
            rpc_status: "planned".to_string(),
        },
    ];
    
    Json(serde_json::json!({
        "chains": chains,
        "total_supported": chains.iter().filter(|c| c.supported).count(),
        "priority_chain": "solana",
        "last_updated": chrono::Utc::now().to_rfc3339()
    }))
}

async fn test_cache(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    test_cache_operation(&state, "api_test_key", "default_test_value").await
}

async fn test_cache_custom(
    State(state): State<AppState>,
    Query(params): Query<TestQuery>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let key = params.key.unwrap_or_else(|| "custom_test_key".to_string());
    let value = params.value.unwrap_or_else(|| format!("custom_value_{}", chrono::Utc::now().timestamp()));
    
    test_cache_operation(&state, &key, &value).await
}

async fn test_cache_operation(
    state: &AppState,
    test_key: &str,
    test_value: &str,
) -> Result<Json<serde_json::Value>, StatusCode> {
    use redis::AsyncCommands;
    
    // Get a Redis connection
    let mut redis_conn = state.db.redis_pool.as_ref().clone();
    
    // Test SET operation
    match redis_conn.set::<&str, &str, ()>(test_key, test_value).await {
        Ok(_) => info!("✅ Cache SET successful for key: {}", test_key),
        Err(e) => {
            error!("❌ Cache SET failed: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    }
    
    // Test GET operation
    let retrieved_value: Option<String> = match redis_conn.get(test_key).await {
        Ok(value) => value,
        Err(e) => {
            error!("❌ Cache GET failed: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };
    
    // Test DELETE operation
    let _: () = match redis_conn.del(test_key).await {
        Ok(result) => result,
        Err(e) => {
            error!("❌ Cache DELETE failed: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };
    
    Ok(Json(serde_json::json!({
        "cache_test": "success",
        "operations": {
            "set": "ok",
            "get": "ok", 
            "delete": "ok"
        },
        "test_key": test_key,
        "test_value": test_value,
        "retrieved_value": retrieved_value,
        "values_match": retrieved_value.as_deref() == Some(test_value),
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

async fn database_info(
    State(state): State<AppState>,
) -> Json<serde_json::Value> {
    let metrics = state.db.get_metrics();

    // Calculate hit ratio outside the JSON macro
    let hits = metrics.cache_hits.load(std::sync::atomic::Ordering::Relaxed);
    let misses = metrics.cache_misses.load(std::sync::atomic::Ordering::Relaxed);
    let hit_ratio = if hits + misses > 0 {
        (hits as f64 / (hits + misses) as f64 * 100.0).round()
    } else {
        0.0
    };

    Json(serde_json::json!({
        "database": {
            "type": "PostgreSQL",
            "status": "connected",
            "pool_info": "connection_pooling_active"
        },
        "cache": {
            "type": "Redis",
            "status": "connected",
            "connection_info": "single_connection"
        },
        "metrics": {
            "total_queries": metrics.total_queries.load(std::sync::atomic::Ordering::Relaxed),
            "cache_hits": hits,
            "cache_misses": misses,
            "hit_ratio": hit_ratio
        },
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

// Solana endpoint handlers
async fn solana_health(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.solana_client.get_health().await {
        Ok(health_status) => {
            let stats = state.solana_client.get_connection_stats();
            Ok(Json(serde_json::json!({
                "solana_rpc": {
                    "status": health_status,
                    "healthy": health_status == "ok",
                    "connection_stats": {
                        "total_requests": stats.total_requests,
                        "rate_limit": {
                            "requests_per_second": stats.rate_limit_config.requests_per_second,
                            "burst_capacity": stats.rate_limit_config.burst_capacity
                        },
                        "connection_config": {
                            "max_connections": stats.connection_config.max_connections,
                            "timeout_seconds": stats.connection_config.timeout_seconds,
                            "retry_attempts": stats.connection_config.retry_attempts
                        }
                    }
                },
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Solana health check failed: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

async fn solana_current_slot(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.solana_client.get_slot().await {
        Ok(slot) => {
            Ok(Json(serde_json::json!({
                "current_slot": slot,
                "network": "mainnet-beta",
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get Solana slot: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

async fn solana_account_info(
    State(state): State<AppState>,
    Path(address): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.solana_client.get_account_info(&address).await {
        Ok(account_info) => {
            Ok(Json(serde_json::json!({
                "address": address,
                "account_info": account_info,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get Solana account info for {}: {}", address, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn solana_analyze_gaming(
    State(state): State<AppState>,
    Path(address): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.solana_client.analyze_gaming_relevance(&address).await {
        Ok(analysis) => {
            Ok(Json(serde_json::json!({
                "address": address,
                "gaming_analysis": analysis,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to analyze Solana address {}: {}", address, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn solana_transaction_analysis(
    State(state): State<AppState>,
    Path(signature): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.solana_client.analyze_transaction_gaming(&signature).await {
        Ok(analysis) => {
            Ok(Json(serde_json::json!({
                "signature": signature,
                "gaming_analysis": analysis,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to analyze Solana transaction {}: {}", signature, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn solana_recent_gaming_transactions(
    State(state): State<AppState>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let limit = params.get("limit")
        .and_then(|l| l.parse::<usize>().ok())
        .unwrap_or(10);

    match state.solana_client.get_recent_gaming_transactions(Some(limit)).await {
        Ok(transactions) => {
            Ok(Json(serde_json::json!({
                "gaming_transactions": transactions,
                "count": transactions.len(),
                "limit": limit,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get recent gaming transactions: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

async fn solana_contract_analysis(
    State(state): State<AppState>,
    Path(program_id): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.solana_client.analyze_gaming_contract(&program_id).await {
        Ok(analysis) => {
            Ok(Json(serde_json::json!({
                "program_id": program_id,
                "contract_analysis": analysis,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to analyze gaming contract {}: {}", program_id, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn solana_discover_gaming_contracts(
    State(state): State<AppState>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let limit = params.get("limit")
        .and_then(|l| l.parse::<usize>().ok())
        .unwrap_or(20);

    match state.solana_client.discover_gaming_contracts(Some(limit)).await {
        Ok(contracts) => {
            Ok(Json(serde_json::json!({
                "gaming_contracts": contracts,
                "count": contracts.len(),
                "limit": limit,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to discover gaming contracts: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

async fn solana_detection_stats(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let stats = state.solana_client.get_gaming_detection_stats();

    Ok(Json(serde_json::json!({
        "detection_statistics": stats,
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

// Multi-chain endpoint handlers
async fn multi_chain_health(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match state.multi_chain_client.health_check_all().await {
        Ok(health_statuses) => {
            let supported_chains = state.multi_chain_client.get_supported_chains().await;

            Ok(Json(serde_json::json!({
                "multi_chain_health": health_statuses,
                "supported_chains": supported_chains,
                "total_chains": supported_chains.len(),
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Multi-chain health check failed: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

async fn multi_chain_get_block(
    State(state): State<AppState>,
    Path((chain_str, block_number)): Path<(String, u64)>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let chain = parse_chain(&chain_str)?;

    match state.multi_chain_client.get_block(chain, block_number).await {
        Ok(block_info) => {
            Ok(Json(serde_json::json!({
                "chain": chain_str,
                "block_number": block_number,
                "block_info": block_info,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get block {} for chain {}: {}", block_number, chain_str, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn multi_chain_get_transaction(
    State(state): State<AppState>,
    Path((chain_str, tx_hash)): Path<(String, String)>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let chain = parse_chain(&chain_str)?;

    match state.multi_chain_client.get_transaction(chain, &tx_hash).await {
        Ok(tx_info) => {
            Ok(Json(serde_json::json!({
                "chain": chain_str,
                "transaction_hash": tx_hash,
                "transaction_info": tx_info,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get transaction {} for chain {}: {}", tx_hash, chain_str, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn multi_chain_get_account(
    State(state): State<AppState>,
    Path((chain_str, address)): Path<(String, String)>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let chain = parse_chain(&chain_str)?;

    match state.multi_chain_client.get_account_info(chain, &address).await {
        Ok(account_info) => {
            Ok(Json(serde_json::json!({
                "chain": chain_str,
                "address": address,
                "account_info": account_info,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get account {} for chain {}: {}", address, chain_str, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn multi_chain_get_balance(
    State(state): State<AppState>,
    Path((chain_str, address)): Path<(String, String)>,
    Query(params): Query<std::collections::HashMap<String, String>>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let chain = parse_chain(&chain_str)?;
    let token_address = params.get("token").map(|s| s.as_str());

    match state.multi_chain_client.get_token_balance(chain, &address, token_address).await {
        Ok(balance_info) => {
            Ok(Json(serde_json::json!({
                "chain": chain_str,
                "address": address,
                "token_address": token_address,
                "balance_info": balance_info,
                "timestamp": chrono::Utc::now().to_rfc3339()
            })))
        }
        Err(e) => {
            error!("Failed to get balance for {} on chain {}: {}", address, chain_str, e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

// Helper function to parse chain string
fn parse_chain(chain_str: &str) -> Result<shared_utils::Chain, StatusCode> {
    match chain_str.to_lowercase().as_str() {
        "solana" => Ok(shared_utils::Chain::Solana),
        "ethereum" | "eth" => Ok(shared_utils::Chain::Ethereum),
        "polygon" | "matic" => Ok(shared_utils::Chain::Polygon),
        "arbitrum" | "arb" => Ok(shared_utils::Chain::Arbitrum),
        _ => {
            error!("Unsupported chain: {}", chain_str);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}
