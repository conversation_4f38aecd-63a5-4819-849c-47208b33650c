<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web3 Gaming Scraper - Live Monitoring Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1rem;
        }
        
        .dashboard {
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #4fc3f7;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-healthy { background-color: #4caf50; }
        .status-warning { background-color: #ff9800; }
        .status-error { background-color: #f44336; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
        }
        
        .metric-value {
            font-weight: bold;
            color: #4fc3f7;
        }
        
        .refresh-btn {
            background: #4fc3f7;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #29b6f6;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .error {
            color: #f44336;
            font-style: italic;
        }
        
        .timestamp {
            font-size: 0.9rem;
            opacity: 0.7;
            margin-top: 1rem;
        }
        
        .activity-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            border-left: 3px solid #4fc3f7;
        }
        
        .activity-item:last-child {
            margin-bottom: 0;
        }
        
        .activity-blockchain {
            font-weight: bold;
            color: #4fc3f7;
            text-transform: uppercase;
            font-size: 0.8rem;
        }
        
        .activity-details {
            font-size: 0.9rem;
            margin-top: 0.25rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎮 Web3 Gaming Scraper</h1>
        <p>Live Monitoring Dashboard - Real-time system observation</p>
    </div>
    
    <div class="dashboard">
        <!-- System Status Card -->
        <div class="card">
            <h2>🔍 System Status</h2>
            <div id="system-status">
                <div class="metric">
                    <span class="metric-label">Overall Status</span>
                    <span class="metric-value" id="overall-status">
                        <span class="status-indicator status-healthy"></span>Loading...
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Database</span>
                    <span class="metric-value" id="database-status">
                        <span class="status-indicator status-healthy"></span>Loading...
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Redis Cache</span>
                    <span class="metric-value" id="redis-status">
                        <span class="status-indicator status-healthy"></span>Loading...
                    </span>
                </div>
            </div>
            <button class="refresh-btn" onclick="refreshSystemStatus()">Refresh Status</button>
        </div>
        
        <!-- Database Metrics Card -->
        <div class="card">
            <h2>💾 Database Metrics</h2>
            <div id="database-metrics">
                <div class="metric">
                    <span class="metric-label">Webhook Subscriptions</span>
                    <span class="metric-value" id="webhook-count">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Webhook Deliveries</span>
                    <span class="metric-value" id="delivery-count">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Gaming Projects</span>
                    <span class="metric-value" id="project-count">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Gaming Contracts</span>
                    <span class="metric-value" id="contract-count">Loading...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Blockchain Data</span>
                    <span class="metric-value" id="blockchain-count">Loading...</span>
                </div>
            </div>
            <button class="refresh-btn" onclick="refreshDatabaseMetrics()">Refresh Metrics</button>
        </div>
        
        <!-- Gaming Projects Card -->
        <div class="card">
            <h2>🎮 Gaming Projects</h2>
            <div id="gaming-projects">
                Loading gaming projects...
            </div>
            <button class="refresh-btn" onclick="refreshGamingProjects()">Refresh Projects</button>
        </div>
        
        <!-- Recent Activity Card -->
        <div class="card">
            <h2>⛓️ Recent Blockchain Activity</h2>
            <div id="recent-activity">
                Loading recent activity...
            </div>
            <button class="refresh-btn" onclick="refreshRecentActivity()">Refresh Activity</button>
        </div>
        
        <!-- Webhook Subscriptions Card -->
        <div class="card">
            <h2>🔗 Webhook Subscriptions</h2>
            <div id="webhook-subscriptions">
                Loading webhook subscriptions...
            </div>
            <button class="refresh-btn" onclick="refreshWebhookSubscriptions()">Refresh Subscriptions</button>
        </div>
        
        <!-- System Info Card -->
        <div class="card">
            <h2>ℹ️ System Information</h2>
            <div class="metric">
                <span class="metric-label">API Version</span>
                <span class="metric-value">1.0.0</span>
            </div>
            <div class="metric">
                <span class="metric-label">Environment</span>
                <span class="metric-value">Development</span>
            </div>
            <div class="metric">
                <span class="metric-label">Auto Refresh</span>
                <span class="metric-value">
                    <button class="refresh-btn" onclick="toggleAutoRefresh()" id="auto-refresh-btn">Enable</button>
                </span>
            </div>
            <div class="timestamp" id="last-updated">
                Last updated: Never
            </div>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8001';
        let autoRefreshInterval = null;
        
        // Utility functions
        function updateTimestamp() {
            document.getElementById('last-updated').textContent = 
                `Last updated: ${new Date().toLocaleTimeString()}`;
        }
        
        function setLoading(elementId, isLoading) {
            const element = document.getElementById(elementId);
            if (isLoading) {
                element.classList.add('loading');
            } else {
                element.classList.remove('loading');
            }
        }
        
        function getStatusIndicator(status) {
            const statusClass = status === 'connected' || status === 'healthy' ? 'status-healthy' : 
                               status === 'degraded' ? 'status-warning' : 'status-error';
            return `<span class="status-indicator ${statusClass}"></span>${status}`;
        }
        
        // API functions
        async function refreshSystemStatus() {
            setLoading('system-status', true);
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                
                document.getElementById('overall-status').innerHTML = getStatusIndicator(data.status);
                document.getElementById('database-status').innerHTML = getStatusIndicator(data.components.database.status);
                document.getElementById('redis-status').innerHTML = getStatusIndicator(data.components.redis.status);
                
                updateTimestamp();
            } catch (error) {
                console.error('Failed to refresh system status:', error);
                document.getElementById('overall-status').innerHTML = '<span class="error">Error loading</span>';
            }
            setLoading('system-status', false);
        }
        
        async function refreshDatabaseMetrics() {
            setLoading('database-metrics', true);
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                const counts = data.components.database.table_counts;
                
                document.getElementById('webhook-count').textContent = counts.webhook_subscriptions || 0;
                document.getElementById('delivery-count').textContent = counts.webhook_deliveries || 0;
                document.getElementById('project-count').textContent = counts.gaming_projects || 0;
                document.getElementById('contract-count').textContent = counts.gaming_contracts || 0;
                document.getElementById('blockchain-count').textContent = counts.blockchain_data || 0;
                
                updateTimestamp();
            } catch (error) {
                console.error('Failed to refresh database metrics:', error);
            }
            setLoading('database-metrics', false);
        }
        
        async function refreshGamingProjects() {
            setLoading('gaming-projects', true);
            try {
                const response = await fetch(`${API_BASE}/api/v1/gaming/projects`);
                const data = await response.json();
                
                const projectsHtml = data.projects.map(project => `
                    <div class="activity-item">
                        <div class="activity-blockchain">${project.blockchain}</div>
                        <div class="activity-details">
                            <strong>${project.project_name}</strong> (${project.primary_genre})
                            <br>Status: ${project.is_active ? 'Active' : 'Inactive'}
                        </div>
                    </div>
                `).join('');
                
                document.getElementById('gaming-projects').innerHTML = 
                    projectsHtml || '<div class="error">No gaming projects found</div>';
                
                updateTimestamp();
            } catch (error) {
                console.error('Failed to refresh gaming projects:', error);
                document.getElementById('gaming-projects').innerHTML = '<div class="error">Error loading projects</div>';
            }
            setLoading('gaming-projects', false);
        }
        
        async function refreshRecentActivity() {
            setLoading('recent-activity', true);
            try {
                const response = await fetch(`${API_BASE}/api/v1/blockchain/recent`);
                const data = await response.json();
                
                const activityHtml = data.activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-blockchain">${activity.blockchain}</div>
                        <div class="activity-details">
                            <strong>${activity.event_type}</strong>
                            <br>Contract: ${activity.contract_address?.substring(0, 10)}...
                            ${activity.token_price_usd ? `<br>Value: $${activity.token_price_usd}` : ''}
                        </div>
                    </div>
                `).join('');
                
                document.getElementById('recent-activity').innerHTML = 
                    activityHtml || '<div class="error">No recent activity found</div>';
                
                updateTimestamp();
            } catch (error) {
                console.error('Failed to refresh recent activity:', error);
                document.getElementById('recent-activity').innerHTML = '<div class="error">Error loading activity</div>';
            }
            setLoading('recent-activity', false);
        }
        
        async function refreshWebhookSubscriptions() {
            setLoading('webhook-subscriptions', true);
            try {
                const response = await fetch(`${API_BASE}/api/v1/webhooks/subscriptions`);
                const data = await response.json();
                
                const subscriptionsHtml = data.subscriptions.map(sub => `
                    <div class="activity-item">
                        <div class="activity-blockchain">${sub.is_active ? 'ACTIVE' : 'INACTIVE'}</div>
                        <div class="activity-details">
                            <strong>${sub.subscriber_name}</strong>
                            <br>Deliveries: ${sub.total_deliveries} (${sub.successful_deliveries} success)
                            <br>Verified: ${sub.is_verified ? 'Yes' : 'No'}
                        </div>
                    </div>
                `).join('');
                
                document.getElementById('webhook-subscriptions').innerHTML = 
                    subscriptionsHtml || '<div class="error">No webhook subscriptions found</div>';
                
                updateTimestamp();
            } catch (error) {
                console.error('Failed to refresh webhook subscriptions:', error);
                document.getElementById('webhook-subscriptions').innerHTML = '<div class="error">Error loading subscriptions</div>';
            }
            setLoading('webhook-subscriptions', false);
        }
        
        function toggleAutoRefresh() {
            const btn = document.getElementById('auto-refresh-btn');
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = 'Enable';
            } else {
                autoRefreshInterval = setInterval(refreshAll, 30000); // 30 seconds
                btn.textContent = 'Disable';
            }
        }
        
        function refreshAll() {
            refreshSystemStatus();
            refreshDatabaseMetrics();
            refreshGamingProjects();
            refreshRecentActivity();
            refreshWebhookSubscriptions();
        }
        
        // Initial load
        document.addEventListener('DOMContentLoaded', function() {
            refreshAll();
        });
    </script>
</body>
</html>
