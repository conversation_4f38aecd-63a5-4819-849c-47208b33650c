{"rustc": 15497389221046826682, "features": "[\"atoi_simd\", \"chrono\", \"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-time\", \"fast-float\", \"itoa\", \"lazy\", \"polars-time\", \"ryu\", \"simdutf8\", \"temporal\"]", "declared_features": "[\"async\", \"async-trait\", \"atoi_simd\", \"avro\", \"aws\", \"azure\", \"chrono\", \"chrono-tz\", \"cloud\", \"csv\", \"decompress\", \"decompress-fast\", \"default\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"fast-float\", \"file_cache\", \"flate2\", \"fmt\", \"futures\", \"gcp\", \"http\", \"ipc\", \"ipc_streaming\", \"itoa\", \"json\", \"lazy\", \"object\", \"object_store\", \"parquet\", \"partition\", \"polars-json\", \"polars-parquet\", \"polars-time\", \"python\", \"reqwest\", \"ryu\", \"serde\", \"serde_json\", \"simd\", \"simd-json\", \"simdutf8\", \"temporal\", \"timezones\", \"tokio\", \"tokio-util\", \"url\", \"zstd\"]", "target": 2031945042685161972, "profile": 8276155916380437441, "path": 2989031318017342240, "deps": [[40386456601120721, "percent_encoding", false, 6466076441445340421], [966925859616469517, "ahash", false, 16917607473362308882], [1216309103264968120, "ryu", false, 17910386745663053527], [1737279643867088440, "polars_utils", false, 10916294720058699258], [3419428956812390430, "smartstring", false, 1808345005597031298], [3722963349756955755, "once_cell", false, 3950140573650441576], [4544379658388519060, "home", false, 2030054054950699209], [5157631553186200874, "num_traits", false, 3983708198095814638], [7695812897323945497, "itoa", false, 18104670705255413382], [8067010153367330186, "simdutf8", false, 4455228269351234043], [9451456094439810778, "regex", false, 878718001504540918], [9690350829298438995, "polars_time", false, 13189940873260876407], [9897246384292347999, "chrono", false, 9268331838963614633], [10697383615564341592, "rayon", false, 3113060211295609744], [11243224374683126366, "polars_error", false, 3406120419987733069], [12734988801541075419, "memmap", false, 9547511504484767425], [14517035561158255785, "arrow", false, 15400189539991997372], [15932120279885307830, "memchr", false, 1281753996029912424], [16066129441945555748, "bytes", false, 1974571504047377931], [16583050384810566368, "atoi_simd", false, 11444094226075075308], [16620239925640633684, "polars_core", false, 16846759873613872156], [17433304786843909481, "fast_float", false, 10771220090403917355]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-io-b838b6669afcd1d0/dep-lib-polars_io", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}