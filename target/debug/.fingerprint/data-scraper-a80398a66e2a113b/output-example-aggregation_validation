{"$message_type":"diagnostic","message":"unused import: `sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/data-scraper/examples/aggregation_validation.rs","byte_start":800,"byte_end":805,"line_start":16,"line_end":16,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"crates/data-scraper/examples/aggregation_validation.rs","byte_start":800,"byte_end":807,"line_start":16,"line_end":16,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/data-scraper/examples/aggregation_validation.rs","byte_start":799,"byte_end":800,"line_start":16,"line_end":16,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/data-scraper/examples/aggregation_validation.rs","byte_start":815,"byte_end":816,"line_start":16,"line_end":16,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, Duration};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/data-scraper/examples/aggregation_validation.rs:16:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::{sleep, Duration};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"1 warning emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 1 warning emitted\u001b[0m\n\n"}
