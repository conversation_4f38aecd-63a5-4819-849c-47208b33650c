{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"async\", \"async-stream\", \"bloom_filter\", \"brotli\", \"compression\", \"fallible-streaming-iterator\", \"flate2\", \"futures\", \"gzip\", \"gzip_zlib_ng\", \"lz4\", \"lz4_flex\", \"serde\", \"serde_types\", \"snap\", \"snappy\", \"xxhash-rust\", \"zstd\"]", "target": 4138808511274316409, "profile": 8276155916380437441, "path": 10353146984748429304, "deps": [[966925859616469517, "ahash", false, 16917607473362308882], [1737279643867088440, "polars_utils", false, 10916294720058699258], [5157631553186200874, "num_traits", false, 3983708198095814638], [7898571650830454567, "ethnum", false, 1881489061163792463], [8067010153367330186, "simdutf8", false, 4455228269351234043], [11243224374683126366, "polars_error", false, 3406120419987733069], [11685816543203387617, "streaming_decompression", false, 1363668282342062945], [13077212702700853852, "base64", false, 6897581960676534907], [14517035561158255785, "arrow", false, 15400189539991997372], [16121279737985519728, "parquet_format_safe", false, 10676257711508035396], [17929261977799616229, "polars_compute", false, 3439563883014117661]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-parquet-d8bfde76a0feddbf/dep-lib-polars_parquet", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}