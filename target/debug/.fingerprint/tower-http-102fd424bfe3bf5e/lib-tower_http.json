{"rustc": 15497389221046826682, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 5347358027863023418, "path": 4940994714767780968, "deps": [[784494742817713399, "tower_service", false, 976358663622631765], [1906322745568073236, "pin_project_lite", false, 10122047953940016221], [7712452662827335977, "tower_layer", false, 5958092446026363953], [7896293946984509699, "bitflags", false, 4199894223454998670], [8606274917505247608, "tracing", false, 9217579458450593339], [9010263965687315507, "http", false, 15190774565033300876], [14084095096285906100, "http_body", false, 3858752339072201350], [16066129441945555748, "bytes", false, 16337691178649358567], [16900715236047033623, "http_body_util", false, 18085953101452228190]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-102fd424bfe3bf5e/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}