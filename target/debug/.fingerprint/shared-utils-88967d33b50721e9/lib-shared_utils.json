{"rustc": 15497389221046826682, "features": "[\"default\", \"sqlx\"]", "declared_features": "[\"default\", \"sqlx\"]", "target": 6655291900496003818, "profile": 6675295047989516842, "path": 15376344883518451600, "deps": [[3405707034081185165, "dotenvy", false, 15055560603815943521], [4495526598637097934, "parking_lot", false, 13905995194819255023], [7244058819997729774, "reqwest", false, 3144034380016640644], [8008191657135824715, "thiserror", false, 11345645093265826359], [8319709847752024821, "uuid", false, 17869130911446549586], [8606274917505247608, "tracing", false, 9217579458450593339], [9689903380558560274, "serde", false, 1652587176100301358], [9897246384292347999, "chrono", false, 9850446743430126220], [10632374999838431203, "sqlx", false, 2936572315867471907], [12382237672615274180, "config", false, 17853164611129698600], [12393800526703971956, "tokio", false, 13711536135186096817], [13625485746686963219, "anyhow", false, 9563385383867021308], [15367738274754116744, "serde_json", false, 273018631490583565], [16230660778393187092, "tracing_subscriber", false, 7991284307095066955]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/shared-utils-88967d33b50721e9/dep-lib-shared_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}