{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"business\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"find_many\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"hive_partitions\", \"interpolate\", \"interpolate_by\", \"ipc\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"nightly\", \"object\", \"offset_by\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-json\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 8885459555497909574, "profile": 5347358027863023418, "path": 18442313352138674292, "deps": [[40386456601120721, "percent_encoding", false, 8191989742803952923], [455332427680959199, "polars_ops", false, 13564820156936230461], [519668612969056153, "strum_macros", false, 11158857306431403195], [966925859616469517, "ahash", false, 15005525890638504195], [1737279643867088440, "polars_utils", false, 17516090028869116942], [3419428956812390430, "smartstring", false, 12329604167311017704], [3722963349756955755, "once_cell", false, 9250329688931680279], [6240045547040120597, "polars_io", false, 10037827378252496034], [6511429716036861196, "bytemuck", false, 12249584660493990846], [9690350829298438995, "polars_time", false, 16547472187235890366], [10697383615564341592, "rayon", false, 17666425492764063832], [11487583573386935581, "recursive", false, 10602238534960265711], [12170264697963848012, "either", false, 12794257162807497770], [13018563866916002725, "hashbrown", false, 6906628132907056355], [14517035561158255785, "arrow", false, 4036699857447306546], [16620239925640633684, "polars_core", false, 561396548417138493], [17120771218538166117, "build_script_build", false, 3273057865188144679]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-9dcb3d22dec137a4/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}