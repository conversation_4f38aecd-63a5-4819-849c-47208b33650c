{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlx-macros\", \"sqlx-postgres\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 5347358027863023418, "path": 11330134083678863168, "deps": [[228475551920078470, "sqlx_macros", false, 12633362898716767030], [996810380461694889, "sqlx_core", false, 6674336978252272870], [15634168271133386882, "sqlx_postgres", false, 1584374502822031370]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-3e6a1444884c3b1f/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}