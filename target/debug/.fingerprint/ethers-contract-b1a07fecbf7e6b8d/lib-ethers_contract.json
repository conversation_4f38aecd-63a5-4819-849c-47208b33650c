{"rustc": 15497389221046826682, "features": "[\"abigen\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"providers\", \"rustls\"]", "declared_features": "[\"abigen\", \"abigen-offline\", \"abigen-online\", \"celo\", \"default\", \"eip712\", \"ethers-contract-abigen\", \"ethers-contract-derive\", \"ethers-providers\", \"legacy\", \"openssl\", \"optimism\", \"providers\", \"rustls\"]", "target": 7173054194013103692, "profile": 5347358027863023418, "path": 11111386741765582682, "deps": [[2141440161325128830, "ethers_contract_abigen", false, 7781807987376652342], [3722963349756955755, "once_cell", false, 9250329688931680279], [5030409040180886167, "ethers_core", false, 5456012772859933700], [6264115378959545688, "pin_project", false, 17035967466811142729], [8008191657135824715, "thiserror", false, 16255346684290760672], [8524202948771681628, "ethers_contract_derive", false, 7866805456244894344], [9689903380558560274, "serde", false, 8709775620457530939], [10629569228670356391, "futures_util", false, 11492648470296180545], [13003293521383684806, "hex", false, 4244812444998491169], [14417566112632113257, "ethers_providers", false, 8607689384802699012], [15367738274754116744, "serde_json", false, 11853957577942380444]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ethers-contract-b1a07fecbf7e6b8d/dep-lib-ethers_contract", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}