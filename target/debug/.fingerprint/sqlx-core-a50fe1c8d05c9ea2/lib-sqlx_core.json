{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 5347358027863023418, "path": 17041482572124269129, "deps": [[5103565458935487, "futures_io", false, 2617041617408126875], [40386456601120721, "percent_encoding", false, 8191989742803952923], [530211389790465181, "hex", false, 8406936689739084727], [788558663644978524, "crossbeam_queue", false, 4207299019762808806], [966925859616469517, "ahash", false, 18369609744956247039], [1162433738665300155, "crc", false, 1820750152141112274], [1464803193346256239, "event_listener", false, 8230162268592936092], [1811549171721445101, "futures_channel", false, 1211472837598201951], [3150220818285335163, "url", false, 7945323999013012823], [3405817021026194662, "hashlink", false, 14599801035415219257], [3646857438214563691, "futures_intrusive", false, 10328823580811836689], [3666196340704888985, "smallvec", false, 15902415398285707708], [3712811570531045576, "byteorder", false, 10852973252125295283], [3722963349756955755, "once_cell", false, 9250329688931680279], [5986029879202738730, "log", false, 14372202331545107381], [6493259146304816786, "indexmap", false, 11434938633746865478], [7620660491849607393, "futures_core", false, 2383678945188290618], [8008191657135824715, "thiserror", false, 16255346684290760672], [8319709847752024821, "uuid", false, 4527770802189718398], [8606274917505247608, "tracing", false, 8312589604480262083], [9689903380558560274, "serde", false, 8709775620457530939], [9857275760291862238, "sha2", false, 5626466165695565402], [9897246384292347999, "chrono", false, 187977539902157986], [10629569228670356391, "futures_util", false, 11492648470296180545], [10862088793507253106, "sqlformat", false, 445932637713683657], [11295624341523567602, "rustls", false, 10641254860032273575], [12170264697963848012, "either", false, 9220636862394057335], [12393800526703971956, "tokio", false, 12508040185417025102], [15367738274754116744, "serde_json", false, 11853957577942380444], [15932120279885307830, "memchr", false, 15504980094282092926], [16066129441945555748, "bytes", false, 16108965486125341533], [16311359161338405624, "rustls_pemfile", false, 15881483248844770546], [16973251432615581304, "tokio_stream", false, 4320190141074606933], [17106256174509013259, "atoi", false, 8420170829599764998], [17605717126308396068, "paste", false, 10476848073614706140], [17652733826348741533, "webpki_roots", false, 17853647655319698975]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-a50fe1c8d05c9ea2/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}