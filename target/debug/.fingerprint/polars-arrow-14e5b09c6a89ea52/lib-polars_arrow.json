{"rustc": 15497389221046826682, "features": "[\"arrow-format\", \"atoi_simd\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_hash\", \"compute_take\", \"compute_temporal\", \"fast-float\", \"io_ipc\", \"itoa\", \"multiversion\", \"ryu\", \"strength_reduce\", \"temporal\"]", "declared_features": "[\"arrow-array\", \"arrow-buffer\", \"arrow-data\", \"arrow-format\", \"arrow-schema\", \"arrow_rs\", \"async-stream\", \"atoi\", \"atoi_simd\", \"avro-schema\", \"bigidx\", \"chrono-tz\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_hash\", \"compute_take\", \"compute_temporal\", \"default\", \"dtype-array\", \"dtype-decimal\", \"fast-float\", \"full\", \"futures\", \"hex\", \"indexmap\", \"io_avro\", \"io_avro_async\", \"io_avro_compression\", \"io_flight\", \"io_ipc\", \"io_ipc_compression\", \"io_ipc_read_async\", \"io_ipc_write_async\", \"itoa\", \"itoap\", \"lz4\", \"multiversion\", \"nightly\", \"performant\", \"regex\", \"regex-syntax\", \"ryu\", \"serde\", \"simd\", \"strength_reduce\", \"strings\", \"temporal\", \"timezones\", \"zstd\"]", "target": 5005847566128143416, "profile": 8276155916380437441, "path": 12901887289821863680, "deps": [[966925859616469517, "ahash", false, 16917607473362308882], [1216309103264968120, "ryu", false, 17910386745663053527], [1737279643867088440, "polars_utils", false, 10916294720058699258], [3726277658779405417, "strength_reduce", false, 6961032737405812873], [5157631553186200874, "num_traits", false, 3983708198095814638], [6096440479827326274, "multiversion", false, 6547465609882082556], [6511429716036861196, "bytemuck", false, 1672816269885430399], [7695812897323945497, "itoa", false, 18104670705255413382], [7898571650830454567, "ethnum", false, 1881489061163792463], [8067010153367330186, "simdutf8", false, 4455228269351234043], [9122563107207267705, "dyn_clone", false, 2870004634398091013], [9235208004366183979, "streaming_iterator", false, 16890740069854091555], [9897246384292347999, "chrono", false, 9268331838963614633], [9938377527623146791, "foreign_vec", false, 16582751281981177639], [11243224374683126366, "polars_error", false, 3406120419987733069], [12170264697963848012, "either", false, 17390189878655533154], [13015077130101160787, "arrow_format", false, 8858603762620801249], [13018563866916002725, "hashbrown", false, 3299620658094797291], [14517035561158255785, "build_script_build", false, 4542995532385092749], [16583050384810566368, "atoi_simd", false, 11444094226075075308], [17433304786843909481, "fast_float", false, 10771220090403917355]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-arrow-14e5b09c6a89ea52/dep-lib-polars_arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}