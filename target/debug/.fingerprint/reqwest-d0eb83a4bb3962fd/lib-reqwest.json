{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 8276155916380437441, "path": 9776420804298061517, "deps": [[40386456601120721, "percent_encoding", false, 6466076441445340421], [95042085696191081, "ipnet", false, 11063114189766479070], [264090853244900308, "sync_wrapper", false, 13930855123240482014], [784494742817713399, "tower_service", false, 2202304606038860701], [1044435446100926395, "hyper_rustls", false, 16031905519739385062], [1906322745568073236, "pin_project_lite", false, 7773456665029720526], [3150220818285335163, "url", false, 17066871592244810921], [3722963349756955755, "once_cell", false, 3950140573650441576], [4405182208873388884, "http", false, 10530892365283716272], [5986029879202738730, "log", false, 540550529245414849], [7414427314941361239, "hyper", false, 10913701219332417755], [7620660491849607393, "futures_core", false, 12134165656439499170], [8915503303801890683, "http_body", false, 8488203500508017895], [9689903380558560274, "serde", false, 12364537043182369665], [10229185211513642314, "mime", false, 3946337273449542715], [10629569228670356391, "futures_util", false, 10689215794273529932], [11107720164717273507, "system_configuration", false, 16207986384974825839], [11295624341523567602, "rustls", false, 3591407004760412052], [12186126227181294540, "tokio_native_tls", false, 17741360478831740957], [12367227501898450486, "hyper_tls", false, 5658290499703748170], [12393800526703971956, "tokio", false, 18218432810481653859], [13763625454224483636, "h2", false, 14345848918979473864], [14564311161534545801, "encoding_rs", false, 14910067114114436954], [15367738274754116744, "serde_json", false, 14576692903234035802], [16066129441945555748, "bytes", false, 1974571504047377931], [16311359161338405624, "rustls_pemfile", false, 16202291361653268027], [16542808166767769916, "serde_urlencoded", false, 15995436459804084171], [16622232390123975175, "tokio_rustls", false, 3395707465488493417], [16785601910559813697, "native_tls_crate", false, 10545283649496288179], [17652733826348741533, "webpki_roots", false, 1519446600155896425], [18066890886671768183, "base64", false, 3336679374406924033]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-d0eb83a4bb3962fd/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}