{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"async\", \"async-stream\", \"bloom_filter\", \"brotli\", \"compression\", \"fallible-streaming-iterator\", \"flate2\", \"futures\", \"gzip\", \"gzip_zlib_ng\", \"lz4\", \"lz4_flex\", \"serde\", \"serde_types\", \"snap\", \"snappy\", \"xxhash-rust\", \"zstd\"]", "target": 4138808511274316409, "profile": 5347358027863023418, "path": 10353146984748429304, "deps": [[966925859616469517, "ahash", false, 15005525890638504195], [1737279643867088440, "polars_utils", false, 17516090028869116942], [5157631553186200874, "num_traits", false, 13181684718241081284], [7898571650830454567, "ethnum", false, 2664396464211697769], [8067010153367330186, "simdutf8", false, 1465554792876887024], [11243224374683126366, "polars_error", false, 14690282539794651279], [11685816543203387617, "streaming_decompression", false, 14847918430458003074], [13077212702700853852, "base64", false, 17470592536754067809], [14517035561158255785, "arrow", false, 4036699857447306546], [16121279737985519728, "parquet_format_safe", false, 13698214208697533287], [17929261977799616229, "polars_compute", false, 11576643225259664128]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-parquet-2aa9a2f0cd73da4f/dep-lib-polars_parquet", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}