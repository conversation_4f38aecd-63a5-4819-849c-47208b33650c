{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 5133476436119379086, "deps": [[1213098572879462490, "json5_rs", false, 17346540371806436402], [1965680986145237447, "yaml_rust2", false, 10964967803083572600], [2244620803250265856, "ron", false, 16230423752741839491], [6502365400774175331, "nom", false, 9838198991039023848], [6517602928339163454, "path<PERSON><PERSON>", false, 7412244137823425331], [9689903380558560274, "serde", false, 12364537043182369665], [11946729385090170470, "async_trait", false, 171698127956791669], [13475460906694513802, "convert_case", false, 15624893366313643957], [14618892375165583068, "ini", false, 7006159866891330634], [15367738274754116744, "serde_json", false, 14576692903234035802], [15609422047640926750, "toml", false, 8662626390987604837]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-b450deb08330537d/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}