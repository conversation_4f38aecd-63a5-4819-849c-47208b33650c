{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"business\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"diagonal_concat\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"interpolate\", \"interpolate_by\", \"ipc\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"new_streaming\", \"nightly\", \"object\", \"offset_by\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-json\", \"polars-pipe\", \"polars-stream\", \"polars-time\", \"propagate_nans\", \"pyo3\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"test\", \"test_all\", \"timezones\", \"tokio\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 3520973805660483946, "profile": 8276155916380437441, "path": 5635034491628424403, "deps": [[2909307575601997, "polars_mem_engine", false, 10668290466841889773], [455332427680959199, "polars_ops", false, 1302879595262727754], [966925859616469517, "ahash", false, 16917607473362308882], [1737279643867088440, "polars_utils", false, 10916294720058699258], [1742749750841112243, "polars_expr", false, 6496956469981231700], [3419428956812390430, "smartstring", false, 1808345005597031298], [3722963349756955755, "once_cell", false, 3950140573650441576], [6240045547040120597, "polars_io", false, 8825316889302016023], [7896293946984509699, "bitflags", false, 5647318406756679861], [8698907819379376942, "build_script_build", false, 739631064350536350], [9690350829298438995, "polars_time", false, 13189940873260876407], [10697383615564341592, "rayon", false, 3113060211295609744], [14517035561158255785, "arrow", false, 15400189539991997372], [15932120279885307830, "memchr", false, 1281753996029912424], [16620239925640633684, "polars_core", false, 16846759873613872156], [17120771218538166117, "polars_plan", false, 14294339964705373746], [17155886227862585100, "glob", false, 11740963246867438783]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-lazy-b7f78be85b4568d1/dep-lib-polars_lazy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}