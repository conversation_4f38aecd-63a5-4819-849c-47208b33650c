{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"business\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"diagonal_concat\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"interpolate\", \"interpolate_by\", \"ipc\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"new_streaming\", \"nightly\", \"object\", \"offset_by\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-json\", \"polars-pipe\", \"polars-stream\", \"polars-time\", \"propagate_nans\", \"pyo3\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"test\", \"test_all\", \"timezones\", \"tokio\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 3520973805660483946, "profile": 5347358027863023418, "path": 5635034491628424403, "deps": [[2909307575601997, "polars_mem_engine", false, 14840919256811966353], [455332427680959199, "polars_ops", false, 13564820156936230461], [966925859616469517, "ahash", false, 15005525890638504195], [1737279643867088440, "polars_utils", false, 17516090028869116942], [1742749750841112243, "polars_expr", false, 15936397555394684847], [3419428956812390430, "smartstring", false, 12329604167311017704], [3722963349756955755, "once_cell", false, 9250329688931680279], [6240045547040120597, "polars_io", false, 10037827378252496034], [7896293946984509699, "bitflags", false, 4199894223454998670], [8698907819379376942, "build_script_build", false, 739631064350536350], [9690350829298438995, "polars_time", false, 16547472187235890366], [10697383615564341592, "rayon", false, 17666425492764063832], [14517035561158255785, "arrow", false, 4036699857447306546], [15932120279885307830, "memchr", false, 15504980094282092926], [16620239925640633684, "polars_core", false, 561396548417138493], [17120771218538166117, "polars_plan", false, 9396491959397106890], [17155886227862585100, "glob", false, 6373622843244747377]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-lazy-bb0e75372f9b24a7/dep-lib-polars_lazy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}