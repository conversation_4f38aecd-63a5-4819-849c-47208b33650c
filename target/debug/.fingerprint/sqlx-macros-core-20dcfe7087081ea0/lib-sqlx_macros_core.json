{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"sqlx-postgres\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 12556255233731516675, "deps": [[530211389790465181, "hex", false, 5054411887327788366], [996810380461694889, "sqlx_core", false, 11397637492538707985], [1441306149310335789, "tempfile", false, 6189056105975361370], [2713742371683562785, "syn", false, 14538650839888728606], [3060637413840920116, "proc_macro2", false, 9926504638400894714], [3150220818285335163, "url", false, 3990778450837123107], [3405707034081185165, "dotenvy", false, 3440354807194926672], [3722963349756955755, "once_cell", false, 5712462848953866967], [8045585743974080694, "heck", false, 8715977571542734295], [9689903380558560274, "serde", false, 18102743518911559075], [9857275760291862238, "sha2", false, 13255892527583536382], [12170264697963848012, "either", false, 7038415179184165291], [12393800526703971956, "tokio", false, 3022971809643638826], [15367738274754116744, "serde_json", false, 12229143484653177481], [15634168271133386882, "sqlx_postgres", false, 1659965772412810738], [17990358020177143287, "quote", false, 4307053779554051676]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-20dcfe7087081ea0/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}