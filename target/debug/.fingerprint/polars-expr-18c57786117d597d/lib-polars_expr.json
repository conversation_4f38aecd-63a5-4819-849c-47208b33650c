{"rustc": 15497389221046826682, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"approx_unique\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"is_between\", \"is_in\", \"nightly\", \"panic_on_schema\", \"parquet\", \"polars-json\", \"polars-time\", \"propagate_nans\", \"round_series\", \"streaming\", \"temporal\"]", "target": 183342038448338162, "profile": 5347358027863023418, "path": 15641520773498189381, "deps": [[455332427680959199, "polars_ops", false, 13564820156936230461], [966925859616469517, "ahash", false, 15005525890638504195], [1737279643867088440, "polars_utils", false, 17516090028869116942], [3419428956812390430, "smartstring", false, 12329604167311017704], [3722963349756955755, "once_cell", false, 9250329688931680279], [6240045547040120597, "polars_io", false, 10037827378252496034], [7896293946984509699, "bitflags", false, 4199894223454998670], [9690350829298438995, "polars_time", false, 16547472187235890366], [10697383615564341592, "rayon", false, 17666425492764063832], [14517035561158255785, "arrow", false, 4036699857447306546], [16620239925640633684, "polars_core", false, 561396548417138493], [17120771218538166117, "polars_plan", false, 9396491959397106890]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-expr-18c57786117d597d/dep-lib-polars_expr", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}