{"$message_type":"diagnostic","message":"only traits defined in the current crate can be implemented for types defined outside of the crate","code":{"code":"E0117","explanation":"Only traits defined in the current crate can be implemented for arbitrary types.\n\nErroneous code example:\n\n```compile_fail,E0117\nimpl Drop for u32 {}\n```\n\nThis error indicates a violation of one of Rust's orphan rules for trait\nimplementations. The rule prohibits any implementation of a foreign trait (a\ntrait defined in another crate) where\n\n - the type that is implementing the trait is foreign\n - all of the parameters being passed to the trait (if there are any) are also\n   foreign.\n\nTo avoid this kind of error, ensure that at least one local type is referenced\nby the `impl`:\n\n```\npub struct Foo; // you define your type in your crate\n\nimpl Drop for Foo { // and you can implement the trait on it!\n    // code of trait implementation here\n#   fn drop(&mut self) { }\n}\n\nimpl From<Foo> for i32 { // or you use a type from your crate as\n                         // a type parameter\n    fn from(i: Foo) -> i32 {\n        0\n    }\n}\n```\n\nAlternatively, define a trait locally and implement that instead:\n\n```\ntrait Bar {\n    fn get(&self) -> usize;\n}\n\nimpl Bar for u32 {\n    fn get(&self) -> usize { 0 }\n}\n```\n\nFor information on the design of the orphan rules, see [RFC 1023].\n\n[RFC 1023]: https://github.com/rust-lang/rfcs/blob/master/text/1023-rebalancing-coherence.md\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/src/lib.rs","byte_start":1117,"byte_end":1132,"line_start":34,"line_end":34,"column_start":43,"column_end":58,"is_primary":false,"text":[{"text":"impl From<polars::error::PolarsError> for Web3GamingError {","highlight_start":43,"highlight_end":58}],"label":"`Web3GamingError` is not defined in the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/analytics-engine/src/lib.rs","byte_start":1080,"byte_end":1112,"line_start":34,"line_end":34,"column_start":6,"column_end":38,"is_primary":false,"text":[{"text":"impl From<polars::error::PolarsError> for Web3GamingError {","highlight_start":6,"highlight_end":38}],"label":"`PolarsError` is not defined in the current crate","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/analytics-engine/src/lib.rs","byte_start":1075,"byte_end":1132,"line_start":34,"line_end":34,"column_start":1,"column_end":58,"is_primary":true,"text":[{"text":"impl From<polars::error::PolarsError> for Web3GamingError {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"impl doesn't have any local type before any uncovered type parameters","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for more information see https://doc.rust-lang.org/reference/items/implementations.html#orphan-rules","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"define and implement a trait or new type instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0117]\u001b[0m\u001b[0m\u001b[1m: only traits defined in the current crate can be implemented for types defined outside of the crate\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/src/lib.rs:34:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl From<polars::error::PolarsError> for Web3GamingError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`Web3GamingError` is not defined in the current crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`PolarsError` is not defined in the current crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: impl doesn't have any local type before any uncovered type parameters\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information see https://doc.rust-lang.org/reference/items/implementations.html#orphan-rules\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: define and implement a trait or new type instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `is_between` found for enum `Expr` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":7946,"byte_end":7975,"line_start":256,"line_end":256,"column_start":17,"column_end":46,"is_primary":false,"text":[{"text":"                col(\"timestamp\").dt().date().is_between(","highlight_start":17,"highlight_end":46}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":7975,"byte_end":7985,"line_start":256,"line_end":256,"column_start":46,"column_end":56,"is_primary":true,"text":[{"text":"                col(\"timestamp\").dt().date().is_between(","highlight_start":46,"highlight_end":56}],"label":"method not found in `Expr`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `is_between` found for enum `Expr` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/src/polars_engine.rs:256:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                col(\"timestamp\").dt().date().is_between(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Expr`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `with_order_descending` found for struct `polars::prelude::SortOptions` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":10621,"byte_end":10642,"line_start":316,"line_end":316,"column_start":58,"column_end":79,"is_primary":true,"text":[{"text":"            .sort(\"total_volume\", SortOptions::default().with_order_descending())","highlight_start":58,"highlight_end":79}],"label":"method not found in `SortOptions`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `with_order_descending` found for struct `polars::prelude::SortOptions` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/src/polars_engine.rs:316:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m316\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .sort(\"total_volume\", SortOptions::default().with_order_descending())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `SortOptions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `users`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":12490,"byte_end":12495,"line_start":360,"line_end":360,"column_start":70,"column_end":75,"is_primary":true,"text":[{"text":"    async fn calculate_user_metrics(&self, transactions: &LazyFrame, users: &LazyFrame) -> Result<UserMetrics, Web3GamingError> {","highlight_start":70,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":12490,"byte_end":12495,"line_start":360,"line_end":360,"column_start":70,"column_end":75,"is_primary":true,"text":[{"text":"    async fn calculate_user_metrics(&self, transactions: &LazyFrame, users: &LazyFrame) -> Result<UserMetrics, Web3GamingError> {","highlight_start":70,"highlight_end":75}],"label":null,"suggested_replacement":"_users","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `users`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/src/polars_engine.rs:360:70\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn calculate_user_metrics(&self, transactions: &LazyFrame, users: &LazyFrame) -> Result<UserMetrics, Web3GamingError> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_users`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `with_order_descending` found for struct `polars::prelude::SortOptions` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":14886,"byte_end":14907,"line_start":414,"line_end":414,"column_start":58,"column_end":79,"is_primary":true,"text":[{"text":"            .sort(\"total_volume\", SortOptions::default().with_order_descending())","highlight_start":58,"highlight_end":79}],"label":"method not found in `SortOptions`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `with_order_descending` found for struct `polars::prelude::SortOptions` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/src/polars_engine.rs:414:58\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .sort(\"total_volume\", SortOptions::default().with_order_descending())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `SortOptions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `unwrap_or` found for type `f64` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/src/polars_engine.rs","byte_start":19189,"byte_end":19198,"line_start":520,"line_end":520,"column_start":73,"column_end":82,"is_primary":true,"text":[{"text":"                daily_averages.insert(\"volume\".to_string(), volume_mean.unwrap_or(0.0));","highlight_start":73,"highlight_end":82}],"label":"method not found in `f64`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `unwrap_or` found for type `f64` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/src/polars_engine.rs:520:73\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m520\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                daily_averages.insert(\"volume\".to_string(), volume_mean.unwrap_or(0.0));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `f64`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 5 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 5 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0117, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0117, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0117`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0117`.\u001b[0m\n"}
