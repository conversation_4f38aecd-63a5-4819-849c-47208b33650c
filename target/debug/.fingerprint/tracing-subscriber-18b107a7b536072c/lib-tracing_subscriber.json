{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 3207006686210061414, "deps": [[1009387600818341822, "matchers", false, 5136775758409388199], [1017461770342116999, "sharded_slab", false, 4043473071524776660], [1359731229228270592, "thread_local", false, 18081950493437231178], [3424551429995674438, "tracing_core", false, 6740285323527535059], [3666196340704888985, "smallvec", false, 5038540345199146830], [3722963349756955755, "once_cell", false, 9250329688931680279], [6981130804689348050, "tracing_serde", false, 13273453222036606415], [8606274917505247608, "tracing", false, 9217579458450593339], [8614575489689151157, "nu_ansi_term", false, 2332003482830201961], [9451456094439810778, "regex", false, 15308202986402225626], [9689903380558560274, "serde", false, 1652587176100301358], [10806489435541507125, "tracing_log", false, 17905360314113482667], [15367738274754116744, "serde_json", false, 273018631490583565]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-18b107a7b536072c/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}