{"rustc": 15497389221046826682, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"temporal\"]", "declared_features": "[\"chrono-tz\", \"default\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"fmt\", \"month_end\", \"month_start\", \"offset_by\", \"rolling_window\", \"rolling_window_by\", \"serde\", \"temporal\", \"test\", \"timezones\"]", "target": 12151923383344587982, "profile": 8276155916380437441, "path": 1825361290898964554, "deps": [[455332427680959199, "polars_ops", false, 1302879595262727754], [1737279643867088440, "polars_utils", false, 10916294720058699258], [3419428956812390430, "smartstring", false, 1808345005597031298], [3722963349756955755, "once_cell", false, 3950140573650441576], [6511429716036861196, "bytemuck", false, 1672816269885430399], [9451456094439810778, "regex", false, 878718001504540918], [9897246384292347999, "chrono", false, 9268331838963614633], [11243224374683126366, "polars_error", false, 3406120419987733069], [11610044098279991895, "now", false, 12241815130427566364], [14517035561158255785, "arrow", false, 15400189539991997372], [16620239925640633684, "polars_core", false, 16846759873613872156], [17106256174509013259, "atoi", false, 3410358760634991023]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-time-28c4e47969700ff7/dep-lib-polars_time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}