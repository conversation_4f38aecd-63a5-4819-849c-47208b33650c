{"rustc": 15497389221046826682, "features": "[\"default\", \"sqlx\"]", "declared_features": "[\"default\", \"sqlx\"]", "target": 6655291900496003818, "profile": 6675295047989516842, "path": 15376344883518451600, "deps": [[3405707034081185165, "dotenvy", false, 15055560603815943521], [4495526598637097934, "parking_lot", false, 13905995194819255023], [7244058819997729774, "reqwest", false, 4927074092397745424], [8008191657135824715, "thiserror", false, 16255346684290760672], [8319709847752024821, "uuid", false, 4527770802189718398], [8606274917505247608, "tracing", false, 8312589604480262083], [9689903380558560274, "serde", false, 8709775620457530939], [9897246384292347999, "chrono", false, 187977539902157986], [10632374999838431203, "sqlx", false, 10013759312956197784], [12382237672615274180, "config", false, 9526895265529854336], [12393800526703971956, "tokio", false, 7212051573472437663], [13625485746686963219, "anyhow", false, 9563385383867021308], [15367738274754116744, "serde_json", false, 11853957577942380444], [16230660778393187092, "tracing_subscriber", false, 9959697181140329749]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/shared-utils-4d0047375dea05ac/dep-lib-shared_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}