{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"nightly\"]", "target": 15010007883002352389, "profile": 5347358027863023418, "path": 4081117487231009876, "deps": [[2828590642173593838, "cfg_if", false, 16964169135620860843], [3016941897346161952, "downcast", false, 14516330836865526947], [7886665781035375288, "fragile", false, 6708234805024086591], [9056619860232277314, "mockall_derive", false, 17809356257407456130], [12516616738327129663, "predicates_tree", false, 15870833951644591520], [15863765456528386755, "predicates", false, 10097372989775039285], [17917672826516349275, "lazy_static", false, 10720984073450295554]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall-995873baefc36917/dep-lib-mockall", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}