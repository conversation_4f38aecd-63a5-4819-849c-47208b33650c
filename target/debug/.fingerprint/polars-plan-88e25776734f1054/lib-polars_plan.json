{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"date_offset\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"fmt\", \"fused\", \"future\", \"futures\", \"hive_partitions\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 11334077754402081686, "profile": 8276155916380437441, "path": 10055420711929588378, "deps": [[40386456601120721, "percent_encoding", false, 6466076441445340421], [963741888112789140, "polars_ops", false, 4409815587008152112], [966925859616469517, "ahash", false, 16917607473362308882], [3419428956812390430, "smartstring", false, 1808345005597031298], [3495776386425160289, "build_script_build", false, 3307577898614359876], [3722963349756955755, "once_cell", false, 3950140573650441576], [4446769819467865508, "polars_core", false, 13009331656829711496], [4978756888369265998, "polars_utils", false, 17455359528766704616], [6511429716036861196, "bytemuck", false, 1672816269885430399], [6565397770530258860, "polars_io", false, 12040835443488657384], [6930142607113040362, "arrow", false, 4935114248834157665], [10697383615564341592, "rayon", false, 3113060211295609744], [11411182522717811070, "polars_time", false, 14559942557527191954], [15285011755229917804, "strum_macros", false, 16992986870659122812]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-88e25776734f1054/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}