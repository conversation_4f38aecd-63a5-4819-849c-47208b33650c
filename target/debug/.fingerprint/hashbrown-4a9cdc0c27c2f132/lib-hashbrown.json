{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 3033921117576893, "path": 8979737143335350578, "deps": [[966925859616469517, "ahash", false, 9899951950833326203], [9150530836556604396, "allocator_api2", false, 1735539611776099621]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-4a9cdc0c27c2f132/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}