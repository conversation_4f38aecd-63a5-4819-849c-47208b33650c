{"rustc": 15497389221046826682, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17878142613068009629, "path": 5986205144625639485, "deps": [[5103565458935487, "futures_io", false, 17419322992876663693], [1615478164327904835, "pin_utils", false, 1892149188148494095], [1906322745568073236, "pin_project_lite", false, 10122047953940016221], [5451793922601807560, "slab", false, 5110865232667408813], [7013762810557009322, "futures_sink", false, 16520291564696363974], [7620660491849607393, "futures_core", false, 7206792896077970838], [15932120279885307830, "memchr", false, 15504980094282092926], [16240732885093539806, "futures_task", false, 5079910806899992724]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-4e917a7dd55b195d/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}