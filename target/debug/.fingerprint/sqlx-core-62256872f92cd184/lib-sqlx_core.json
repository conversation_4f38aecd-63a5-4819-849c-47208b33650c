{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 8276155916380437441, "path": 17041482572124269129, "deps": [[5103565458935487, "futures_io", false, 11539599110773873554], [40386456601120721, "percent_encoding", false, 6466076441445340421], [530211389790465181, "hex", false, 17249695804984892782], [788558663644978524, "crossbeam_queue", false, 6219881300390149912], [966925859616469517, "ahash", false, 16917607473362308882], [1162433738665300155, "crc", false, 17196176563137951756], [1464803193346256239, "event_listener", false, 7216279214019494949], [1811549171721445101, "futures_channel", false, 2809131337950437782], [3150220818285335163, "url", false, 17066871592244810921], [3405817021026194662, "hashlink", false, 11918331806160024175], [3646857438214563691, "futures_intrusive", false, 5823946699125818428], [3666196340704888985, "smallvec", false, 17911532867709753888], [3712811570531045576, "byteorder", false, 1688090057903121988], [3722963349756955755, "once_cell", false, 3950140573650441576], [5986029879202738730, "log", false, 540550529245414849], [6493259146304816786, "indexmap", false, 300888238413034454], [7620660491849607393, "futures_core", false, 12134165656439499170], [8008191657135824715, "thiserror", false, 10481705715828789710], [8319709847752024821, "uuid", false, 10796248903766481440], [8606274917505247608, "tracing", false, 15675275868982337999], [9689903380558560274, "serde", false, 12364537043182369665], [9857275760291862238, "sha2", false, 17720400707509889489], [9897246384292347999, "chrono", false, 9268331838963614633], [10629569228670356391, "futures_util", false, 10689215794273529932], [10862088793507253106, "sqlformat", false, 17664338405992518109], [11295624341523567602, "rustls", false, 3591407004760412052], [12170264697963848012, "either", false, 17390189878655533154], [12393800526703971956, "tokio", false, 18218432810481653859], [15367738274754116744, "serde_json", false, 14576692903234035802], [15932120279885307830, "memchr", false, 1281753996029912424], [16066129441945555748, "bytes", false, 1974571504047377931], [16311359161338405624, "rustls_pemfile", false, 16202291361653268027], [16973251432615581304, "tokio_stream", false, 9975049469501018076], [17106256174509013259, "atoi", false, 3410358760634991023], [17605717126308396068, "paste", false, 10476848073614706140], [17652733826348741533, "webpki_roots", false, 1519446600155896425]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-62256872f92cd184/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}