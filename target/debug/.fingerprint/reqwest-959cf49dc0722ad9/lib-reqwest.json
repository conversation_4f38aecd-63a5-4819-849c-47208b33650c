{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 9776420804298061517, "deps": [[40386456601120721, "percent_encoding", false, 8191989742803952923], [95042085696191081, "ipnet", false, 1768588591202163297], [264090853244900308, "sync_wrapper", false, 7576646051607697378], [784494742817713399, "tower_service", false, 976358663622631765], [1044435446100926395, "hyper_rustls", false, 4844475622587231532], [1906322745568073236, "pin_project_lite", false, 10122047953940016221], [3150220818285335163, "url", false, 8873553841871137308], [3722963349756955755, "once_cell", false, 9250329688931680279], [4405182208873388884, "http", false, 4310921186945951448], [5986029879202738730, "log", false, 14372202331545107381], [7414427314941361239, "hyper", false, 12785438001991005346], [7620660491849607393, "futures_core", false, 7206792896077970838], [8915503303801890683, "http_body", false, 9760610989407348513], [9689903380558560274, "serde", false, 1652587176100301358], [10229185211513642314, "mime", false, 10898770798336713718], [10629569228670356391, "futures_util", false, 9585563021757688674], [11107720164717273507, "system_configuration", false, 8740665324401407764], [11295624341523567602, "rustls", false, 10641254860032273575], [12186126227181294540, "tokio_native_tls", false, 3481314498934677353], [12367227501898450486, "hyper_tls", false, 17764319486394438012], [12393800526703971956, "tokio", false, 5772717272019752093], [13763625454224483636, "h2", false, 4302210664450353341], [14564311161534545801, "encoding_rs", false, 12780744117765599861], [15367738274754116744, "serde_json", false, 273018631490583565], [16066129441945555748, "bytes", false, 16337691178649358567], [16311359161338405624, "rustls_pemfile", false, 15881483248844770546], [16542808166767769916, "serde_urlencoded", false, 7421997403636843716], [16622232390123975175, "tokio_rustls", false, 3986301223245552544], [16785601910559813697, "native_tls_crate", false, 17812459481541300465], [17652733826348741533, "webpki_roots", false, 17853647655319698975], [18066890886671768183, "base64", false, 2078352242049108517]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-959cf49dc0722ad9/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}