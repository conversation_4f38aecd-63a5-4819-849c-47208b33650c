{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 7511545393986131494, "deps": [[996810380461694889, "sqlx_core", false, 4398727108226818607], [2713742371683562785, "syn", false, 14538650839888728606], [3060637413840920116, "proc_macro2", false, 9926504638400894714], [15733334431800349573, "sqlx_macros_core", false, 3222253765723022956], [17990358020177143287, "quote", false, 4307053779554051676]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-5405b796d90f7d24/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}