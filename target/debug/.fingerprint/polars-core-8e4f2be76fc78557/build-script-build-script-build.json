{"rustc": 15497389221046826682, "features": "[\"algorithm_group_by\", \"chrono\", \"comfy-table\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"fmt\", \"lazy\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"rows\", \"temporal\", \"zip_with\"]", "declared_features": "[\"algorithm_group_by\", \"arrow-array\", \"arrow_rs\", \"avx512\", \"bigidx\", \"checked_arithmetic\", \"chrono\", \"chrono-tz\", \"comfy-table\", \"dataframe_arithmetic\", \"default\", \"describe\", \"diagonal_concat\", \"docs\", \"docs-selection\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"fmt\", \"fmt_no_tty\", \"group_by_list\", \"is_first_distinct\", \"is_last_distinct\", \"lazy\", \"ndarray\", \"nightly\", \"object\", \"partition_by\", \"performant\", \"product\", \"python\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"reinterpret\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"rows\", \"serde\", \"serde-lazy\", \"serde_json\", \"simd\", \"strings\", \"take_opt_iter\", \"temporal\", \"timezones\", \"unique_counts\", \"zip_with\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 857911798438104786, "deps": [[5398981501050481332, "version_check", false, 11737096578573216674]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-core-8e4f2be76fc78557/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}