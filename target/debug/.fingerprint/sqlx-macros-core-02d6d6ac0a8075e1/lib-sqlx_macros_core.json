{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"sqlx-postgres\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 12556255233731516675, "deps": [[530211389790465181, "hex", false, 8406936689739084727], [996810380461694889, "sqlx_core", false, 17542921213874058643], [1441306149310335789, "tempfile", false, 12151143712187029531], [2713742371683562785, "syn", false, 8444015993336014493], [3060637413840920116, "proc_macro2", false, 9926504638400894714], [3150220818285335163, "url", false, 18437266996205268115], [3405707034081185165, "dotenvy", false, 15055560603815943521], [3722963349756955755, "once_cell", false, 9250329688931680279], [8045585743974080694, "heck", false, 1316230015390171336], [9689903380558560274, "serde", false, 1652587176100301358], [9857275760291862238, "sha2", false, 7145092560904833011], [12170264697963848012, "either", false, 12794257162807497770], [12393800526703971956, "tokio", false, 10295785685701397780], [15367738274754116744, "serde_json", false, 273018631490583565], [15634168271133386882, "sqlx_postgres", false, 1199175711415253256], [17990358020177143287, "quote", false, 4307053779554051676]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-02d6d6ac0a8075e1/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}