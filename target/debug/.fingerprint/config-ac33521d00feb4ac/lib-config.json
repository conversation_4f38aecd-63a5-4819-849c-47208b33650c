{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 5133476436119379086, "deps": [[1213098572879462490, "json5_rs", false, 2037231130890087090], [1965680986145237447, "yaml_rust2", false, 810810572842498976], [2244620803250265856, "ron", false, 3696770430147574570], [6502365400774175331, "nom", false, 13889024271665151550], [6517602928339163454, "path<PERSON><PERSON>", false, 9463922204363178483], [9689903380558560274, "serde", false, 1652587176100301358], [11946729385090170470, "async_trait", false, 171698127956791669], [13475460906694513802, "convert_case", false, 12511240149004723287], [14618892375165583068, "ini", false, 15833204954858569240], [15367738274754116744, "serde_json", false, 273018631490583565], [15609422047640926750, "toml", false, 8876324374818470917]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-ac33521d00feb4ac/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}