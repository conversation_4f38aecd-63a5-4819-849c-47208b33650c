{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"nightly_derive\"]", "target": 16548621151911234621, "profile": 3033921117576893, "path": 11729565353640587755, "deps": [[2828590642173593838, "cfg_if", false, 16964169135620860843], [3060637413840920116, "proc_macro2", false, 9926504638400894714], [4974441333307933176, "syn", false, 10019496655894177572], [17990358020177143287, "quote", false, 4307053779554051676]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall_derive-941db0182b688928/dep-lib-mockall_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}