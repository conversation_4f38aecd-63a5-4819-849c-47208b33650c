{"rustc": 15497389221046826682, "features": "[\"csv\", \"default\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-slim\", \"fmt\", \"lazy\", \"polars-io\", \"polars-lazy\", \"polars-ops\", \"polars-time\", \"temporal\", \"zip_with\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"avro\", \"avx512\", \"aws\", \"azure\", \"bench\", \"bigidx\", \"binary_encoding\", \"business\", \"checked_arithmetic\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"dataframe_arithmetic\", \"decompress\", \"decompress-fast\", \"default\", \"describe\", \"diagonal_concat\", \"diff\", \"docs\", \"docs-selection\", \"dot_diagram\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-slim\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"find_many\", \"fmt\", \"fmt_no_tty\", \"fused\", \"gcp\", \"hist\", \"http\", \"interpolate\", \"interpolate_by\", \"ipc\", \"ipc_streaming\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"lazy\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"ndarray\", \"nightly\", \"object\", \"offset_by\", \"parquet\", \"partition_by\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-io\", \"polars-lazy\", \"polars-ops\", \"polars-plan\", \"polars-sql\", \"polars-time\", \"product\", \"propagate_nans\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"rows\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde-lazy\", \"sign\", \"simd\", \"sql\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"take_opt_iter\", \"temporal\", \"test\", \"timezones\", \"to_dummies\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\", \"zip_with\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 12968442319718407458, "deps": [[5398981501050481332, "version_check", false, 11737096578573216674]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-a8fe6298fdf28f12/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}