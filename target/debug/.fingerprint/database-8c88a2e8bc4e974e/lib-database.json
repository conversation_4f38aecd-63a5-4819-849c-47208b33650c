{"rustc": 15497389221046826682, "features": "[\"default\", \"migrations\", \"postgres\", \"redis\"]", "declared_features": "[\"default\", \"migrations\", \"postgres\", \"redis\", \"testing\"]", "target": 13745987626157619292, "profile": 6675295047989516842, "path": 11469606969491719041, "deps": [[4311858244109339105, "redis", false, 7522055455220812768], [6079318424673677659, "sha256", false, 18005041679004492464], [7144144552800689541, "shared_utils", false, 8726929469695153423], [8008191657135824715, "thiserror", false, 16255346684290760672], [8319709847752024821, "uuid", false, 4527770802189718398], [8606274917505247608, "tracing", false, 8312589604480262083], [9689903380558560274, "serde", false, 8709775620457530939], [9897246384292347999, "chrono", false, 187977539902157986], [10632374999838431203, "sqlx", false, 3749974635487732298], [12393800526703971956, "tokio", false, 12508040185417025102], [13625485746686963219, "anyhow", false, 9563385383867021308], [15367738274754116744, "serde_json", false, 11853957577942380444], [17772299992546037086, "flate2", false, 4529196862055027675]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/database-8c88a2e8bc4e974e/dep-lib-database", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}