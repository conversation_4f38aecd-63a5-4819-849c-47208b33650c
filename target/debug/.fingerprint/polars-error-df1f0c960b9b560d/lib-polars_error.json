{"rustc": 15497389221046826682, "features": "[\"regex\"]", "declared_features": "[\"arrow-format\", \"avro-schema\", \"object_store\", \"python\", \"regex\"]", "target": 9265665750937118341, "profile": 8276155916380437441, "path": 9264798006857306513, "deps": [[8008191657135824715, "thiserror", false, 10481705715828789710], [8067010153367330186, "simdutf8", false, 4455228269351234043], [9451456094439810778, "regex", false, 878718001504540918]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-error-df1f0c960b9b560d/dep-lib-polars_error", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}