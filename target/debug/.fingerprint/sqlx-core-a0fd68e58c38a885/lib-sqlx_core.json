{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 3033921117576893, "path": 17041482572124269129, "deps": [[5103565458935487, "futures_io", false, 5731065063706288197], [40386456601120721, "percent_encoding", false, 11948866364106209857], [530211389790465181, "hex", false, 5054411887327788366], [788558663644978524, "crossbeam_queue", false, 12596040589546121098], [966925859616469517, "ahash", false, 9899951950833326203], [1162433738665300155, "crc", false, 16371995889012638562], [1464803193346256239, "event_listener", false, 1356731389384423266], [1811549171721445101, "futures_channel", false, 11124731099116953245], [3150220818285335163, "url", false, 3990778450837123107], [3405817021026194662, "hashlink", false, 11769276191265843684], [3646857438214563691, "futures_intrusive", false, 2476455420074245861], [3666196340704888985, "smallvec", false, 16993971398721692458], [3712811570531045576, "byteorder", false, 16513619057662210953], [3722963349756955755, "once_cell", false, 5712462848953866967], [5986029879202738730, "log", false, 17068279859887383371], [6493259146304816786, "indexmap", false, 15063066048614982711], [7620660491849607393, "futures_core", false, 11374427026840189081], [8008191657135824715, "thiserror", false, 9991571435991383383], [8319709847752024821, "uuid", false, 1950093996735897188], [8606274917505247608, "tracing", false, 11360654268941461429], [9689903380558560274, "serde", false, 18102743518911559075], [9857275760291862238, "sha2", false, 13255892527583536382], [9897246384292347999, "chrono", false, 1930532979728190509], [10629569228670356391, "futures_util", false, 87877988966063952], [10862088793507253106, "sqlformat", false, 7836746235995858068], [11295624341523567602, "rustls", false, 8691585576684441741], [12170264697963848012, "either", false, 7038415179184165291], [12393800526703971956, "tokio", false, 3022971809643638826], [15367738274754116744, "serde_json", false, 12229143484653177481], [15932120279885307830, "memchr", false, 13649664433696150093], [16066129441945555748, "bytes", false, 13856125665968228880], [16311359161338405624, "rustls_pemfile", false, 8681513952372120467], [16973251432615581304, "tokio_stream", false, 14735992238328081288], [17106256174509013259, "atoi", false, 6149272054393252448], [17605717126308396068, "paste", false, 10476848073614706140], [17652733826348741533, "webpki_roots", false, 1677600062459778917]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-a0fd68e58c38a885/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}