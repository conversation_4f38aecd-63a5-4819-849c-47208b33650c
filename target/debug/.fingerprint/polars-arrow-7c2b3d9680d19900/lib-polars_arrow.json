{"rustc": 15497389221046826682, "features": "[\"arrow-format\", \"atoi_simd\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_hash\", \"compute_take\", \"compute_temporal\", \"fast-float\", \"io_ipc\", \"itoa\", \"multiversion\", \"ryu\", \"strength_reduce\", \"temporal\"]", "declared_features": "[\"arrow-array\", \"arrow-buffer\", \"arrow-data\", \"arrow-format\", \"arrow-schema\", \"arrow_rs\", \"async-stream\", \"atoi\", \"atoi_simd\", \"avro-schema\", \"bigidx\", \"chrono-tz\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_hash\", \"compute_take\", \"compute_temporal\", \"default\", \"dtype-array\", \"dtype-decimal\", \"fast-float\", \"full\", \"futures\", \"hex\", \"indexmap\", \"io_avro\", \"io_avro_async\", \"io_avro_compression\", \"io_flight\", \"io_ipc\", \"io_ipc_compression\", \"io_ipc_read_async\", \"io_ipc_write_async\", \"itoa\", \"itoap\", \"lz4\", \"multiversion\", \"nightly\", \"performant\", \"regex\", \"regex-syntax\", \"ryu\", \"serde\", \"simd\", \"strength_reduce\", \"strings\", \"temporal\", \"timezones\", \"zstd\"]", "target": 5005847566128143416, "profile": 5347358027863023418, "path": 12901887289821863680, "deps": [[966925859616469517, "ahash", false, 15005525890638504195], [1216309103264968120, "ryu", false, 16908231344506514424], [1737279643867088440, "polars_utils", false, 17516090028869116942], [3726277658779405417, "strength_reduce", false, 7135736817488027042], [5157631553186200874, "num_traits", false, 13181684718241081284], [6096440479827326274, "multiversion", false, 12394996258919527999], [6511429716036861196, "bytemuck", false, 12249584660493990846], [7695812897323945497, "itoa", false, 375099722027685691], [7898571650830454567, "ethnum", false, 2664396464211697769], [8067010153367330186, "simdutf8", false, 1465554792876887024], [9122563107207267705, "dyn_clone", false, 527187183770278137], [9235208004366183979, "streaming_iterator", false, 11288303874831930356], [9897246384292347999, "chrono", false, 5861715046674372433], [9938377527623146791, "foreign_vec", false, 11201478178279962265], [11243224374683126366, "polars_error", false, 14690282539794651279], [12170264697963848012, "either", false, 12794257162807497770], [13015077130101160787, "arrow_format", false, 15643341967203564984], [13018563866916002725, "hashbrown", false, 6906628132907056355], [14517035561158255785, "build_script_build", false, 4542995532385092749], [16583050384810566368, "atoi_simd", false, 6933576889736204754], [17433304786843909481, "fast_float", false, 11290510952535507637]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-arrow-7c2b3d9680d19900/dep-lib-polars_arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}