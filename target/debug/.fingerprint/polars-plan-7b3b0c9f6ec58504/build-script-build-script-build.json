{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"business\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"find_many\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"hive_partitions\", \"interpolate\", \"interpolate_by\", \"ipc\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"nightly\", \"object\", \"offset_by\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-json\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 12047122258441253362, "deps": [[5398981501050481332, "version_check", false, 11737096578573216674]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-7b3b0c9f6ec58504/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}