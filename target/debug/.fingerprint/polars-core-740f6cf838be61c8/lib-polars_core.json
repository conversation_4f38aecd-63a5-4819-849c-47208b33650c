{"rustc": 15497389221046826682, "features": "[\"algorithm_group_by\", \"chrono\", \"comfy-table\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"fmt\", \"lazy\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"rows\", \"temporal\", \"zip_with\"]", "declared_features": "[\"algorithm_group_by\", \"arrow-array\", \"arrow_rs\", \"avx512\", \"bigidx\", \"checked_arithmetic\", \"chrono\", \"chrono-tz\", \"comfy-table\", \"dataframe_arithmetic\", \"default\", \"describe\", \"diagonal_concat\", \"docs\", \"docs-selection\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"fmt\", \"fmt_no_tty\", \"group_by_list\", \"is_first_distinct\", \"is_last_distinct\", \"lazy\", \"ndarray\", \"nightly\", \"object\", \"partition_by\", \"performant\", \"product\", \"python\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"reinterpret\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"rows\", \"serde\", \"serde-lazy\", \"serde_json\", \"simd\", \"strings\", \"take_opt_iter\", \"temporal\", \"timezones\", \"unique_counts\", \"zip_with\"]", "target": 10155373262246874405, "profile": 5347358027863023418, "path": 15153756106393721159, "deps": [[966925859616469517, "ahash", false, 15005525890638504195], [1737279643867088440, "polars_utils", false, 17516090028869116942], [1804806304303030865, "xxhash_rust", false, 1114833529594814109], [3419428956812390430, "smartstring", false, 12329604167311017704], [3722963349756955755, "once_cell", false, 9250329688931680279], [5157631553186200874, "num_traits", false, 13181684718241081284], [6493259146304816786, "indexmap", false, 11434938633746865478], [6511429716036861196, "bytemuck", false, 12249584660493990846], [7896293946984509699, "bitflags", false, 4199894223454998670], [8008191657135824715, "thiserror", false, 11345645093265826359], [9196727883430091646, "rand_distr", false, 8108188953816615185], [9451456094439810778, "regex", false, 14461852244860413511], [9897246384292347999, "chrono", false, 5861715046674372433], [10170320114020213262, "comfy_table", false, 15888691937724977670], [10697383615564341592, "rayon", false, 17666425492764063832], [11243224374683126366, "polars_error", false, 14690282539794651279], [12170264697963848012, "either", false, 12794257162807497770], [13018563866916002725, "hashbrown", false, 6906628132907056355], [13208667028893622512, "rand", false, 11393838192381618100], [14517035561158255785, "arrow", false, 4036699857447306546], [16620239925640633684, "build_script_build", false, 9067973481255503943], [16810752119801614479, "polars_row", false, 16199636346340509831], [17929261977799616229, "polars_compute", false, 11576643225259664128]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-core-740f6cf838be61c8/dep-lib-polars_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}