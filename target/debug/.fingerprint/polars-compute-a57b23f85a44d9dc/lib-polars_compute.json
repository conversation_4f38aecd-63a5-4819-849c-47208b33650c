{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"dtype-array\", \"nightly\", \"simd\"]", "target": 1692980664120516943, "profile": 5347358027863023418, "path": 7027488262057746237, "deps": [[1737279643867088440, "polars_utils", false, 17516090028869116942], [3726277658779405417, "strength_reduce", false, 7135736817488027042], [5157631553186200874, "num_traits", false, 13181684718241081284], [6511429716036861196, "bytemuck", false, 12249584660493990846], [11243224374683126366, "polars_error", false, 14690282539794651279], [12170264697963848012, "either", false, 12794257162807497770], [14517035561158255785, "arrow", false, 4036699857447306546], [17929261977799616229, "build_script_build", false, 14146476947936763773]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-compute-a57b23f85a44d9dc/dep-lib-polars_compute", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}