{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"asof_join\", \"async\", \"cloud\", \"cse\", \"csv\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"futures\", \"ipc\", \"json\", \"object\", \"parquet\", \"polars-json\", \"polars-time\", \"pyo3\", \"python\", \"temporal\", \"tokio\"]", "target": 8948906024185173859, "profile": 5347358027863023418, "path": 11931826724097608032, "deps": [[455332427680959199, "polars_ops", false, 13564820156936230461], [1737279643867088440, "polars_utils", false, 17516090028869116942], [1742749750841112243, "polars_expr", false, 15936397555394684847], [6240045547040120597, "polars_io", false, 10037827378252496034], [9690350829298438995, "polars_time", false, 16547472187235890366], [10697383615564341592, "rayon", false, 17666425492764063832], [11243224374683126366, "polars_error", false, 14690282539794651279], [14517035561158255785, "arrow", false, 4036699857447306546], [16620239925640633684, "polars_core", false, 561396548417138493], [17120771218538166117, "polars_plan", false, 9396491959397106890]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-mem-engine-ed0a66afa4010413/dep-lib-polars_mem_engine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}