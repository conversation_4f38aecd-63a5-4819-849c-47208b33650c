{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 13376416658102300949, "profile": 6675295047989516842, "path": 2818422534699728420, "deps": [[3601586811267292532, "tower", false, 3709666778466463379], [4311858244109339105, "redis", false, 5266244582649582458], [4891297352905791595, "axum", false, 2678561873289989347], [7144144552800689541, "shared_utils", false, 9873345715889877715], [8606274917505247608, "tracing", false, 9217579458450593339], [9689903380558560274, "serde", false, 1652587176100301358], [9897246384292347999, "chrono", false, 9850446743430126220], [10632374999838431203, "sqlx", false, 2936572315867471907], [12393800526703971956, "tokio", false, 13711536135186096817], [13374073496340178589, "database", false, 14266968652970165220], [13625485746686963219, "anyhow", false, 9563385383867021308], [14435908599267459652, "tower_http", false, 8267405941050921369], [15367738274754116744, "serde_json", false, 273018631490583565], [16230660778393187092, "tracing_subscriber", false, 7991284307095066955]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/standalone-api-49070062e659739a/dep-bin-standalone-api", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}