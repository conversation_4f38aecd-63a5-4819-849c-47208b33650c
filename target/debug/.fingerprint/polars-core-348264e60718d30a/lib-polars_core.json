{"rustc": 15497389221046826682, "features": "[\"algorithm_group_by\", \"chrono\", \"comfy-table\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"fmt\", \"lazy\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"rows\", \"temporal\", \"zip_with\"]", "declared_features": "[\"algorithm_group_by\", \"arrow-array\", \"arrow_rs\", \"avx512\", \"bigidx\", \"checked_arithmetic\", \"chrono\", \"chrono-tz\", \"comfy-table\", \"dataframe_arithmetic\", \"default\", \"describe\", \"diagonal_concat\", \"docs\", \"docs-selection\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"fmt\", \"fmt_no_tty\", \"group_by_list\", \"is_first_distinct\", \"is_last_distinct\", \"lazy\", \"ndarray\", \"nightly\", \"object\", \"partition_by\", \"performant\", \"product\", \"python\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"reinterpret\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"rows\", \"serde\", \"serde-lazy\", \"serde_json\", \"simd\", \"strings\", \"take_opt_iter\", \"temporal\", \"timezones\", \"unique_counts\", \"zip_with\"]", "target": 10155373262246874405, "profile": 8276155916380437441, "path": 15153756106393721159, "deps": [[966925859616469517, "ahash", false, 16917607473362308882], [1737279643867088440, "polars_utils", false, 10916294720058699258], [1804806304303030865, "xxhash_rust", false, 6198922579910407897], [3419428956812390430, "smartstring", false, 1808345005597031298], [3722963349756955755, "once_cell", false, 3950140573650441576], [5157631553186200874, "num_traits", false, 3983708198095814638], [6493259146304816786, "indexmap", false, 300888238413034454], [6511429716036861196, "bytemuck", false, 1672816269885430399], [7896293946984509699, "bitflags", false, 5647318406756679861], [8008191657135824715, "thiserror", false, 10481705715828789710], [9196727883430091646, "rand_distr", false, 6933103812344975397], [9451456094439810778, "regex", false, 878718001504540918], [9897246384292347999, "chrono", false, 9268331838963614633], [10170320114020213262, "comfy_table", false, 145993296018411543], [10697383615564341592, "rayon", false, 3113060211295609744], [11243224374683126366, "polars_error", false, 3406120419987733069], [12170264697963848012, "either", false, 17390189878655533154], [13018563866916002725, "hashbrown", false, 3299620658094797291], [13208667028893622512, "rand", false, 18188154742885992259], [14517035561158255785, "arrow", false, 15400189539991997372], [16620239925640633684, "build_script_build", false, 9067973481255503943], [16810752119801614479, "polars_row", false, 9006600554432799891], [17929261977799616229, "polars_compute", false, 3439563883014117661]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-core-348264e60718d30a/dep-lib-polars_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}