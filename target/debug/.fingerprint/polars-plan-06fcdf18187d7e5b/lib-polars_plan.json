{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"business\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"find_many\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"hive_partitions\", \"interpolate\", \"interpolate_by\", \"ipc\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"nightly\", \"object\", \"offset_by\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-json\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 8885459555497909574, "profile": 8276155916380437441, "path": 18442313352138674292, "deps": [[40386456601120721, "percent_encoding", false, 6466076441445340421], [455332427680959199, "polars_ops", false, 1302879595262727754], [519668612969056153, "strum_macros", false, 11158857306431403195], [966925859616469517, "ahash", false, 16917607473362308882], [1737279643867088440, "polars_utils", false, 10916294720058699258], [3419428956812390430, "smartstring", false, 1808345005597031298], [3722963349756955755, "once_cell", false, 3950140573650441576], [6240045547040120597, "polars_io", false, 8825316889302016023], [6511429716036861196, "bytemuck", false, 1672816269885430399], [9690350829298438995, "polars_time", false, 13189940873260876407], [10697383615564341592, "rayon", false, 3113060211295609744], [11487583573386935581, "recursive", false, 16497165912627291091], [12170264697963848012, "either", false, 17390189878655533154], [13018563866916002725, "hashbrown", false, 3299620658094797291], [14517035561158255785, "arrow", false, 15400189539991997372], [16620239925640633684, "polars_core", false, 16846759873613872156], [17120771218538166117, "build_script_build", false, 3273057865188144679]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-06fcdf18187d7e5b/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}