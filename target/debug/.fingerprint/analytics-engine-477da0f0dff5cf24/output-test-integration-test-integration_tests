{"$message_type":"diagnostic","message":"expected `,`, found `.`","code":null,"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12014,"byte_end":12015,"line_start":355,"line_end":355,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":18,"highlight_end":19}],"label":"expected `,`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: expected `,`, found `.`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:355:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m355\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"=\" .repeat(60));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `,`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"argument never used","code":null,"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12015,"byte_end":12025,"line_start":355,"line_end":355,"column_start":19,"column_end":29,"is_primary":true,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":19,"highlight_end":29}],"label":"argument never used","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12010,"byte_end":12013,"line_start":355,"line_end":355,"column_start":14,"column_end":17,"is_primary":false,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":14,"highlight_end":17}],"label":"formatting specifier missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: argument never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:355:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m355\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"=\" .repeat(60));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9margument never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mformatting specifier missing\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected `,`, found `.`","code":null,"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12477,"byte_end":12478,"line_start":368,"line_end":368,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":18,"highlight_end":19}],"label":"expected `,`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: expected `,`, found `.`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:368:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m368\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"=\" .repeat(60));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `,`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"argument never used","code":null,"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12478,"byte_end":12488,"line_start":368,"line_end":368,"column_start":19,"column_end":29,"is_primary":true,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":19,"highlight_end":29}],"label":"argument never used","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12473,"byte_end":12476,"line_start":368,"line_end":368,"column_start":14,"column_end":17,"is_primary":false,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":14,"highlight_end":17}],"label":"formatting specifier missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: argument never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:368:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m368\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"=\" .repeat(60));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9margument never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mformatting specifier missing\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find function `repeat` in this scope","code":{"code":"E0425","explanation":"An unresolved name was used.\n\nErroneous code examples:\n\n```compile_fail,E0425\nsomething_that_doesnt_exist::foo;\n// error: unresolved name `something_that_doesnt_exist::foo`\n\n// or:\n\ntrait Foo {\n    fn bar() {\n        Self; // error: unresolved name `Self`\n    }\n}\n\n// or:\n\nlet x = unknown_variable;  // error: unresolved name `unknown_variable`\n```\n\nPlease verify that the name wasn't misspelled and ensure that the\nidentifier being referred to is valid for the given situation. Example:\n\n```\nenum something_that_does_exist {\n    Foo,\n}\n```\n\nOr:\n\n```\nmod something_that_does_exist {\n    pub static foo : i32 = 0i32;\n}\n\nsomething_that_does_exist::foo; // ok!\n```\n\nOr:\n\n```\nlet unknown_variable = 12u32;\nlet x = unknown_variable; // ok!\n```\n\nIf the item is not defined in the current module, it must be imported using a\n`use` statement, like so:\n\n```\n# mod foo { pub fn bar() {} }\n# fn main() {\nuse foo::bar;\nbar();\n# }\n```\n\nIf the item you are importing is not defined in some super-module of the\ncurrent module, then it must also be declared as public (e.g., `pub fn`).\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12015,"byte_end":12021,"line_start":355,"line_end":355,"column_start":19,"column_end":25,"is_primary":true,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":19,"highlight_end":25}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these functions","code":null,"level":"help","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::array::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::io::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::iter::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use core::array::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use core::iter::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use polars::prelude::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use rayon::iter::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use tokio::io::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0425]\u001b[0m\u001b[0m\u001b[1m: cannot find function `repeat` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:355:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m355\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"=\" .repeat(60));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these functions\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::array::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::io::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::iter::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use core::array::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m      and 4 other candidates\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find function `repeat` in this scope","code":{"code":"E0425","explanation":"An unresolved name was used.\n\nErroneous code examples:\n\n```compile_fail,E0425\nsomething_that_doesnt_exist::foo;\n// error: unresolved name `something_that_doesnt_exist::foo`\n\n// or:\n\ntrait Foo {\n    fn bar() {\n        Self; // error: unresolved name `Self`\n    }\n}\n\n// or:\n\nlet x = unknown_variable;  // error: unresolved name `unknown_variable`\n```\n\nPlease verify that the name wasn't misspelled and ensure that the\nidentifier being referred to is valid for the given situation. Example:\n\n```\nenum something_that_does_exist {\n    Foo,\n}\n```\n\nOr:\n\n```\nmod something_that_does_exist {\n    pub static foo : i32 = 0i32;\n}\n\nsomething_that_does_exist::foo; // ok!\n```\n\nOr:\n\n```\nlet unknown_variable = 12u32;\nlet x = unknown_variable; // ok!\n```\n\nIf the item is not defined in the current module, it must be imported using a\n`use` statement, like so:\n\n```\n# mod foo { pub fn bar() {} }\n# fn main() {\nuse foo::bar;\nbar();\n# }\n```\n\nIf the item you are importing is not defined in some super-module of the\ncurrent module, then it must also be declared as public (e.g., `pub fn`).\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":12478,"byte_end":12484,"line_start":368,"line_end":368,"column_start":19,"column_end":25,"is_primary":true,"text":[{"text":"    println!(\"=\" .repeat(60));","highlight_start":19,"highlight_end":25}],"label":"not found in this scope","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing one of these functions","code":null,"level":"help","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::array::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::io::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use std::iter::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use core::array::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use core::iter::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use polars::prelude::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use rayon::iter::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":283,"byte_end":283,"line_start":8,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use analytics_engine::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use tokio::io::repeat;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0425]\u001b[0m\u001b[0m\u001b[1m: cannot find function `repeat` in this scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:368:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m368\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    println!(\"=\" .repeat(60));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in this scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these functions\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::array::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::io::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use std::iter::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use core::array::repeat;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m      and 4 other candidates\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 0 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1419,"byte_end":1421,"line_start":51,"line_end":51,"column_start":44,"column_end":46,"is_primary":false,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":44,"highlight_end":46}],"label":"argument #1 of type `AnalyticsConfig` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1393,"byte_end":1419,"line_start":51,"line_end":51,"column_start":18,"column_end":44,"is_primary":true,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":18,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Volumes/Booter/growlerHome/KBScraperProject/RewriteInProgress/web3GameScraperRewrite/crates/analytics-engine/src/polars_engine.rs","byte_start":7327,"byte_end":7330,"line_start":237,"line_end":237,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(config: AnalyticsConfig) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1419,"byte_end":1421,"line_start":51,"line_end":51,"column_start":44,"column_end":46,"is_primary":true,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":44,"highlight_end":46}],"label":null,"suggested_replacement":"(/* AnalyticsConfig */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 0 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:51:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let engine = PolarsAnalyticsEngine::new().await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12margument #1 of type `AnalyticsConfig` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Volumes/Booter/growlerHome/KBScraperProject/RewriteInProgress/web3GameScraperRewrite/crates/analytics-engine/src/polars_engine.rs:237:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(config: AnalyticsConfig) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let engine = PolarsAnalyticsEngine::new(\u001b[0m\u001b[0m\u001b[38;5;10m/* AnalyticsConfig */\u001b[0m\u001b[0m).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[38;5;10m+++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`analytics_engine::PolarsAnalyticsEngine` is not a future","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1422,"byte_end":1427,"line_start":51,"line_end":51,"column_start":47,"column_end":52,"is_primary":true,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":47,"highlight_end":52}],"label":"`analytics_engine::PolarsAnalyticsEngine` is not a future","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1422,"byte_end":1427,"line_start":51,"line_end":51,"column_start":47,"column_end":52,"is_primary":false,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":47,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1393,"byte_end":1421,"line_start":51,"line_end":51,"column_start":18,"column_end":46,"is_primary":false,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":18,"highlight_end":46}],"label":"this call returns `analytics_engine::PolarsAnalyticsEngine`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the trait `Future` is not implemented for `analytics_engine::PolarsAnalyticsEngine`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"analytics_engine::PolarsAnalyticsEngine must be a future or must implement `IntoFuture` to be awaited","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `analytics_engine::PolarsAnalyticsEngine` to implement `IntoFuture`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the `.await`","code":null,"level":"help","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":1421,"byte_end":1427,"line_start":51,"line_end":51,"column_start":46,"column_end":52,"is_primary":true,"text":[{"text":"    let engine = PolarsAnalyticsEngine::new().await?;","highlight_start":46,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: `analytics_engine::PolarsAnalyticsEngine` is not a future\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:51:47\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let engine = PolarsAnalyticsEngine::new().await?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`analytics_engine::PolarsAnalyticsEngine` is not a future\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the `.await`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis call returns `analytics_engine::PolarsAnalyticsEngine`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the trait `Future` is not implemented for `analytics_engine::PolarsAnalyticsEngine`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: analytics_engine::PolarsAnalyticsEngine must be a future or must implement `IntoFuture` to be awaited\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `analytics_engine::PolarsAnalyticsEngine` to implement `IntoFuture`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `analytics_engine::AnalyticsRequest` has no field named `algorithm_type`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":3454,"byte_end":3468,"line_start":108,"line_end":108,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"        algorithm_type: AlgorithmType::PlayerLifetimeValue,","highlight_start":9,"highlight_end":23}],"label":"`analytics_engine::AnalyticsRequest` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `query_type`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `analytics_engine::AnalyticsRequest` has no field named `algorithm_type`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:108:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        algorithm_type: AlgorithmType::PlayerLifetimeValue,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`analytics_engine::AnalyticsRequest` does not have this field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `query_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `analytics_engine::AnalyticsRequest` has no field named `input_data`","code":{"code":"E0560","explanation":"An unknown field was specified into a structure.\n\nErroneous code example:\n\n```compile_fail,E0560\nstruct Simba {\n    mother: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 };\n// error: structure `Simba` has no field named `father`\n```\n\nVerify you didn't misspell the field's name or that the field exists. Example:\n\n```\nstruct Simba {\n    mother: u32,\n    father: u32,\n}\n\nlet s = Simba { mother: 1, father: 0 }; // ok!\n```\n"},"level":"error","spans":[{"file_name":"crates/analytics-engine/tests/integration_tests.rs","byte_start":3514,"byte_end":3524,"line_start":109,"line_end":109,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"        input_data,","highlight_start":9,"highlight_end":19}],"label":"`analytics_engine::AnalyticsRequest` does not have this field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `query_type`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0560]\u001b[0m\u001b[0m\u001b[1m: struct `analytics_engine::AnalyticsRequest` has no field named `input_data`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/analytics-engine/tests/integration_tests.rs:109:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        input_data,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`analytics_engine::AnalyticsRequest` does not have this field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: available fields are: `query_type`\u001b[0m\n\n"}
