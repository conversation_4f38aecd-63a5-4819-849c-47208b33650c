{"rustc": 15497389221046826682, "features": "[\"alloc\", \"libc-extra-traits\", \"std\", \"stdio\", \"termios\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 660990570592641402, "path": 12472740811601050467, "deps": [[3430646239657634944, "build_script_build", false, 12592073672434812242], [4684437522915235464, "libc", false, 8613408108593689094], [7896293946984509699, "bitflags", false, 5647318406756679861], [8253628577145923712, "libc_errno", false, 6477407132166690169]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-ff69640866bf3eff/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}