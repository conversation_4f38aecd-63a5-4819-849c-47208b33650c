{"rustc": 15497389221046826682, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 8276155916380437441, "path": 3564174027221637386, "deps": [[1288403060204016458, "tokio_util", false, 13764178792200092252], [1906322745568073236, "pin_project_lite", false, 7773456665029720526], [7620660491849607393, "futures_core_03", false, 12134165656439499170], [12393800526703971956, "tokio_dep", false, 18218432810481653859], [15932120279885307830, "memchr", false, 1281753996029912424], [16066129441945555748, "bytes", false, 1974571504047377931]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/combine-bb1e397b211fd76a/dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}