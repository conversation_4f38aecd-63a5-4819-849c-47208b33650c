{"rustc": 15497389221046826682, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"asof_join\", \"async\", \"cloud\", \"cse\", \"csv\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"futures\", \"ipc\", \"json\", \"object\", \"parquet\", \"polars-json\", \"polars-time\", \"pyo3\", \"python\", \"temporal\", \"tokio\"]", "target": 8948906024185173859, "profile": 8276155916380437441, "path": 11931826724097608032, "deps": [[455332427680959199, "polars_ops", false, 1302879595262727754], [1737279643867088440, "polars_utils", false, 10916294720058699258], [1742749750841112243, "polars_expr", false, 6496956469981231700], [6240045547040120597, "polars_io", false, 8825316889302016023], [9690350829298438995, "polars_time", false, 13189940873260876407], [10697383615564341592, "rayon", false, 3113060211295609744], [11243224374683126366, "polars_error", false, 3406120419987733069], [14517035561158255785, "arrow", false, 15400189539991997372], [16620239925640633684, "polars_core", false, 16846759873613872156], [17120771218538166117, "polars_plan", false, 14294339964705373746]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-mem-engine-720fd64a6e873515/dep-lib-polars_mem_engine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}