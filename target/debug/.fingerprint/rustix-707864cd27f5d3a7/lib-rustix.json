{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2210360036522109082, "path": 8823220324589195036, "deps": [[4684437522915235464, "libc", false, 14380184061943402702], [7896293946984509699, "bitflags", false, 2845562401809256506], [8253628577145923712, "libc_errno", false, 1754927597114782688], [12053020504183902936, "build_script_build", false, 11815023492946364410]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-707864cd27f5d3a7/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}