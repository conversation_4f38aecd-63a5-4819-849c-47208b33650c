{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"rayon\", \"serde\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 5347358027863023418, "path": 8979737143335350578, "deps": [[966925859616469517, "ahash", false, 15005525890638504195], [9150530836556604396, "allocator_api2", false, 4961289459697848833], [9689903380558560274, "serde", false, 1652587176100301358], [10697383615564341592, "rayon", false, 17666425492764063832]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-0526e9b49c174196/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}