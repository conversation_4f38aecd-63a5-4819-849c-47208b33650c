{"rustc": 15497389221046826682, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"zip_with\"]", "declared_features": "[\"abs\", \"approx_unique\", \"asof_join\", \"base64\", \"big_idx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"convert_index\", \"cov\", \"cross_join\", \"cum_agg\", \"cutqcut\", \"diff\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fused\", \"group_by_list\", \"hash\", \"hex\", \"interpolate\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"jsonpath_lib\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"mode\", \"moment\", \"nightly\", \"object\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-json\", \"propagate_nans\", \"rand\", \"rand_distr\", \"random\", \"rank\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde_json\", \"simd\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"timezones\", \"to_dummies\", \"top_k\", \"unique_counts\", \"zip_with\"]", "target": 13897111239935570622, "profile": 8276155916380437441, "path": 9172020262429943000, "deps": [[963741888112789140, "build_script_build", false, 224083324530087542], [966925859616469517, "ahash", false, 16917607473362308882], [3419428956812390430, "smartstring", false, 1808345005597031298], [4446769819467865508, "polars_core", false, 13009331656829711496], [4978756888369265998, "polars_utils", false, 17455359528766704616], [5157631553186200874, "num_traits", false, 3983708198095814638], [6493259146304816786, "indexmap", false, 300888238413034454], [6511429716036861196, "bytemuck", false, 1672816269885430399], [6930142607113040362, "arrow", false, 4935114248834157665], [9451456094439810778, "regex", false, 878718001504540918], [10697383615564341592, "rayon", false, 3113060211295609744], [10766548186203220745, "polars_error", false, 4451831741403312465], [12084198568646017729, "arg<PERSON><PERSON>", false, 11692123043921610127], [12170264697963848012, "either", false, 17390189878655533154], [13018563866916002725, "hashbrown", false, 15295667022088157241], [15932120279885307830, "memchr", false, 1281753996029912424]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-ops-dad294a37dc98da5/dep-lib-polars_ops", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}