{"rustc": 15497389221046826682, "features": "[\"csv\", \"default\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-slim\", \"fmt\", \"lazy\", \"polars-io\", \"polars-lazy\", \"polars-ops\", \"polars-time\", \"temporal\", \"zip_with\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"async\", \"avro\", \"avx512\", \"aws\", \"azure\", \"bench\", \"bigidx\", \"binary_encoding\", \"business\", \"checked_arithmetic\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"dataframe_arithmetic\", \"decompress\", \"decompress-fast\", \"default\", \"describe\", \"diagonal_concat\", \"diff\", \"docs\", \"docs-selection\", \"dot_diagram\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-slim\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"find_many\", \"fmt\", \"fmt_no_tty\", \"fused\", \"gcp\", \"hist\", \"http\", \"interpolate\", \"interpolate_by\", \"ipc\", \"ipc_streaming\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"lazy\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"month_end\", \"month_start\", \"ndarray\", \"nightly\", \"object\", \"offset_by\", \"parquet\", \"partition_by\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-io\", \"polars-lazy\", \"polars-ops\", \"polars-plan\", \"polars-sql\", \"polars-time\", \"product\", \"propagate_nans\", \"random\", \"range\", \"rank\", \"regex\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"row_hash\", \"rows\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde-lazy\", \"sign\", \"simd\", \"sql\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"take_opt_iter\", \"temporal\", \"test\", \"timezones\", \"to_dummies\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\", \"zip_with\"]", "target": 12349568194006998978, "profile": 5347358027863023418, "path": 4862955734638892062, "deps": [[455332427680959199, "polars_ops", false, 13564820156936230461], [1737279643867088440, "polars_utils", false, 17516090028869116942], [4329015716636128466, "build_script_build", false, 10351438398118273377], [6240045547040120597, "polars_io", false, 10037827378252496034], [6253319913096395078, "polars_parquet", false, 16701018001673408405], [8698907819379376942, "polars_lazy", false, 11341578427141858813], [9690350829298438995, "polars_time", false, 16547472187235890366], [11243224374683126366, "polars_error", false, 14690282539794651279], [14517035561158255785, "arrow", false, 4036699857447306546], [16620239925640633684, "polars_core", false, 561396548417138493]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-fc9a1f083439b1ab/dep-lib-polars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}