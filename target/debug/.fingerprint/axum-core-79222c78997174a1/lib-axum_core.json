{"rustc": 15497389221046826682, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 5347358027863023418, "path": 1953081417165192972, "deps": [[784494742817713399, "tower_service", false, 976358663622631765], [1906322745568073236, "pin_project_lite", false, 10122047953940016221], [2517136641825875337, "sync_wrapper", false, 12215418833615401458], [7712452662827335977, "tower_layer", false, 5958092446026363953], [7858942147296547339, "rustversion", false, 6910462189244203060], [8606274917505247608, "tracing", false, 9217579458450593339], [9010263965687315507, "http", false, 15190774565033300876], [10229185211513642314, "mime", false, 10898770798336713718], [10629569228670356391, "futures_util", false, 5510586782975070624], [11946729385090170470, "async_trait", false, 171698127956791669], [14084095096285906100, "http_body", false, 3858752339072201350], [16066129441945555748, "bytes", false, 16337691178649358567], [16900715236047033623, "http_body_util", false, 18085953101452228190]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-79222c78997174a1/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}