{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 3207006686210061414, "deps": [[1009387600818341822, "matchers", false, 13115835336023800623], [1017461770342116999, "sharded_slab", false, 197732468135314267], [1359731229228270592, "thread_local", false, 4271432432837479832], [3424551429995674438, "tracing_core", false, 17464811346243893659], [3666196340704888985, "smallvec", false, 17911532867709753888], [3722963349756955755, "once_cell", false, 3950140573650441576], [6981130804689348050, "tracing_serde", false, 801687172873320267], [8606274917505247608, "tracing", false, 15675275868982337999], [8614575489689151157, "nu_ansi_term", false, 17487421387051990741], [9451456094439810778, "regex", false, 878718001504540918], [9689903380558560274, "serde", false, 12364537043182369665], [10806489435541507125, "tracing_log", false, 3103271864384602339], [15367738274754116744, "serde_json", false, 14576692903234035802]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-a53291842509931b/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}