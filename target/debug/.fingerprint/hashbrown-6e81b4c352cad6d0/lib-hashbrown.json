{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"rayon\", \"serde\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 8276155916380437441, "path": 8979737143335350578, "deps": [[966925859616469517, "ahash", false, 16917607473362308882], [9150530836556604396, "allocator_api2", false, 8152849764597444491], [9689903380558560274, "serde", false, 12364537043182369665], [10697383615564341592, "rayon", false, 3113060211295609744]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-6e81b4c352cad6d0/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}