{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"dtype-array\", \"nightly\", \"simd\"]", "target": 1692980664120516943, "profile": 8276155916380437441, "path": 7027488262057746237, "deps": [[1737279643867088440, "polars_utils", false, 10916294720058699258], [3726277658779405417, "strength_reduce", false, 6961032737405812873], [5157631553186200874, "num_traits", false, 3983708198095814638], [6511429716036861196, "bytemuck", false, 1672816269885430399], [11243224374683126366, "polars_error", false, 3406120419987733069], [12170264697963848012, "either", false, 17390189878655533154], [14517035561158255785, "arrow", false, 15400189539991997372], [17929261977799616229, "build_script_build", false, 14146476947936763773]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-compute-0c77ccd86c56c44e/dep-lib-polars_compute", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}