{"rustc": 15497389221046826682, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"temporal\"]", "declared_features": "[\"approx_unique\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"is_between\", \"is_in\", \"nightly\", \"panic_on_schema\", \"parquet\", \"polars-json\", \"polars-time\", \"propagate_nans\", \"round_series\", \"streaming\", \"temporal\"]", "target": 183342038448338162, "profile": 8276155916380437441, "path": 15641520773498189381, "deps": [[455332427680959199, "polars_ops", false, 1302879595262727754], [966925859616469517, "ahash", false, 16917607473362308882], [1737279643867088440, "polars_utils", false, 10916294720058699258], [3419428956812390430, "smartstring", false, 1808345005597031298], [3722963349756955755, "once_cell", false, 3950140573650441576], [6240045547040120597, "polars_io", false, 8825316889302016023], [7896293946984509699, "bitflags", false, 5647318406756679861], [9690350829298438995, "polars_time", false, 13189940873260876407], [10697383615564341592, "rayon", false, 3113060211295609744], [14517035561158255785, "arrow", false, 15400189539991997372], [16620239925640633684, "polars_core", false, 16846759873613872156], [17120771218538166117, "polars_plan", false, 14294339964705373746]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-expr-316905897235e7cd/dep-lib-polars_expr", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}