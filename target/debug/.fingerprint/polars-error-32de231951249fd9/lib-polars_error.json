{"rustc": 15497389221046826682, "features": "[\"arrow-format\", \"regex\"]", "declared_features": "[\"arrow-format\", \"avro-schema\", \"object_store\", \"python\", \"regex\"]", "target": 1257440485424937050, "profile": 8276155916380437441, "path": 14856783424868757205, "deps": [[8008191657135824715, "thiserror", false, 10481705715828789710], [8067010153367330186, "simdutf8", false, 4455228269351234043], [9451456094439810778, "regex", false, 878718001504540918], [13015077130101160787, "arrow_format", false, 8858603762620801249]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-error-32de231951249fd9/dep-lib-polars_error", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}