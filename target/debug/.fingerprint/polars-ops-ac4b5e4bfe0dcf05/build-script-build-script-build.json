{"rustc": 15497389221046826682, "features": "[\"chunked_ids\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\"]", "declared_features": "[\"abs\", \"aho-corasick\", \"approx_unique\", \"array_any_all\", \"array_count\", \"array_to_struct\", \"asof_join\", \"base64\", \"big_idx\", \"binary_encoding\", \"business\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"cov\", \"cross_join\", \"cum_agg\", \"cutqcut\", \"diff\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"ewma\", \"ewma_by\", \"extract_groups\", \"extract_jsonpath\", \"find_many\", \"fused\", \"gather\", \"hash\", \"hex\", \"hist\", \"interpolate\", \"interpolate_by\", \"is_between\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"jsonpath_lib\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"mode\", \"moment\", \"nightly\", \"object\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-json\", \"propagate_nans\", \"rand\", \"rand_distr\", \"random\", \"rank\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"rolling_window_by\", \"round_series\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde_json\", \"simd\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"timezones\", \"to_dummies\", \"top_k\", \"unicode-reverse\", \"unique_counts\"]", "target": 5408242616063297496, "profile": 3033921117576893, "path": 2078203677314252209, "deps": [[5398981501050481332, "version_check", false, 11737096578573216674]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-ops-ac4b5e4bfe0dcf05/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}