{"rustc": 15497389221046826682, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"temporal\"]", "declared_features": "[\"chrono-tz\", \"default\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"fmt\", \"month_end\", \"month_start\", \"offset_by\", \"rolling_window\", \"rolling_window_by\", \"serde\", \"temporal\", \"test\", \"timezones\"]", "target": 12151923383344587982, "profile": 5347358027863023418, "path": 1825361290898964554, "deps": [[455332427680959199, "polars_ops", false, 13564820156936230461], [1737279643867088440, "polars_utils", false, 17516090028869116942], [3419428956812390430, "smartstring", false, 12329604167311017704], [3722963349756955755, "once_cell", false, 9250329688931680279], [6511429716036861196, "bytemuck", false, 12249584660493990846], [9451456094439810778, "regex", false, 14461852244860413511], [9897246384292347999, "chrono", false, 5861715046674372433], [11243224374683126366, "polars_error", false, 14690282539794651279], [11610044098279991895, "now", false, 7545046540247550507], [14517035561158255785, "arrow", false, 4036699857447306546], [16620239925640633684, "polars_core", false, 561396548417138493], [17106256174509013259, "atoi", false, 5529014118790921187]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-time-4f48292494fdce5b/dep-lib-polars_time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}