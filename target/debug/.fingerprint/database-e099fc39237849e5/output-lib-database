{"$message_type":"diagnostic","message":"unused imports: `ErrorArchetype` and `SemanticError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/connection.rs","byte_start":555,"byte_end":568,"line_start":13,"line_end":13,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"use shared_utils::{Web3Gaming<PERSON>rror, Semantic<PERSON>rror, ErrorArchetype};","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/database/src/connection.rs","byte_start":570,"byte_end":584,"line_start":13,"line_end":13,"column_start":52,"column_end":66,"is_primary":true,"text":[{"text":"use shared_utils::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rchetype};","highlight_start":52,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/database/src/connection.rs","byte_start":553,"byte_end":584,"line_start":13,"line_end":13,"column_start":35,"column_end":66,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":35,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/connection.rs","byte_start":537,"byte_end":538,"line_start":13,"line_end":13,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/connection.rs","byte_start":584,"byte_end":585,"line_start":13,"line_end":13,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ErrorArchetype` and `SemanticError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/connection.rs:13:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/queries.rs","byte_start":622,"byte_end":632,"line_start":15,"line_end":15,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/database/src/queries.rs","byte_start":618,"byte_end":634,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/queries.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ErrorArchetype` and `SemanticError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/cache.rs","byte_start":517,"byte_end":530,"line_start":13,"line_end":13,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/database/src/cache.rs","byte_start":532,"byte_end":546,"line_start":13,"line_end":13,"column_start":52,"column_end":66,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":52,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/database/src/cache.rs","byte_start":515,"byte_end":546,"line_start":13,"line_end":13,"column_start":35,"column_end":66,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":35,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/cache.rs","byte_start":499,"byte_end":500,"line_start":13,"line_end":13,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/cache.rs","byte_start":546,"byte_end":547,"line_start":13,"line_end":13,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ErrorArchetype` and `SemanticError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/cache.rs:13:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/cache.rs","byte_start":636,"byte_end":655,"line_start":17,"line_end":17,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/database/src/cache.rs","byte_start":632,"byte_end":657,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/cache.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DateTime` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/analytics.rs","byte_start":616,"byte_end":624,"line_start":15,"line_end":15,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDate};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/database/src/analytics.rs","byte_start":626,"byte_end":629,"line_start":15,"line_end":15,"column_start":24,"column_end":27,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDate};","highlight_start":24,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/database/src/analytics.rs","byte_start":616,"byte_end":631,"line_start":15,"line_end":15,"column_start":14,"column_end":29,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDate};","highlight_start":14,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/analytics.rs","byte_start":615,"byte_end":616,"line_start":15,"line_end":15,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDate};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/analytics.rs","byte_start":640,"byte_end":641,"line_start":15,"line_end":15,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, NaiveDate};","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `DateTime` and `Utc`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/analytics.rs:15:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc, NaiveDate};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Postgres` and `QueryBuilder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/analytics.rs","byte_start":691,"byte_end":703,"line_start":17,"line_end":17,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/database/src/analytics.rs","byte_start":705,"byte_end":713,"line_start":17,"line_end":17,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/database/src/analytics.rs","byte_start":691,"byte_end":715,"line_start":17,"line_end":17,"column_start":12,"column_end":36,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":12,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/analytics.rs","byte_start":690,"byte_end":691,"line_start":17,"line_end":17,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/analytics.rs","byte_start":718,"byte_end":719,"line_start":17,"line_end":17,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Postgres` and `QueryBuilder`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/analytics.rs:17:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sqlx::{QueryBuilder, Postgres, Row};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `queries::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/lib.rs","byte_start":686,"byte_end":696,"line_start":22,"line_end":22,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"pub use queries::*;","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates/database/src/lib.rs","byte_start":678,"byte_end":698,"line_start":22,"line_end":23,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use queries::*;","highlight_start":1,"highlight_end":20},{"text":"pub use cache::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `queries::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/lib.rs:22:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use queries::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ErrorArchetype` and `SemanticError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/lib.rs","byte_start":775,"byte_end":788,"line_start":26,"line_end":26,"column_start":37,"column_end":50,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":37,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/database/src/lib.rs","byte_start":790,"byte_end":804,"line_start":26,"line_end":26,"column_start":52,"column_end":66,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":52,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"crates/database/src/lib.rs","byte_start":773,"byte_end":804,"line_start":26,"line_end":26,"column_start":35,"column_end":66,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":35,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/lib.rs","byte_start":757,"byte_end":758,"line_start":26,"line_end":26,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"crates/database/src/lib.rs","byte_start":804,"byte_end":805,"line_start":26,"line_end":26,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"use shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ErrorArchetype` and `SemanticError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/lib.rs:26:37\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse shared_utils::{Web3GamingError, SemanticError, ErrorArchetype};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `chain`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/queries.rs","byte_start":8418,"byte_end":8423,"line_start":206,"line_end":206,"column_start":37,"column_end":42,"is_primary":true,"text":[{"text":"        let query = if let Some(ref chain) = chain {","highlight_start":37,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"crates/database/src/queries.rs","byte_start":8418,"byte_end":8423,"line_start":206,"line_end":206,"column_start":37,"column_end":42,"is_primary":true,"text":[{"text":"        let query = if let Some(ref chain) = chain {","highlight_start":37,"highlight_end":42}],"label":null,"suggested_replacement":"_chain","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `chain`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/queries.rs:206:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let query = if let Some(ref chain) = chain {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_chain`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Row`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/lib.rs","byte_start":826,"byte_end":829,"line_start":27,"line_end":27,"column_start":20,"column_end":23,"is_primary":true,"text":[{"text":"use sqlx::{PgPool, Row};","highlight_start":20,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Row`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/lib.rs:27:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sqlx::{PgPool, Row};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `query_cache` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"crates/database/src/lib.rs","byte_start":1129,"byte_end":1144,"line_start":37,"line_end":37,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct DatabaseManager {","highlight_start":12,"highlight_end":27}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates/database/src/lib.rs","byte_start":1371,"byte_end":1382,"line_start":45,"line_end":45,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    query_cache: Arc<RwLock<std::collections::HashMap<String, String>>>,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`DatabaseManager` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `query_cache` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mcrates/database/src/lib.rs:45:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct DatabaseManager {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    query_cache: Arc<RwLock<std::collections::HashMap<String, String>>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `DatabaseManager` has a derived impl for the trait `Clone`, but this is intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"11 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 11 warnings emitted\u001b[0m\n\n"}
