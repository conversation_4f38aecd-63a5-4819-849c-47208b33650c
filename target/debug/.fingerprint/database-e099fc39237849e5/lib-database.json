{"rustc": 15497389221046826682, "features": "[\"default\", \"migrations\", \"postgres\", \"redis\"]", "declared_features": "[\"default\", \"migrations\", \"postgres\", \"redis\", \"testing\"]", "target": 13745987626157619292, "profile": 6675295047989516842, "path": 11469606969491719041, "deps": [[4311858244109339105, "redis", false, 5266244582649582458], [6079318424673677659, "sha256", false, 12411407268226760664], [7144144552800689541, "shared_utils", false, 9873345715889877715], [8008191657135824715, "thiserror", false, 11345645093265826359], [8319709847752024821, "uuid", false, 17869130911446549586], [8606274917505247608, "tracing", false, 9217579458450593339], [9689903380558560274, "serde", false, 1652587176100301358], [9897246384292347999, "chrono", false, 9850446743430126220], [10632374999838431203, "sqlx", false, 2936572315867471907], [12393800526703971956, "tokio", false, 13711536135186096817], [13625485746686963219, "anyhow", false, 9563385383867021308], [15367738274754116744, "serde_json", false, 273018631490583565], [17772299992546037086, "flate2", false, 4529196862055027675]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/database-e099fc39237849e5/dep-lib-database", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}