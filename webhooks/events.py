"""
Webhook event models and types.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum


class WebhookEventType(str, Enum):
    """Types of webhook events."""
    
    # Blockchain events
    BLOCK_MINED = "block_mined"
    TRANSACTION_CONFIRMED = "transaction_confirmed"
    CONTRACT_DEPLOYED = "contract_deployed"
    
    # Gaming events
    GAMING_CONTRACT_DISCOVERED = "gaming_contract_discovered"
    GAMING_TRANSACTION = "gaming_transaction"
    
    # NFT events
    NFT_MINTED = "nft_minted"
    NFT_TRANSFERRED = "nft_transferred"
    NFT_LISTED = "nft_listed"
    NFT_SOLD = "nft_sold"
    
    # Token events
    TOKEN_TRANSFER = "token_transfer"
    LARGE_TOKEN_TRANSFER = "large_token_transfer"
    WHALE_ACTIVITY = "whale_activity"
    
    # Contract events
    CONTRACT_INTERACTION = "contract_interaction"
    NEW_GAMING_CONTRACT = "new_gaming_contract"


@dataclass
class WebhookEvent:
    """Base webhook event."""
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: WebhookEventType = WebhookEventType.TRANSACTION_CONFIRMED
    timestamp: datetime = field(default_factory=datetime.utcnow)
    blockchain: str = "ethereum"
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "blockchain": self.blockchain,
            "data": self.data,
            "metadata": self.metadata
        }


@dataclass
class BlockchainWebhookEvent(WebhookEvent):
    """Blockchain-specific webhook event."""
    
    block_number: Optional[int] = None
    transaction_hash: Optional[str] = None
    contract_address: Optional[str] = None
    gas_used: Optional[int] = None
    gas_price: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with blockchain-specific fields."""
        base_dict = super().to_dict()
        base_dict.update({
            "block_number": self.block_number,
            "transaction_hash": self.transaction_hash,
            "contract_address": self.contract_address,
            "gas_used": self.gas_used,
            "gas_price": self.gas_price
        })
        return base_dict


@dataclass
class GamingWebhookEvent(BlockchainWebhookEvent):
    """Gaming-specific webhook event."""
    
    gaming_project: Optional[str] = None
    game_name: Optional[str] = None
    player_address: Optional[str] = None
    gaming_action: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with gaming-specific fields."""
        base_dict = super().to_dict()
        base_dict.update({
            "gaming_project": self.gaming_project,
            "game_name": self.game_name,
            "player_address": self.player_address,
            "gaming_action": self.gaming_action
        })
        return base_dict


@dataclass
class NFTWebhookEvent(GamingWebhookEvent):
    """NFT-specific webhook event."""
    
    token_id: Optional[str] = None
    collection_name: Optional[str] = None
    from_address: Optional[str] = None
    to_address: Optional[str] = None
    price: Optional[float] = None
    currency: Optional[str] = None
    marketplace: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with NFT-specific fields."""
        base_dict = super().to_dict()
        base_dict.update({
            "token_id": self.token_id,
            "collection_name": self.collection_name,
            "from_address": self.from_address,
            "to_address": self.to_address,
            "price": self.price,
            "currency": self.currency,
            "marketplace": self.marketplace
        })
        return base_dict


@dataclass
class TokenWebhookEvent(GamingWebhookEvent):
    """Token-specific webhook event."""
    
    token_symbol: Optional[str] = None
    token_address: Optional[str] = None
    amount: Optional[float] = None
    amount_usd: Optional[float] = None
    from_address: Optional[str] = None
    to_address: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with token-specific fields."""
        base_dict = super().to_dict()
        base_dict.update({
            "token_symbol": self.token_symbol,
            "token_address": self.token_address,
            "amount": self.amount,
            "amount_usd": self.amount_usd,
            "from_address": self.from_address,
            "to_address": self.to_address
        })
        return base_dict
