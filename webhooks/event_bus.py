"""
Internal event bus for real-time event distribution.
"""

import asyncio
import logging
from typing import Dict, List, Callable, Any, Optional
from datetime import datetime
from dataclasses import dataclass

from .events import WebhookEvent, WebhookEventType
from .manager import webhook_manager


logger = logging.getLogger(__name__)


@dataclass
class EventSubscription:
    """Event subscription configuration."""
    event_type: str
    handler: Callable
    filter_func: Optional[Callable] = None
    priority: int = 0  # Higher priority handlers run first


class EventBus:
    """Internal event bus for real-time event distribution."""
    
    def __init__(self):
        self.subscriptions: Dict[str, List[EventSubscription]] = {}
        self.running = False
        
        # Statistics
        self.stats = {
            "events_processed": 0,
            "handlers_executed": 0,
            "errors_count": 0,
            "last_event_time": None
        }
    
    def subscribe(self, event_type: str, handler: Callable, filter_func: Optional[Callable] = None, priority: int = 0):
        """Subscribe to events of a specific type."""
        if event_type not in self.subscriptions:
            self.subscriptions[event_type] = []
        
        subscription = EventSubscription(
            event_type=event_type,
            handler=handler,
            filter_func=filter_func,
            priority=priority
        )
        
        self.subscriptions[event_type].append(subscription)
        
        # Sort by priority (higher first)
        self.subscriptions[event_type].sort(key=lambda x: x.priority, reverse=True)
        
        logger.info(f"Subscribed handler to {event_type} events (priority: {priority})")
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """Unsubscribe a handler from events."""
        if event_type in self.subscriptions:
            self.subscriptions[event_type] = [
                sub for sub in self.subscriptions[event_type] 
                if sub.handler != handler
            ]
            logger.info(f"Unsubscribed handler from {event_type} events")
    
    async def emit(self, event: WebhookEvent) -> bool:
        """Emit an event to all subscribers."""
        if not self.running:
            return False
        
        try:
            event_type = event.event_type.value
            subscriptions = self.subscriptions.get(event_type, [])
            
            if not subscriptions:
                # No subscribers, but still emit to webhook manager
                await webhook_manager.emit_event(event)
                return True
            
            # Process all subscriptions
            for subscription in subscriptions:
                try:
                    # Apply filter if present
                    if subscription.filter_func and not subscription.filter_func(event):
                        continue
                    
                    # Execute handler
                    if asyncio.iscoroutinefunction(subscription.handler):
                        await subscription.handler(event)
                    else:
                        subscription.handler(event)
                    
                    self.stats["handlers_executed"] += 1
                    
                except Exception as e:
                    logger.error(f"Error executing handler for {event_type}: {e}")
                    self.stats["errors_count"] += 1
            
            # Always emit to webhook manager for external delivery
            await webhook_manager.emit_event(event)
            
            self.stats["events_processed"] += 1
            self.stats["last_event_time"] = datetime.utcnow()
            
            return True
            
        except Exception as e:
            logger.error(f"Error emitting event to event bus: {e}")
            self.stats["errors_count"] += 1
            return False
    
    async def start(self):
        """Start the event bus."""
        self.running = True
        logger.info("✅ Event bus started")
    
    async def stop(self):
        """Stop the event bus."""
        self.running = False
        logger.info("✅ Event bus stopped")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get event bus statistics."""
        return {
            **self.stats,
            "subscription_count": sum(len(subs) for subs in self.subscriptions.values()),
            "event_types": list(self.subscriptions.keys()),
            "running": self.running
        }


# Global event bus instance
event_bus = EventBus()


# Convenience functions for common gaming events
async def emit_blockchain_event(
    event_type: WebhookEventType,
    blockchain: str,
    block_number: Optional[int] = None,
    transaction_hash: Optional[str] = None,
    contract_address: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None
):
    """Emit a blockchain event."""
    from .events import BlockchainWebhookEvent
    
    event = BlockchainWebhookEvent(
        event_type=event_type,
        blockchain=blockchain,
        block_number=block_number,
        transaction_hash=transaction_hash,
        contract_address=contract_address,
        data=data or {}
    )
    
    await event_bus.emit(event)


async def emit_gaming_event(
    event_type: WebhookEventType,
    blockchain: str,
    gaming_project: Optional[str] = None,
    game_name: Optional[str] = None,
    player_address: Optional[str] = None,
    gaming_action: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None
):
    """Emit a gaming-specific event."""
    from .events import GamingWebhookEvent
    
    event = GamingWebhookEvent(
        event_type=event_type,
        blockchain=blockchain,
        gaming_project=gaming_project,
        game_name=game_name,
        player_address=player_address,
        gaming_action=gaming_action,
        data=data or {}
    )
    
    await event_bus.emit(event)


async def emit_nft_event(
    event_type: WebhookEventType,
    blockchain: str,
    contract_address: str,
    token_id: str,
    collection_name: Optional[str] = None,
    from_address: Optional[str] = None,
    to_address: Optional[str] = None,
    price: Optional[float] = None,
    currency: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None
):
    """Emit an NFT event."""
    from .events import NFTWebhookEvent
    
    event = NFTWebhookEvent(
        event_type=event_type,
        blockchain=blockchain,
        contract_address=contract_address,
        token_id=token_id,
        collection_name=collection_name,
        from_address=from_address,
        to_address=to_address,
        price=price,
        currency=currency,
        data=data or {}
    )
    
    await event_bus.emit(event)


async def emit_token_event(
    event_type: WebhookEventType,
    blockchain: str,
    token_address: str,
    token_symbol: Optional[str] = None,
    amount: Optional[float] = None,
    amount_usd: Optional[float] = None,
    from_address: Optional[str] = None,
    to_address: Optional[str] = None,
    data: Optional[Dict[str, Any]] = None
):
    """Emit a token event."""
    from .events import TokenWebhookEvent
    
    event = TokenWebhookEvent(
        event_type=event_type,
        blockchain=blockchain,
        token_address=token_address,
        token_symbol=token_symbol,
        amount=amount,
        amount_usd=amount_usd,
        from_address=from_address,
        to_address=to_address,
        data=data or {}
    )
    
    await event_bus.emit(event)
