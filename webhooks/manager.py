"""
Webhook manager for handling event emission and delivery.
"""

import asyncio
import logging
import aiohttp
import json
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from collections import defaultdict, deque

from .config import webhook_config
from .events import WebhookEvent, WebhookEventType


logger = logging.getLogger(__name__)


class WebhookManager:
    """Manages webhook event emission and delivery."""
    
    def __init__(self, config=None):
        self.config = config or webhook_config
        self.subscribers: Dict[str, List[str]] = defaultdict(list)  # event_type -> [urls]
        self.internal_handlers: Dict[str, List[Callable]] = defaultdict(list)  # event_type -> [handlers]
        self.event_queue: asyncio.Queue = asyncio.Queue(maxsize=self.config.internal_queue_size)
        self.rate_limiter: Dict[str, deque] = defaultdict(deque)  # url -> timestamps
        self.workers: List[asyncio.Task] = []
        self.running = False
        
        # Statistics
        self.stats = {
            "events_emitted": 0,
            "events_delivered": 0,
            "events_failed": 0,
            "subscribers_count": 0,
            "last_event_time": None
        }
    
    async def start(self):
        """Start the webhook manager and workers."""
        if self.running:
            return
        
        self.running = True
        logger.info(f"Starting webhook manager with {self.config.worker_count} workers")
        
        # Start worker tasks
        for i in range(self.config.worker_count):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info("✅ Webhook manager started successfully")
    
    async def stop(self):
        """Stop the webhook manager and workers."""
        if not self.running:
            return
        
        self.running = False
        logger.info("Stopping webhook manager...")
        
        # Cancel all workers
        for worker in self.workers:
            worker.cancel()
        
        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("✅ Webhook manager stopped")
    
    async def emit_event(self, event: WebhookEvent) -> bool:
        """Emit a webhook event for processing."""
        if not self.config.enabled:
            return False
        
        try:
            # Add to internal queue for processing
            await self.event_queue.put(event)
            self.stats["events_emitted"] += 1
            self.stats["last_event_time"] = datetime.utcnow()
            
            logger.debug(f"Emitted webhook event: {event.event_type} ({event.id})")
            return True
            
        except asyncio.QueueFull:
            logger.warning("Webhook event queue is full, dropping event")
            self.stats["events_failed"] += 1
            return False
        except Exception as e:
            logger.error(f"Error emitting webhook event: {e}")
            self.stats["events_failed"] += 1
            return False
    
    def subscribe_url(self, event_type: str, url: str):
        """Subscribe a URL to receive webhook events of a specific type."""
        if url not in self.subscribers[event_type]:
            self.subscribers[event_type].append(url)
            self.stats["subscribers_count"] = sum(len(urls) for urls in self.subscribers.values())
            logger.info(f"Subscribed {url} to {event_type} events")
    
    def unsubscribe_url(self, event_type: str, url: str):
        """Unsubscribe a URL from webhook events."""
        if url in self.subscribers[event_type]:
            self.subscribers[event_type].remove(url)
            self.stats["subscribers_count"] = sum(len(urls) for urls in self.subscribers.values())
            logger.info(f"Unsubscribed {url} from {event_type} events")
    
    def add_internal_handler(self, event_type: str, handler: Callable):
        """Add an internal event handler function."""
        self.internal_handlers[event_type].append(handler)
        logger.info(f"Added internal handler for {event_type} events")
    
    async def _worker(self, worker_name: str):
        """Worker task for processing webhook events."""
        logger.info(f"Webhook worker {worker_name} started")
        
        while self.running:
            try:
                # Get event from queue with timeout
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                
                # Process the event
                await self._process_event(event)
                
            except asyncio.TimeoutError:
                # No events in queue, continue
                continue
            except Exception as e:
                logger.error(f"Error in webhook worker {worker_name}: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"Webhook worker {worker_name} stopped")
    
    async def _process_event(self, event: WebhookEvent):
        """Process a single webhook event."""
        event_type = event.event_type.value
        
        # Process internal handlers first
        await self._process_internal_handlers(event_type, event)
        
        # Process external subscribers
        await self._process_external_subscribers(event_type, event)
    
    async def _process_internal_handlers(self, event_type: str, event: WebhookEvent):
        """Process internal event handlers."""
        handlers = self.internal_handlers.get(event_type, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                logger.error(f"Error in internal handler for {event_type}: {e}")
    
    async def _process_external_subscribers(self, event_type: str, event: WebhookEvent):
        """Process external webhook subscribers."""
        urls = self.subscribers.get(event_type, [])
        
        if not urls:
            return
        
        # Prepare webhook payload
        payload = event.to_dict()
        
        # Send to all subscribers
        tasks = []
        for url in urls:
            if self._check_rate_limit(url):
                task = asyncio.create_task(self._send_webhook(url, payload))
                tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Update statistics
            for result in results:
                if isinstance(result, Exception):
                    self.stats["events_failed"] += 1
                else:
                    self.stats["events_delivered"] += 1
    
    def _check_rate_limit(self, url: str) -> bool:
        """Check if URL is within rate limits."""
        now = datetime.utcnow()
        minute_ago = now - timedelta(minutes=1)
        
        # Clean old timestamps
        url_timestamps = self.rate_limiter[url]
        while url_timestamps and url_timestamps[0] < minute_ago:
            url_timestamps.popleft()
        
        # Check rate limit
        if len(url_timestamps) >= self.config.rate_limit_per_minute:
            return False
        
        # Add current timestamp
        url_timestamps.append(now)
        return True
    
    async def _send_webhook(self, url: str, payload: Dict[str, Any]) -> bool:
        """Send webhook to a specific URL."""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Web3GameScraper-Webhook/1.0"
        }
        
        # Add authentication if configured
        if self.config.webhook_secret:
            headers["X-Webhook-Secret"] = self.config.webhook_secret
        
        for attempt in range(self.config.max_retries + 1):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.config.timeout)) as session:
                    async with session.post(url, json=payload, headers=headers) as response:
                        if response.status == 200:
                            logger.debug(f"Webhook delivered successfully to {url}")
                            return True
                        else:
                            logger.warning(f"Webhook delivery failed to {url}: HTTP {response.status}")
                            
            except Exception as e:
                logger.warning(f"Webhook delivery attempt {attempt + 1} failed to {url}: {e}")
                
                if attempt < self.config.max_retries:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))  # Exponential backoff
        
        logger.error(f"Webhook delivery failed to {url} after {self.config.max_retries + 1} attempts")
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get webhook manager statistics."""
        return {
            **self.stats,
            "queue_size": self.event_queue.qsize(),
            "active_workers": len([w for w in self.workers if not w.done()]),
            "total_subscribers": self.stats["subscribers_count"],
            "running": self.running
        }


# Global webhook manager instance
webhook_manager = WebhookManager()
