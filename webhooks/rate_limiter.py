"""
Advanced rate limiting and caching for webhook system.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
import aioredis
from collections import defaultdict, deque

from config.settings import get_settings
from monitoring.webhook_metrics import webhook_metrics

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class RateLimitResult:
    """Result of rate limit check"""
    allowed: bool
    remaining: int
    reset_time: datetime
    retry_after: Optional[int] = None


@dataclass
class CacheConfig:
    """Cache configuration"""
    ttl_seconds: int
    max_size: int
    compression: bool = False


class IntelligentRateLimiter:
    """Intelligent rate limiter with Redis backend"""
    
    def __init__(self):
        self.redis_client: Optional[aioredis.Redis] = None
        self.local_cache: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.rate_limit_configs: Dict[str, Dict] = {}
        
        # Default rate limits
        self.default_limits = {
            'per_minute': 100,
            'per_hour': 1000,
            'per_day': 10000,
            'burst_limit': 20,
            'burst_window': 10  # seconds
        }
        
        # Adaptive rate limiting
        self.subscriber_performance: Dict[str, Dict] = defaultdict(lambda: {
            'success_rate': 1.0,
            'avg_response_time': 0.0,
            'error_count': 0,
            'last_error_time': None,
            'adaptive_limit': None
        })
    
    async def initialize(self):
        """Initialize rate limiter"""
        try:
            self.redis_client = aioredis.from_url(
                settings.redis.url,
                encoding="utf-8",
                decode_responses=True
            )
            
            await self.redis_client.ping()
            logger.info("✅ Rate limiter Redis connection established")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize rate limiter: {e}")
    
    async def check_rate_limit(self, subscriber_url: str, 
                              custom_limit: Optional[int] = None) -> RateLimitResult:
        """Check if request is within rate limits"""
        current_time = time.time()
        
        # Get rate limit configuration
        limit_per_minute = custom_limit or self.default_limits['per_minute']
        
        # Apply adaptive rate limiting
        adaptive_limit = await self._get_adaptive_limit(subscriber_url, limit_per_minute)
        
        try:
            if self.redis_client:
                return await self._check_redis_rate_limit(
                    subscriber_url, adaptive_limit, current_time
                )
            else:
                return await self._check_local_rate_limit(
                    subscriber_url, adaptive_limit, current_time
                )
        except Exception as e:
            logger.error(f"❌ Rate limit check failed: {e}")
            # Fail open - allow request but log error
            return RateLimitResult(
                allowed=True,
                remaining=adaptive_limit,
                reset_time=datetime.utcnow() + timedelta(minutes=1)
            )
    
    async def _get_adaptive_limit(self, subscriber_url: str, base_limit: int) -> int:
        """Get adaptive rate limit based on subscriber performance"""
        perf = self.subscriber_performance[subscriber_url]
        
        # If no adaptive limit set, use base limit
        if perf['adaptive_limit'] is None:
            perf['adaptive_limit'] = base_limit
            return base_limit
        
        # Adjust based on performance
        success_rate = perf['success_rate']
        avg_response_time = perf['avg_response_time']
        
        # Increase limit for well-performing subscribers
        if success_rate > 0.98 and avg_response_time < 1000:  # < 1 second
            perf['adaptive_limit'] = min(base_limit * 2, base_limit * 3)
        # Decrease limit for poorly performing subscribers
        elif success_rate < 0.9 or avg_response_time > 5000:  # > 5 seconds
            perf['adaptive_limit'] = max(base_limit // 2, 10)
        # Gradually restore limit for recovering subscribers
        elif success_rate > 0.95:
            perf['adaptive_limit'] = min(perf['adaptive_limit'] + 1, base_limit)
        
        return perf['adaptive_limit']
    
    async def _check_redis_rate_limit(self, subscriber_url: str, 
                                    limit: int, current_time: float) -> RateLimitResult:
        """Check rate limit using Redis"""
        key = f"rate_limit:{subscriber_url}"
        window_start = int(current_time // 60) * 60  # 1-minute window
        
        # Use Redis pipeline for atomic operations
        pipe = self.redis_client.pipeline()
        
        # Count requests in current window
        pipe.zcount(key, window_start, current_time)
        
        # Remove old entries (older than 1 minute)
        pipe.zremrangebyscore(key, 0, current_time - 60)
        
        # Add current request
        pipe.zadd(key, {str(current_time): current_time})
        
        # Set expiration
        pipe.expire(key, 120)  # 2 minutes
        
        results = await pipe.execute()
        current_count = results[0]
        
        if current_count >= limit:
            # Rate limited
            webhook_metrics.record_rate_limit(subscriber_url)
            
            return RateLimitResult(
                allowed=False,
                remaining=0,
                reset_time=datetime.fromtimestamp(window_start + 60),
                retry_after=int(window_start + 60 - current_time)
            )
        else:
            return RateLimitResult(
                allowed=True,
                remaining=limit - current_count - 1,
                reset_time=datetime.fromtimestamp(window_start + 60)
            )
    
    async def _check_local_rate_limit(self, subscriber_url: str, 
                                    limit: int, current_time: float) -> RateLimitResult:
        """Check rate limit using local cache (fallback)"""
        requests = self.local_cache[subscriber_url]
        window_start = current_time - 60  # 1-minute window
        
        # Remove old requests
        while requests and requests[0] < window_start:
            requests.popleft()
        
        if len(requests) >= limit:
            # Rate limited
            webhook_metrics.record_rate_limit(subscriber_url)
            
            return RateLimitResult(
                allowed=False,
                remaining=0,
                reset_time=datetime.fromtimestamp(requests[0] + 60),
                retry_after=int(requests[0] + 60 - current_time)
            )
        else:
            requests.append(current_time)
            return RateLimitResult(
                allowed=True,
                remaining=limit - len(requests),
                reset_time=datetime.fromtimestamp(current_time + 60)
            )
    
    async def update_subscriber_performance(self, subscriber_url: str, 
                                          success: bool, response_time_ms: float):
        """Update subscriber performance metrics for adaptive rate limiting"""
        perf = self.subscriber_performance[subscriber_url]
        
        # Update success rate (exponential moving average)
        alpha = 0.1  # Smoothing factor
        perf['success_rate'] = (alpha * (1.0 if success else 0.0) + 
                               (1 - alpha) * perf['success_rate'])
        
        # Update average response time
        perf['avg_response_time'] = (alpha * response_time_ms + 
                                   (1 - alpha) * perf['avg_response_time'])
        
        # Track errors
        if not success:
            perf['error_count'] += 1
            perf['last_error_time'] = time.time()
        
        # Store in Redis for persistence
        if self.redis_client:
            try:
                await self.redis_client.hset(
                    f"subscriber_perf:{subscriber_url}",
                    mapping={
                        'success_rate': perf['success_rate'],
                        'avg_response_time': perf['avg_response_time'],
                        'error_count': perf['error_count'],
                        'last_error_time': perf['last_error_time'] or 0,
                        'adaptive_limit': perf['adaptive_limit'] or 0
                    }
                )
                await self.redis_client.expire(f"subscriber_perf:{subscriber_url}", 86400)  # 24 hours
            except Exception as e:
                logger.error(f"❌ Failed to store subscriber performance: {e}")
    
    async def get_rate_limit_stats(self) -> Dict[str, Any]:
        """Get rate limiting statistics"""
        stats = {
            'total_subscribers': len(self.subscriber_performance),
            'adaptive_limits': {},
            'performance_metrics': {},
            'rate_limited_count': 0
        }
        
        for subscriber_url, perf in self.subscriber_performance.items():
            stats['adaptive_limits'][subscriber_url] = perf['adaptive_limit']
            stats['performance_metrics'][subscriber_url] = {
                'success_rate': perf['success_rate'],
                'avg_response_time': perf['avg_response_time'],
                'error_count': perf['error_count']
            }
        
        return stats


class AdvancedCacheManager:
    """Advanced caching manager with Redis backend"""
    
    def __init__(self):
        self.redis_client: Optional[aioredis.Redis] = None
        self.local_cache: Dict[str, Any] = {}
        self.cache_configs: Dict[str, CacheConfig] = {
            'subscriptions': CacheConfig(ttl_seconds=300, max_size=1000),
            'gaming_contracts': CacheConfig(ttl_seconds=600, max_size=5000),
            'gaming_projects': CacheConfig(ttl_seconds=600, max_size=1000),
            'webhook_templates': CacheConfig(ttl_seconds=3600, max_size=100),
            'rate_limits': CacheConfig(ttl_seconds=60, max_size=10000)
        }
        
        # Cache statistics
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    async def initialize(self):
        """Initialize cache manager"""
        try:
            self.redis_client = aioredis.from_url(
                settings.redis.url,
                encoding="utf-8",
                decode_responses=True
            )
            
            await self.redis_client.ping()
            logger.info("✅ Cache manager Redis connection established")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize cache manager: {e}")
    
    async def get(self, key: str, cache_type: str = 'default') -> Optional[Any]:
        """Get value from cache"""
        try:
            # Try Redis first
            if self.redis_client:
                cached_value = await self.redis_client.get(f"cache:{cache_type}:{key}")
                if cached_value:
                    self.cache_stats['hits'] += 1
                    return json.loads(cached_value)
            
            # Try local cache
            local_key = f"{cache_type}:{key}"
            if local_key in self.local_cache:
                self.cache_stats['hits'] += 1
                return self.local_cache[local_key]
            
            self.cache_stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"❌ Cache get error: {e}")
            self.cache_stats['errors'] += 1
            return None
    
    async def set(self, key: str, value: Any, cache_type: str = 'default', 
                 ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        try:
            config = self.cache_configs.get(cache_type, self.cache_configs['subscriptions'])
            ttl = ttl or config.ttl_seconds
            
            # Store in Redis
            if self.redis_client:
                await self.redis_client.setex(
                    f"cache:{cache_type}:{key}",
                    ttl,
                    json.dumps(value)
                )
            
            # Store in local cache
            local_key = f"{cache_type}:{key}"
            self.local_cache[local_key] = value
            
            # Manage local cache size
            if len(self.local_cache) > config.max_size:
                # Remove oldest entries (simple FIFO)
                keys_to_remove = list(self.local_cache.keys())[:len(self.local_cache) - config.max_size]
                for old_key in keys_to_remove:
                    del self.local_cache[old_key]
            
            self.cache_stats['sets'] += 1
            return True
            
        except Exception as e:
            logger.error(f"❌ Cache set error: {e}")
            self.cache_stats['errors'] += 1
            return False
    
    async def delete(self, key: str, cache_type: str = 'default') -> bool:
        """Delete value from cache"""
        try:
            # Delete from Redis
            if self.redis_client:
                await self.redis_client.delete(f"cache:{cache_type}:{key}")
            
            # Delete from local cache
            local_key = f"{cache_type}:{key}"
            if local_key in self.local_cache:
                del self.local_cache[local_key]
            
            self.cache_stats['deletes'] += 1
            return True
            
        except Exception as e:
            logger.error(f"❌ Cache delete error: {e}")
            self.cache_stats['errors'] += 1
            return False
    
    async def clear_cache_type(self, cache_type: str) -> bool:
        """Clear all entries of a specific cache type"""
        try:
            # Clear from Redis
            if self.redis_client:
                pattern = f"cache:{cache_type}:*"
                keys = await self.redis_client.keys(pattern)
                if keys:
                    await self.redis_client.delete(*keys)
            
            # Clear from local cache
            keys_to_remove = [k for k in self.local_cache.keys() if k.startswith(f"{cache_type}:")]
            for key in keys_to_remove:
                del self.local_cache[key]
            
            logger.info(f"✅ Cleared cache type: {cache_type}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to clear cache type {cache_type}: {e}")
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / max(total_requests, 1)) * 100
        
        stats = {
            **self.cache_stats,
            'hit_rate_percent': hit_rate,
            'local_cache_size': len(self.local_cache),
            'cache_types': list(self.cache_configs.keys())
        }
        
        # Get Redis info if available
        if self.redis_client:
            try:
                redis_info = await self.redis_client.info('memory')
                stats['redis_memory_usage'] = redis_info.get('used_memory_human', 'unknown')
                stats['redis_connected'] = True
            except:
                stats['redis_connected'] = False
        else:
            stats['redis_connected'] = False
        
        return stats


# Global instances
rate_limiter = IntelligentRateLimiter()
cache_manager = AdvancedCacheManager()
