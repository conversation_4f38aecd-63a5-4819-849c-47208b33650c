"""
Enhanced webhook manager with performance optimizations.
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp
from dataclasses import dataclass

from .manager import <PERSON>hookManager
from .performance_optimizer import performance_optimizer
from .events import WebhookEvent, GamingWebhookEvent
from .gaming_classifier import DatabaseGamingClassifier
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class DeliveryResult:
    """Result of webhook delivery attempt"""
    success: bool
    status_code: Optional[int] = None
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None
    retry_after: Optional[int] = None


class EnhancedWebhookManager(WebhookManager):
    """Enhanced webhook manager with performance optimizations"""
    
    def __init__(self):
        super().__init__()
        self.gaming_classifier = DatabaseGamingClassifier()
        self.connection_pool: Optional[aiohttp.ClientSession] = None
        self.delivery_semaphore = asyncio.Semaphore(50)  # Limit concurrent deliveries
        
        # Performance tracking
        self.delivery_stats = {
            'total_attempts': 0,
            'successful_deliveries': 0,
            'failed_deliveries': 0,
            'average_response_time': 0.0
        }
    
    async def initialize(self):
        """Initialize enhanced webhook manager"""
        await super().initialize()
        await performance_optimizer.initialize()
        
        # Create optimized HTTP client session
        connector = aiohttp.TCPConnector(
            limit=100,  # Total connection pool size
            limit_per_host=20,  # Connections per host
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(
            total=settings.webhook.timeout,
            connect=10,
            sock_read=settings.webhook.timeout
        )
        
        self.connection_pool = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Web3GamingWebhooks/1.0',
                'Content-Type': 'application/json'
            }
        )
        
        logger.info("✅ Enhanced webhook manager initialized with connection pooling")
    
    async def process_event(self, event: WebhookEvent):
        """Process webhook event with enhanced performance"""
        start_time = time.time()
        
        try:
            # Get gaming classification if it's a gaming event
            gaming_classification = None
            if isinstance(event, GamingWebhookEvent):
                gaming_classification = await self.gaming_classifier.classify_event(event)
            
            # Get matching subscriptions from cache
            subscriptions = await performance_optimizer.get_cached_subscriptions(
                event_type=event.event_type.value,
                gaming_category=gaming_classification.category.value if gaming_classification else None,
                blockchain=event.blockchain
            )
            
            if not subscriptions:
                logger.debug(f"No matching subscriptions for event {event.event_type.value}")
                return
            
            # Filter subscriptions based on significance and value thresholds
            filtered_subscriptions = []
            for subscription in subscriptions:
                if self._should_deliver_to_subscription(event, subscription, gaming_classification):
                    filtered_subscriptions.append(subscription)
            
            if not filtered_subscriptions:
                logger.debug(f"No subscriptions passed filtering for event {event.event_type.value}")
                return
            
            # Create delivery tasks
            delivery_tasks = []
            for subscription in filtered_subscriptions:
                task = self._create_delivery_task(event, subscription, gaming_classification)
                delivery_tasks.append(task)
            
            # Execute deliveries concurrently with semaphore limiting
            if delivery_tasks:
                await asyncio.gather(*delivery_tasks, return_exceptions=True)
            
            # Update performance metrics
            processing_time = (time.time() - start_time) * 1000
            performance_optimizer.add_processing_time(processing_time)
            
            logger.info(f"✅ Processed event {event.id} for {len(filtered_subscriptions)} subscriptions in {processing_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"❌ Failed to process event {event.id}: {e}")
    
    def _should_deliver_to_subscription(self, event: WebhookEvent, 
                                      subscription: Dict, 
                                      gaming_classification) -> bool:
        """Check if event should be delivered to subscription"""
        # Check significance threshold
        if gaming_classification and gaming_classification.significance_score:
            min_significance = subscription.get('min_significance_score', 0.0)
            if gaming_classification.significance_score < min_significance:
                return False
        
        # Check value thresholds
        event_data = event.data or {}
        
        # Token value threshold
        token_value = event_data.get('amount_usd', 0.0)
        min_token_value = subscription.get('min_token_value_usd', 0.0)
        if token_value > 0 and token_value < min_token_value:
            return False
        
        # NFT value threshold
        nft_value = event_data.get('price_usd', 0.0)
        min_nft_value = subscription.get('min_nft_value_usd', 0.0)
        if nft_value > 0 and nft_value < min_nft_value:
            return False
        
        return True
    
    async def _create_delivery_task(self, event: WebhookEvent, 
                                   subscription: Dict, 
                                   gaming_classification) -> asyncio.Task:
        """Create delivery task for subscription"""
        return asyncio.create_task(
            self._deliver_to_subscription(event, subscription, gaming_classification)
        )
    
    async def _deliver_to_subscription(self, event: WebhookEvent, 
                                     subscription: Dict, 
                                     gaming_classification):
        """Deliver webhook to subscription with performance optimization"""
        async with self.delivery_semaphore:
            start_time = time.time()
            
            try:
                # Prepare webhook payload
                payload = self._prepare_webhook_payload(event, gaming_classification)
                
                # Add to batch processing
                delivery_data = {
                    'webhook_event_id': event.id,
                    'event_type': event.event_type.value,
                    'subscriber_url': subscription['subscriber_url'],
                    'gaming_category': gaming_classification.category.value if gaming_classification else None,
                    'gaming_action': gaming_classification.action.value if gaming_classification else None,
                    'significance_score': gaming_classification.significance_score if gaming_classification else None,
                    'event_payload': json.dumps(payload)
                }
                
                await performance_optimizer.add_to_batch(delivery_data)
                
                # Attempt delivery
                result = await self._attempt_delivery(
                    subscription['subscriber_url'],
                    payload,
                    subscription.get('webhook_secret')
                )
                
                # Update delivery statistics
                delivery_time = (time.time() - start_time) * 1000
                performance_optimizer.add_delivery_time(delivery_time)
                
                if result.success:
                    self.delivery_stats['successful_deliveries'] += 1
                    logger.debug(f"✅ Delivered webhook to {subscription['subscriber_url']} in {delivery_time:.2f}ms")
                else:
                    self.delivery_stats['failed_deliveries'] += 1
                    logger.warning(f"❌ Failed to deliver webhook to {subscription['subscriber_url']}: {result.error_message}")
                
                self.delivery_stats['total_attempts'] += 1
                
            except Exception as e:
                logger.error(f"❌ Error delivering to {subscription['subscriber_url']}: {e}")
                self.delivery_stats['failed_deliveries'] += 1
                self.delivery_stats['total_attempts'] += 1
    
    def _prepare_webhook_payload(self, event: WebhookEvent, gaming_classification) -> Dict:
        """Prepare webhook payload with gaming classification"""
        payload = event.to_dict()
        
        # Add gaming classification if available
        if gaming_classification:
            payload['gaming_classification'] = {
                'category': gaming_classification.category.value,
                'action': gaming_classification.action.value,
                'significance_score': gaming_classification.significance_score,
                'confidence_score': gaming_classification.confidence_score,
                'gaming_project': gaming_classification.gaming_project,
                'player_address': gaming_classification.player_address,
                'value_usd': gaming_classification.value_usd
            }
        
        return payload
    
    async def _attempt_delivery(self, url: str, payload: Dict, 
                               webhook_secret: Optional[str] = None) -> DeliveryResult:
        """Attempt webhook delivery with optimized HTTP client"""
        if not self.connection_pool:
            return DeliveryResult(
                success=False,
                error_message="Connection pool not initialized"
            )
        
        start_time = time.time()
        
        try:
            # Prepare headers
            headers = {
                'Content-Type': 'application/json',
                'X-Webhook-Timestamp': str(int(time.time())),
                'X-Webhook-Source': 'Web3GamingTracker'
            }
            
            # Add signature if webhook secret is provided
            if webhook_secret:
                import hmac
                import hashlib
                
                payload_str = json.dumps(payload, sort_keys=True)
                signature = hmac.new(
                    webhook_secret.encode(),
                    payload_str.encode(),
                    hashlib.sha256
                ).hexdigest()
                headers['X-Webhook-Signature'] = f"sha256={signature}"
            
            # Make HTTP request
            async with self.connection_pool.post(
                url,
                json=payload,
                headers=headers
            ) as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    return DeliveryResult(
                        success=True,
                        status_code=response.status,
                        response_time_ms=response_time
                    )
                elif response.status == 429:
                    # Rate limited
                    retry_after = response.headers.get('Retry-After', 60)
                    return DeliveryResult(
                        success=False,
                        status_code=response.status,
                        response_time_ms=response_time,
                        error_message="Rate limited",
                        retry_after=int(retry_after)
                    )
                else:
                    error_text = await response.text()
                    return DeliveryResult(
                        success=False,
                        status_code=response.status,
                        response_time_ms=response_time,
                        error_message=f"HTTP {response.status}: {error_text[:200]}"
                    )
        
        except asyncio.TimeoutError:
            return DeliveryResult(
                success=False,
                error_message="Request timeout"
            )
        except Exception as e:
            return DeliveryResult(
                success=False,
                error_message=str(e)
            )
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get enhanced performance statistics"""
        base_stats = await super().get_stats()
        
        # Get performance optimizer metrics
        perf_metrics = await performance_optimizer.get_performance_metrics()
        
        # Combine statistics
        enhanced_stats = {
            **base_stats,
            'performance_metrics': {
                'total_events_processed': perf_metrics.total_events_processed,
                'average_processing_time_ms': perf_metrics.average_processing_time_ms,
                'average_delivery_time_ms': perf_metrics.average_delivery_time_ms,
                'cache_hit_rate': perf_metrics.cache_hit_rate,
                'queue_size': perf_metrics.queue_size,
                'memory_usage_mb': perf_metrics.memory_usage_mb
            },
            'delivery_stats': self.delivery_stats,
            'connection_pool': {
                'total_connections': self.connection_pool.connector._limit if self.connection_pool else 0,
                'connections_per_host': self.connection_pool.connector._limit_per_host if self.connection_pool else 0
            }
        }
        
        return enhanced_stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check"""
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'components': {}
        }
        
        try:
            # Check connection pool
            if self.connection_pool and not self.connection_pool.closed:
                health_status['components']['connection_pool'] = 'healthy'
            else:
                health_status['components']['connection_pool'] = 'unhealthy'
                health_status['status'] = 'degraded'
            
            # Check performance optimizer
            if performance_optimizer.redis_client:
                try:
                    await performance_optimizer.redis_client.ping()
                    health_status['components']['redis_cache'] = 'healthy'
                except:
                    health_status['components']['redis_cache'] = 'unhealthy'
                    health_status['status'] = 'degraded'
            
            # Check delivery performance
            if self.delivery_stats['total_attempts'] > 0:
                success_rate = (self.delivery_stats['successful_deliveries'] / 
                              self.delivery_stats['total_attempts'])
                if success_rate > 0.95:
                    health_status['components']['delivery_performance'] = 'healthy'
                elif success_rate > 0.8:
                    health_status['components']['delivery_performance'] = 'degraded'
                else:
                    health_status['components']['delivery_performance'] = 'unhealthy'
                    health_status['status'] = 'unhealthy'
            
            # Add performance metrics
            perf_metrics = await performance_optimizer.get_performance_metrics()
            health_status['metrics'] = {
                'cache_hit_rate': perf_metrics.cache_hit_rate,
                'average_processing_time_ms': perf_metrics.average_processing_time_ms,
                'queue_size': perf_metrics.queue_size,
                'success_rate': (self.delivery_stats['successful_deliveries'] / 
                               max(self.delivery_stats['total_attempts'], 1))
            }
            
        except Exception as e:
            health_status['status'] = 'unhealthy'
            health_status['error'] = str(e)
        
        return health_status
    
    async def close(self):
        """Close enhanced webhook manager"""
        await super().close()
        
        # Close connection pool
        if self.connection_pool:
            await self.connection_pool.close()
        
        # Close performance optimizer
        await performance_optimizer.close()
        
        logger.info("✅ Enhanced webhook manager closed")


# Global enhanced webhook manager instance
enhanced_webhook_manager = EnhancedWebhookManager()
