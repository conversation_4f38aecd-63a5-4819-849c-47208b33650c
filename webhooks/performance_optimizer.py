"""
Webhook system performance optimization and monitoring.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import aioredis
from sqlalchemy.orm import Session
from sqlalchemy import text

from models.base import get_db_sync
from models.gaming import WebhookSubscription, WebhookDelivery
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class PerformanceMetrics:
    """Performance metrics for webhook system"""
    total_events_processed: int = 0
    successful_deliveries: int = 0
    failed_deliveries: int = 0
    average_processing_time_ms: float = 0.0
    average_delivery_time_ms: float = 0.0
    queue_size: int = 0
    active_workers: int = 0
    cache_hit_rate: float = 0.0
    database_query_time_ms: float = 0.0
    redis_operation_time_ms: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0


@dataclass
class CacheEntry:
    """Cache entry with TTL"""
    data: Any
    created_at: datetime
    ttl_seconds: int
    
    def is_expired(self) -> bool:
        return datetime.utcnow() > self.created_at + timedelta(seconds=self.ttl_seconds)


class PerformanceOptimizer:
    """Webhook system performance optimizer"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.cache: Dict[str, CacheEntry] = {}
        self.redis_client: Optional[aioredis.Redis] = None
        self.processing_times: deque = deque(maxlen=1000)
        self.delivery_times: deque = deque(maxlen=1000)
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Performance tracking
        self.query_cache: Dict[str, Any] = {}
        self.subscription_cache: Dict[str, List[Dict]] = {}
        self.gaming_contract_cache: Dict[str, Dict] = {}
        
        # Batch processing
        self.batch_size = 100
        self.batch_timeout = 5.0  # seconds
        self.pending_deliveries: List[Dict] = []
        self.last_batch_time = time.time()
    
    async def initialize(self):
        """Initialize performance optimizer"""
        try:
            # Initialize Redis connection
            self.redis_client = aioredis.from_url(
                settings.redis.url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test Redis connection
            await self.redis_client.ping()
            logger.info("✅ Performance optimizer Redis connection established")
            
            # Warm up caches
            await self.warm_up_caches()
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize performance optimizer: {e}")
    
    async def warm_up_caches(self):
        """Warm up frequently used caches"""
        try:
            # Cache active subscriptions
            await self.cache_active_subscriptions()
            
            # Cache gaming contracts
            await self.cache_gaming_contracts()
            
            # Cache gaming projects
            await self.cache_gaming_projects()
            
            logger.info("✅ Performance caches warmed up")
            
        except Exception as e:
            logger.error(f"❌ Failed to warm up caches: {e}")
    
    async def cache_active_subscriptions(self):
        """Cache active webhook subscriptions"""
        cache_key = "active_subscriptions"
        
        try:
            db = get_db_sync()
            
            # Use optimized query
            result = db.execute(text("""
                SELECT 
                    id, subscriber_url, webhook_secret, event_types,
                    gaming_categories, blockchain_filters, min_significance_score,
                    min_token_value_usd, min_nft_value_usd, rate_limit_per_minute
                FROM webhook_subscriptions 
                WHERE is_active = true AND is_verified = true
            """))
            
            subscriptions = []
            for row in result:
                subscriptions.append({
                    'id': row[0],
                    'subscriber_url': row[1],
                    'webhook_secret': row[2],
                    'event_types': row[3] or [],
                    'gaming_categories': row[4] or [],
                    'blockchain_filters': row[5] or [],
                    'min_significance_score': float(row[6]) if row[6] else 0.0,
                    'min_token_value_usd': float(row[7]) if row[7] else 0.0,
                    'min_nft_value_usd': float(row[8]) if row[8] else 0.0,
                    'rate_limit_per_minute': row[9] or 100
                })
            
            # Cache in memory
            self.subscription_cache[cache_key] = subscriptions
            
            # Cache in Redis
            if self.redis_client:
                await self.redis_client.setex(
                    f"webhook:{cache_key}",
                    300,  # 5 minutes TTL
                    str(subscriptions)
                )
            
            logger.info(f"✅ Cached {len(subscriptions)} active subscriptions")
            
        except Exception as e:
            logger.error(f"❌ Failed to cache active subscriptions: {e}")
        finally:
            db.close()
    
    async def cache_gaming_contracts(self):
        """Cache gaming contract information"""
        try:
            db = get_db_sync()
            
            result = db.execute(text("""
                SELECT 
                    contract_address, blockchain, name, symbol, contract_type,
                    is_gaming, confidence_level, confidence_score
                FROM gaming_contracts 
                WHERE is_gaming = true
            """))
            
            for row in result:
                cache_key = f"{row[0]}:{row[1]}"  # address:blockchain
                contract_data = {
                    'contract_address': row[0],
                    'blockchain': row[1],
                    'name': row[2],
                    'symbol': row[3],
                    'contract_type': row[4],
                    'is_gaming': row[5],
                    'confidence_level': row[6],
                    'confidence_score': float(row[7]) if row[7] else 0.0
                }
                
                self.gaming_contract_cache[cache_key] = contract_data
                
                # Cache in Redis
                if self.redis_client:
                    await self.redis_client.setex(
                        f"webhook:contract:{cache_key}",
                        600,  # 10 minutes TTL
                        str(contract_data)
                    )
            
            logger.info(f"✅ Cached {len(self.gaming_contract_cache)} gaming contracts")
            
        except Exception as e:
            logger.error(f"❌ Failed to cache gaming contracts: {e}")
        finally:
            db.close()
    
    async def cache_gaming_projects(self):
        """Cache gaming project information"""
        try:
            db = get_db_sync()
            
            result = db.execute(text("""
                SELECT 
                    id, project_name, blockchain, primary_genre, is_active
                FROM gaming_projects 
                WHERE is_active = true
            """))
            
            projects = {}
            for row in result:
                project_data = {
                    'id': row[0],
                    'project_name': row[1],
                    'blockchain': row[2],
                    'primary_genre': row[3],
                    'is_active': row[4]
                }
                projects[row[1].lower()] = project_data
            
            # Cache in Redis
            if self.redis_client:
                await self.redis_client.setex(
                    "webhook:gaming_projects",
                    600,  # 10 minutes TTL
                    str(projects)
                )
            
            logger.info(f"✅ Cached {len(projects)} gaming projects")
            
        except Exception as e:
            logger.error(f"❌ Failed to cache gaming projects: {e}")
        finally:
            db.close()
    
    async def get_cached_subscriptions(self, event_type: str = None, 
                                     gaming_category: str = None,
                                     blockchain: str = None) -> List[Dict]:
        """Get cached active subscriptions with filtering"""
        cache_key = "active_subscriptions"
        
        # Try memory cache first
        if cache_key in self.subscription_cache:
            subscriptions = self.subscription_cache[cache_key]
            self.cache_hits += 1
        else:
            # Try Redis cache
            if self.redis_client:
                try:
                    cached_data = await self.redis_client.get(f"webhook:{cache_key}")
                    if cached_data:
                        subscriptions = eval(cached_data)  # In production, use proper JSON
                        self.subscription_cache[cache_key] = subscriptions
                        self.cache_hits += 1
                    else:
                        await self.cache_active_subscriptions()
                        subscriptions = self.subscription_cache.get(cache_key, [])
                        self.cache_misses += 1
                except Exception as e:
                    logger.error(f"Redis cache error: {e}")
                    await self.cache_active_subscriptions()
                    subscriptions = self.subscription_cache.get(cache_key, [])
                    self.cache_misses += 1
            else:
                await self.cache_active_subscriptions()
                subscriptions = self.subscription_cache.get(cache_key, [])
                self.cache_misses += 1
        
        # Apply filters
        filtered_subscriptions = []
        for sub in subscriptions:
            if event_type and event_type not in sub.get('event_types', []):
                continue
            if gaming_category and gaming_category not in sub.get('gaming_categories', []):
                continue
            if blockchain and blockchain not in sub.get('blockchain_filters', []):
                continue
            
            filtered_subscriptions.append(sub)
        
        return filtered_subscriptions
    
    async def get_cached_gaming_contract(self, contract_address: str, 
                                       blockchain: str) -> Optional[Dict]:
        """Get cached gaming contract information"""
        cache_key = f"{contract_address}:{blockchain}"
        
        # Try memory cache first
        if cache_key in self.gaming_contract_cache:
            self.cache_hits += 1
            return self.gaming_contract_cache[cache_key]
        
        # Try Redis cache
        if self.redis_client:
            try:
                cached_data = await self.redis_client.get(f"webhook:contract:{cache_key}")
                if cached_data:
                    contract_data = eval(cached_data)  # In production, use proper JSON
                    self.gaming_contract_cache[cache_key] = contract_data
                    self.cache_hits += 1
                    return contract_data
            except Exception as e:
                logger.error(f"Redis cache error: {e}")
        
        # Cache miss - query database
        self.cache_misses += 1
        try:
            db = get_db_sync()
            result = db.execute(text("""
                SELECT 
                    contract_address, blockchain, name, symbol, contract_type,
                    is_gaming, confidence_level, confidence_score
                FROM gaming_contracts 
                WHERE contract_address = :address AND blockchain = :blockchain AND is_gaming = true
                LIMIT 1
            """), {"address": contract_address, "blockchain": blockchain})
            
            row = result.fetchone()
            if row:
                contract_data = {
                    'contract_address': row[0],
                    'blockchain': row[1],
                    'name': row[2],
                    'symbol': row[3],
                    'contract_type': row[4],
                    'is_gaming': row[5],
                    'confidence_level': row[6],
                    'confidence_score': float(row[7]) if row[7] else 0.0
                }
                
                # Cache the result
                self.gaming_contract_cache[cache_key] = contract_data
                
                if self.redis_client:
                    await self.redis_client.setex(
                        f"webhook:contract:{cache_key}",
                        600,
                        str(contract_data)
                    )
                
                return contract_data
            
        except Exception as e:
            logger.error(f"❌ Failed to query gaming contract: {e}")
        finally:
            db.close()
        
        return None
    
    def add_processing_time(self, processing_time_ms: float):
        """Add processing time measurement"""
        self.processing_times.append(processing_time_ms)
        self.metrics.average_processing_time_ms = sum(self.processing_times) / len(self.processing_times)
    
    def add_delivery_time(self, delivery_time_ms: float):
        """Add delivery time measurement"""
        self.delivery_times.append(delivery_time_ms)
        self.metrics.average_delivery_time_ms = sum(self.delivery_times) / len(self.delivery_times)
    
    def update_cache_hit_rate(self):
        """Update cache hit rate"""
        total_requests = self.cache_hits + self.cache_misses
        if total_requests > 0:
            self.metrics.cache_hit_rate = self.cache_hits / total_requests
    
    async def add_to_batch(self, delivery_data: Dict):
        """Add delivery to batch for processing"""
        self.pending_deliveries.append(delivery_data)
        
        # Check if batch is ready for processing
        current_time = time.time()
        if (len(self.pending_deliveries) >= self.batch_size or 
            current_time - self.last_batch_time >= self.batch_timeout):
            await self.process_batch()
    
    async def process_batch(self):
        """Process batch of webhook deliveries"""
        if not self.pending_deliveries:
            return
        
        start_time = time.time()
        
        try:
            # Process deliveries in batch
            db = get_db_sync()
            
            # Bulk insert webhook deliveries
            if self.pending_deliveries:
                delivery_values = []
                for delivery in self.pending_deliveries:
                    delivery_values.append(f"""(
                        '{delivery['webhook_event_id']}',
                        '{delivery['event_type']}',
                        '{delivery['subscriber_url']}',
                        'PENDING',
                        0,
                        '{delivery.get('gaming_category', '')}',
                        '{delivery.get('gaming_action', '')}',
                        {delivery.get('significance_score', 'NULL')},
                        '{delivery['event_payload']}',
                        NOW(),
                        NOW()
                    )""")
                
                bulk_insert_sql = f"""
                    INSERT INTO webhook_deliveries (
                        webhook_event_id, event_type, subscriber_url, delivery_status,
                        delivery_attempts, gaming_category, gaming_action, significance_score,
                        event_payload, created_at, updated_at
                    ) VALUES {','.join(delivery_values)}
                """
                
                db.execute(text(bulk_insert_sql))
                db.commit()
            
            # Clear batch
            processed_count = len(self.pending_deliveries)
            self.pending_deliveries.clear()
            self.last_batch_time = time.time()
            
            # Update metrics
            processing_time = (time.time() - start_time) * 1000
            self.add_processing_time(processing_time)
            self.metrics.total_events_processed += processed_count
            
            logger.info(f"✅ Processed batch of {processed_count} deliveries in {processing_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"❌ Failed to process batch: {e}")
        finally:
            db.close()
    
    async def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        self.update_cache_hit_rate()
        self.metrics.queue_size = len(self.pending_deliveries)
        
        # Get additional metrics from database
        try:
            db = get_db_sync()
            
            # Get delivery statistics
            result = db.execute(text("""
                SELECT 
                    COUNT(*) FILTER (WHERE delivery_status = 'DELIVERED') as successful,
                    COUNT(*) FILTER (WHERE delivery_status = 'FAILED') as failed,
                    AVG(response_time_ms) FILTER (WHERE delivery_status = 'DELIVERED') as avg_response_time
                FROM webhook_deliveries 
                WHERE created_at >= NOW() - INTERVAL '1 hour'
            """))
            
            row = result.fetchone()
            if row:
                self.metrics.successful_deliveries = row[0] or 0
                self.metrics.failed_deliveries = row[1] or 0
                self.metrics.average_delivery_time_ms = float(row[2]) if row[2] else 0.0
            
        except Exception as e:
            logger.error(f"❌ Failed to get performance metrics: {e}")
        finally:
            db.close()
        
        return self.metrics
    
    async def cleanup_expired_cache(self):
        """Clean up expired cache entries"""
        expired_keys = []
        for key, entry in self.cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.info(f"🧹 Cleaned up {len(expired_keys)} expired cache entries")
    
    async def close(self):
        """Close performance optimizer"""
        # Process any remaining batch
        if self.pending_deliveries:
            await self.process_batch()
        
        # Close Redis connection
        if self.redis_client:
            await self.redis_client.close()


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
