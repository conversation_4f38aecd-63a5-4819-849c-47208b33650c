"""
Webhook configuration and settings.
"""

import os
from typing import List, Optional
from dataclasses import dataclass, field


@dataclass
class WebhookConfig:
    """Configuration for webhook system."""
    
    # Basic settings
    enabled: bool = True
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: float = 30.0
    
    # Rate limiting
    rate_limit_per_minute: int = 100
    burst_limit: int = 10
    
    # Security
    require_authentication: bool = True
    webhook_secret: Optional[str] = None
    
    # Internal event bus
    internal_queue_size: int = 1000
    worker_count: int = 4
    
    # External endpoints
    external_endpoints: List[str] = field(default_factory=list)
    
    # Gaming-specific settings
    gaming_events_enabled: bool = True
    nft_events_enabled: bool = True
    token_events_enabled: bool = True
    contract_events_enabled: bool = True
    
    # Filtering thresholds
    min_token_transfer_value: float = 1000.0  # USD
    min_nft_value: float = 100.0  # USD
    
    @classmethod
    def from_env(cls) -> 'WebhookConfig':
        """Create configuration from environment variables."""
        return cls(
            enabled=os.getenv('WEBHOOK_ENABLED', 'true').lower() == 'true',
            max_retries=int(os.getenv('WEBHOOK_MAX_RETRIES', '3')),
            retry_delay=float(os.getenv('WEBHOOK_RETRY_DELAY', '1.0')),
            timeout=float(os.getenv('WEBHOOK_TIMEOUT', '30.0')),
            
            rate_limit_per_minute=int(os.getenv('WEBHOOK_RATE_LIMIT', '100')),
            burst_limit=int(os.getenv('WEBHOOK_BURST_LIMIT', '10')),
            
            require_authentication=os.getenv('WEBHOOK_REQUIRE_AUTH', 'true').lower() == 'true',
            webhook_secret=os.getenv('WEBHOOK_SECRET'),
            
            internal_queue_size=int(os.getenv('WEBHOOK_QUEUE_SIZE', '1000')),
            worker_count=int(os.getenv('WEBHOOK_WORKERS', '4')),
            
            external_endpoints=os.getenv('WEBHOOK_ENDPOINTS', '').split(',') if os.getenv('WEBHOOK_ENDPOINTS') else [],
            
            gaming_events_enabled=os.getenv('WEBHOOK_GAMING_EVENTS', 'true').lower() == 'true',
            nft_events_enabled=os.getenv('WEBHOOK_NFT_EVENTS', 'true').lower() == 'true',
            token_events_enabled=os.getenv('WEBHOOK_TOKEN_EVENTS', 'true').lower() == 'true',
            contract_events_enabled=os.getenv('WEBHOOK_CONTRACT_EVENTS', 'true').lower() == 'true',
            
            min_token_transfer_value=float(os.getenv('WEBHOOK_MIN_TOKEN_VALUE', '1000.0')),
            min_nft_value=float(os.getenv('WEBHOOK_MIN_NFT_VALUE', '100.0')),
        )


# Global configuration instance
webhook_config = WebhookConfig.from_env()
