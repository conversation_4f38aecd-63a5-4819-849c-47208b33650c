#!/usr/bin/env python3
"""
Test script for blockchain scraper functionality
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test database connectivity"""
    try:
        from models.base import engine
        from sqlalchemy import text
        
        logger.info("Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text('SELECT 1'))
            logger.info("✅ Database connection successful")
            return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False

async def test_blockchain_clients():
    """Test blockchain client initialization"""
    try:
        from blockchain.multi_chain_client import multi_chain_manager
        
        logger.info("Testing blockchain clients...")
        
        # Test Ethereum client
        eth_client = multi_chain_manager.get_client('ethereum')
        if eth_client:
            logger.info("✅ Ethereum client initialized")
        else:
            logger.warning("⚠️ Ethereum client not available")
        
        # Test Solana client
        sol_client = multi_chain_manager.get_client('solana')
        if sol_client:
            logger.info("✅ Solana client initialized")
        else:
            logger.warning("⚠️ Solana client not available")
        
        return True
    except Exception as e:
        logger.error(f"❌ Blockchain client test failed: {e}")
        return False

async def test_contract_analysis():
    """Test contract analysis functionality"""
    try:
        from scrapers.blockchain.scraper_manager import blockchain_scraper_manager
        
        logger.info("Testing contract analysis...")
        
        # Test with a known gaming contract (example)
        test_contracts = [
            ("******************************************", "ethereum"),  # CryptoKitties
            ("******************************************", "ethereum"),  # CryptoPunks
        ]
        
        for contract_address, chain in test_contracts:
            logger.info(f"Analyzing contract {contract_address} on {chain}...")
            try:
                result = await blockchain_scraper_manager.analyze_specific_contract(
                    contract_address, chain
                )
                
                if result.get('error'):
                    logger.warning(f"⚠️ Analysis failed for {contract_address}: {result['error']}")
                else:
                    is_gaming = result.get('combined_result', False)
                    confidence = result.get('heuristic_analysis', {}).get('confidence_score', 0)
                    logger.info(f"✅ Contract {contract_address}: Gaming={is_gaming}, Confidence={confidence:.2f}")
                
            except Exception as e:
                logger.error(f"❌ Error analyzing {contract_address}: {e}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Contract analysis test failed: {e}")
        return False

async def test_blockchain_data_insertion():
    """Test inserting blockchain data into database"""
    try:
        from models.gaming import BlockchainData
        from models.base import SessionLocal
        
        logger.info("Testing blockchain data insertion...")
        
        # Create test blockchain data
        test_data = BlockchainData(
            contract_address="******************************************",
            blockchain="ethereum",
            transaction_hash="0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
            block_number=18000000,
            event_type="Transfer",
            event_data={"from": "0x123", "to": "0x456", "value": "1000"},
            gas_used=21000,
            gas_price=20000000000,
            block_timestamp=datetime.now(datetime.UTC)
        )
        
        # Insert into database
        with SessionLocal() as session:
            session.add(test_data)
            session.commit()
            logger.info("✅ Test blockchain data inserted successfully")
            
            # Clean up test data
            session.delete(test_data)
            session.commit()
            logger.info("✅ Test data cleaned up")
        
        return True
    except Exception as e:
        logger.error(f"❌ Blockchain data insertion test failed: {e}")
        return False

async def test_scraper_manager():
    """Test the blockchain scraper manager"""
    try:
        from scrapers.blockchain.scraper_manager import blockchain_scraper_manager
        
        logger.info("Testing blockchain scraper manager...")
        
        # Get stats
        stats = await blockchain_scraper_manager.get_stats()
        logger.info(f"✅ Scraper stats: {stats}")
        
        # Test configuration
        config = blockchain_scraper_manager.config
        logger.info(f"✅ Scraper config: chains={config.chains}, mode={config.mode.value}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Scraper manager test failed: {e}")
        return False

async def run_blockchain_scraper_test():
    """Run a short blockchain scraping test"""
    try:
        from scrapers.blockchain.scraper_manager import blockchain_scraper_manager
        
        logger.info("🚀 Starting blockchain scraper test...")
        
        # Start scraping for a short period
        scraping_task = asyncio.create_task(blockchain_scraper_manager.start_scraping())
        
        # Let it run for 30 seconds
        await asyncio.sleep(30)
        
        # Stop scraping
        await blockchain_scraper_manager.stop_scraping()
        
        # Cancel the task if it's still running
        if not scraping_task.done():
            scraping_task.cancel()
            try:
                await scraping_task
            except asyncio.CancelledError:
                pass
        
        # Get final stats
        stats = await blockchain_scraper_manager.get_stats()
        logger.info(f"✅ Scraping test completed. Final stats: {stats}")
        
        return True
    except Exception as e:
        logger.error(f"❌ Blockchain scraper test failed: {e}")
        return False

async def main():
    """Main test function"""
    logger.info("🧪 Starting Web3 Gaming Blockchain Scraper Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Blockchain Clients", test_blockchain_clients),
        ("Contract Analysis", test_contract_analysis),
        ("Database Insertion", test_blockchain_data_insertion),
        ("Scraper Manager", test_scraper_manager),
        ("Blockchain Scraper", run_blockchain_scraper_test),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running test: {test_name}")
        logger.info("-" * 40)
        
        try:
            result = await test_func()
            results[test_name] = result
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Blockchain scraper is ready for production.")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        sys.exit(1)
