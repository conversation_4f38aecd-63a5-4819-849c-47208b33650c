-- Webhook System Performance Optimization
-- Database indexes, constraints, and query optimizations

-- =====================================================
-- WEBHOOK SUBSCRIPTIONS OPTIMIZATION
-- =====================================================

-- Primary indexes for webhook_subscriptions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_active 
ON webhook_subscriptions (is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_verified 
ON webhook_subscriptions (is_verified) WHERE is_verified = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_api_key 
ON webhook_subscriptions (api_key) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_url 
ON webhook_subscriptions (subscriber_url);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_active_verified 
ON webhook_subscriptions (is_active, is_verified) 
WHERE is_active = true AND is_verified = true;

-- GIN indexes for array columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_event_types 
ON webhook_subscriptions USING GIN (event_types);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_blockchain_filters 
ON webhook_subscriptions USING GIN (blockchain_filters);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_gaming_categories 
ON webhook_subscriptions USING GIN (gaming_categories);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_subscriptions_gaming_projects 
ON webhook_subscriptions USING GIN (gaming_projects_filter);

-- =====================================================
-- WEBHOOK DELIVERIES OPTIMIZATION
-- =====================================================

-- Primary indexes for webhook_deliveries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_status 
ON webhook_deliveries (delivery_status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_created_at 
ON webhook_deliveries (created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_event_type 
ON webhook_deliveries (event_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_subscriber_url 
ON webhook_deliveries (subscriber_url);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_gaming_category 
ON webhook_deliveries (gaming_category) WHERE gaming_category IS NOT NULL;

-- Composite indexes for performance queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_url_status_created 
ON webhook_deliveries (subscriber_url, delivery_status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_status_created 
ON webhook_deliveries (delivery_status, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_gaming_category_created 
ON webhook_deliveries (gaming_category, created_at DESC) 
WHERE gaming_category IS NOT NULL;

-- Partial indexes for failed deliveries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_failed 
ON webhook_deliveries (created_at DESC, delivery_attempts) 
WHERE delivery_status = 'FAILED';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_deliveries_pending 
ON webhook_deliveries (created_at ASC) 
WHERE delivery_status = 'PENDING';

-- =====================================================
-- GAMING DATA OPTIMIZATION
-- =====================================================

-- Gaming contracts indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_contracts_address_blockchain 
ON gaming_contracts (contract_address, blockchain);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_contracts_gaming_active 
ON gaming_contracts (is_gaming, is_monitored) 
WHERE is_gaming = true AND is_monitored = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_contracts_confidence 
ON gaming_contracts (confidence_level, confidence_score) 
WHERE is_gaming = true;

-- Gaming projects indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_projects_active 
ON gaming_projects (is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_projects_blockchain 
ON gaming_projects (blockchain);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_projects_genre 
ON gaming_projects (primary_genre) WHERE primary_genre IS NOT NULL;

-- Text search indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_gaming_projects_name_search 
ON gaming_projects USING GIN (to_tsvector('english', project_name));

-- Blockchain data indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blockchain_data_contract_blockchain 
ON blockchain_data (contract_address, blockchain);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blockchain_data_timestamp 
ON blockchain_data (block_timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blockchain_data_event_type 
ON blockchain_data (event_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_blockchain_data_gaming_project 
ON blockchain_data (gaming_project_id) WHERE gaming_project_id IS NOT NULL;

-- =====================================================
-- PERFORMANCE VIEWS
-- =====================================================

-- Optimized webhook analytics view
CREATE OR REPLACE VIEW webhook_analytics_optimized AS
SELECT 
    DATE(wd.created_at) as delivery_date,
    wd.event_type,
    wd.gaming_category,
    COUNT(*) as total_deliveries,
    COUNT(*) FILTER (WHERE wd.delivery_status = 'DELIVERED') as successful_deliveries,
    COUNT(*) FILTER (WHERE wd.delivery_status = 'FAILED') as failed_deliveries,
    COUNT(*) FILTER (WHERE wd.delivery_status = 'PENDING') as pending_deliveries,
    ROUND(AVG(wd.response_time_ms) FILTER (WHERE wd.delivery_status = 'DELIVERED'), 2) as avg_response_time_ms,
    ROUND(AVG(wd.significance_score) FILTER (WHERE wd.significance_score IS NOT NULL), 3) as avg_significance_score,
    COUNT(DISTINCT wd.subscriber_url) as unique_subscribers
FROM webhook_deliveries wd
WHERE wd.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(wd.created_at), wd.event_type, wd.gaming_category
ORDER BY delivery_date DESC, total_deliveries DESC;

-- Subscription performance view
CREATE OR REPLACE VIEW subscription_performance_optimized AS
SELECT 
    ws.id,
    ws.subscriber_name,
    ws.subscriber_url,
    ws.is_active,
    ws.is_verified,
    ws.rate_limit_per_minute,
    COUNT(wd.id) as total_deliveries,
    COUNT(wd.id) FILTER (WHERE wd.delivery_status = 'DELIVERED') as successful_deliveries,
    COUNT(wd.id) FILTER (WHERE wd.delivery_status = 'FAILED') as failed_deliveries,
    CASE 
        WHEN COUNT(wd.id) > 0 
        THEN ROUND(COUNT(wd.id) FILTER (WHERE wd.delivery_status = 'DELIVERED')::numeric / COUNT(wd.id) * 100, 2)
        ELSE 0 
    END as success_rate_percent,
    ROUND(AVG(wd.response_time_ms) FILTER (WHERE wd.delivery_status = 'DELIVERED'), 2) as avg_response_time_ms,
    MAX(wd.created_at) as last_delivery_at,
    ws.created_at as subscription_created_at
FROM webhook_subscriptions ws
LEFT JOIN webhook_deliveries wd ON ws.subscriber_url = wd.subscriber_url 
    AND wd.created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY ws.id, ws.subscriber_name, ws.subscriber_url, ws.is_active, 
         ws.is_verified, ws.rate_limit_per_minute, ws.created_at
ORDER BY total_deliveries DESC;

-- Gaming activity summary view
CREATE OR REPLACE VIEW gaming_activity_summary AS
SELECT 
    DATE(bd.block_timestamp) as activity_date,
    bd.blockchain,
    gc.name as contract_name,
    gp.project_name,
    gp.primary_genre,
    COUNT(*) as total_transactions,
    COUNT(DISTINCT bd.from_address) as unique_from_addresses,
    COUNT(DISTINCT bd.to_address) as unique_to_addresses,
    SUM(bd.token_price_usd) as total_volume_usd,
    AVG(bd.token_price_usd) as avg_transaction_value_usd
FROM blockchain_data bd
LEFT JOIN gaming_contracts gc ON bd.contract_address = gc.contract_address 
    AND bd.blockchain = gc.blockchain
LEFT JOIN gaming_projects gp ON bd.gaming_project_id = gp.id
WHERE bd.block_timestamp >= CURRENT_DATE - INTERVAL '7 days'
    AND gc.is_gaming = true
GROUP BY DATE(bd.block_timestamp), bd.blockchain, gc.name, gp.project_name, gp.primary_genre
ORDER BY activity_date DESC, total_transactions DESC;

-- =====================================================
-- QUERY OPTIMIZATION FUNCTIONS
-- =====================================================

-- Function to get active gaming subscriptions efficiently
CREATE OR REPLACE FUNCTION get_active_gaming_subscriptions(
    p_event_type TEXT DEFAULT NULL,
    p_gaming_category TEXT DEFAULT NULL,
    p_blockchain TEXT DEFAULT NULL
) RETURNS TABLE (
    subscription_id INTEGER,
    subscriber_url TEXT,
    webhook_secret TEXT,
    event_types TEXT[],
    gaming_categories TEXT[],
    blockchain_filters TEXT[],
    min_significance_score NUMERIC,
    rate_limit_per_minute INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ws.id,
        ws.subscriber_url,
        ws.webhook_secret,
        ws.event_types,
        ws.gaming_categories,
        ws.blockchain_filters,
        ws.min_significance_score,
        ws.rate_limit_per_minute
    FROM webhook_subscriptions ws
    WHERE ws.is_active = true 
        AND ws.is_verified = true
        AND (p_event_type IS NULL OR p_event_type = ANY(ws.event_types))
        AND (p_gaming_category IS NULL OR p_gaming_category = ANY(ws.gaming_categories))
        AND (p_blockchain IS NULL OR p_blockchain = ANY(ws.blockchain_filters));
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get gaming contract info efficiently
CREATE OR REPLACE FUNCTION get_gaming_contract_info(
    p_contract_address TEXT,
    p_blockchain TEXT
) RETURNS TABLE (
    contract_id INTEGER,
    contract_name TEXT,
    symbol TEXT,
    contract_type TEXT,
    is_gaming BOOLEAN,
    confidence_level TEXT,
    confidence_score NUMERIC,
    gaming_project_id INTEGER,
    project_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        gc.id,
        gc.name,
        gc.symbol,
        gc.contract_type,
        gc.is_gaming,
        gc.confidence_level,
        gc.confidence_score,
        gp.id,
        gp.project_name
    FROM gaming_contracts gc
    LEFT JOIN gaming_projects gp ON gc.name ILIKE '%' || gp.project_name || '%'
    WHERE gc.contract_address = p_contract_address 
        AND gc.blockchain = p_blockchain
        AND gc.is_gaming = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql STABLE;

-- =====================================================
-- MAINTENANCE PROCEDURES
-- =====================================================

-- Procedure to clean up old webhook deliveries
CREATE OR REPLACE FUNCTION cleanup_old_webhook_deliveries(
    p_days_to_keep INTEGER DEFAULT 90
) RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM webhook_deliveries 
    WHERE created_at < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep
        AND delivery_status IN ('DELIVERED', 'FAILED');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Procedure to update subscription statistics
CREATE OR REPLACE FUNCTION update_subscription_statistics() RETURNS VOID AS $$
BEGIN
    UPDATE webhook_subscriptions ws
    SET 
        total_deliveries = stats.total_deliveries,
        successful_deliveries = stats.successful_deliveries,
        failed_deliveries = stats.failed_deliveries,
        last_delivery_at = stats.last_delivery_at,
        updated_at = CURRENT_TIMESTAMP
    FROM (
        SELECT 
            wd.subscriber_url,
            COUNT(*) as total_deliveries,
            COUNT(*) FILTER (WHERE wd.delivery_status = 'DELIVERED') as successful_deliveries,
            COUNT(*) FILTER (WHERE wd.delivery_status = 'FAILED') as failed_deliveries,
            MAX(wd.created_at) as last_delivery_at
        FROM webhook_deliveries wd
        WHERE wd.created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY wd.subscriber_url
    ) stats
    WHERE ws.subscriber_url = stats.subscriber_url;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- View for monitoring query performance
CREATE OR REPLACE VIEW webhook_query_performance AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals,
    most_common_freqs
FROM pg_stats 
WHERE schemaname = 'public' 
    AND tablename IN ('webhook_subscriptions', 'webhook_deliveries', 'gaming_contracts', 'gaming_projects')
ORDER BY tablename, attname;

-- Index usage statistics
CREATE OR REPLACE VIEW webhook_index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public' 
    AND tablename IN ('webhook_subscriptions', 'webhook_deliveries', 'gaming_contracts', 'gaming_projects')
ORDER BY idx_scan DESC;
