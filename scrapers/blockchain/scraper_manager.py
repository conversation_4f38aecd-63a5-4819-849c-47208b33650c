"""
Blockchain Scraper Manager
Coordinates all blockchain scraping components and provides unified interface
"""
import asyncio
import logging
import os
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import json

from .contract_detector import gaming_contract_detector, ContractAnalysisResult, ContractConfidence
from .event_scraper import blockchain_event_scraper, GameEvent, GameEventType
from .real_time_monitor import real_time_monitor
from .ml_classifier import gaming_ml_classifier, ClassificationResult
from blockchain.multi_chain_client import multi_chain_manager
from models.gaming import BlockchainData
from services.gaming_project_creator import gaming_project_creator
from config.settings import get_settings
from webhooks.event_bus import event_bus, emit_blockchain_event, emit_gaming_event
from webhooks.events import WebhookEventType

settings = get_settings()
logger = logging.getLogger(__name__)

# Detection log file path
DETECTION_LOG_FILE = "blockchain_gaming_detection_log.txt"


class ScrapingMode(Enum):
    """Scraping operation modes"""
    HISTORICAL = "historical"
    REAL_TIME = "real_time"
    HYBRID = "hybrid"


@dataclass
class ScrapingConfig:
    """Configuration for blockchain scraping"""
    chains: List[str]
    mode: ScrapingMode
    enable_ml_classification: bool = True
    enable_real_time_monitoring: bool = True
    historical_blocks_back: int = 1000
    contract_scan_interval: int = 600  # 10 minutes
    event_batch_size: int = 100
    max_concurrent_requests: int = 5


@dataclass
class ScrapingStats:
    """Statistics for scraping operations"""
    contracts_analyzed: int = 0
    gaming_contracts_found: int = 0
    events_processed: int = 0
    errors_encountered: int = 0
    last_update: datetime = None
    chains_active: List[str] = None


class BlockchainScraperManager:
    """Main manager for blockchain scraping operations"""
    
    def __init__(self, config: ScrapingConfig = None):
        self.config = config or ScrapingConfig(
            chains=['ethereum', 'polygon', 'bsc', 'solana'],
            mode=ScrapingMode.HYBRID
        )
        self.stats = ScrapingStats(chains_active=[])
        self.is_running = False
        self.tasks: List[asyncio.Task] = []
        self.discovered_contracts: Set[str] = set()
        
        # Event callbacks
        self.event_callbacks = []
        self.contract_callbacks = []
    
    async def start_scraping(self):
        """Start the blockchain scraping system"""
        if self.is_running:
            logger.warning("Scraping already running")
            return
        
        logger.info(f"Starting blockchain scraping in {self.config.mode.value} mode")
        logger.info(f"Monitoring chains: {self.config.chains}")

        self.is_running = True
        self.stats.chains_active = self.config.chains.copy()

        # Start webhook system
        await event_bus.start()
        from webhooks.manager import webhook_manager
        await webhook_manager.start()
        logger.info("✅ Webhook system started")

        try:
            # Start different components based on mode
            if self.config.mode in [ScrapingMode.HISTORICAL, ScrapingMode.HYBRID]:
                await self._start_historical_scraping()
            
            if self.config.mode in [ScrapingMode.REAL_TIME, ScrapingMode.HYBRID]:
                await self._start_real_time_monitoring()
            
            # Start periodic tasks
            self.tasks.append(asyncio.create_task(self._periodic_contract_discovery()))
            self.tasks.append(asyncio.create_task(self._periodic_stats_update()))
            
            # Wait for all tasks
            await asyncio.gather(*self.tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"Error in scraping system: {e}")
        finally:
            self.is_running = False
    
    async def stop_scraping(self):
        """Stop the blockchain scraping system"""
        logger.info("Stopping blockchain scraping system")
        self.is_running = False
        
        # Cancel all tasks
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # Stop real-time monitoring
        if self.config.enable_real_time_monitoring:
            await real_time_monitor.stop_monitoring()
        
        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.tasks.clear()
        logger.info("Blockchain scraping system stopped")
    
    async def _start_historical_scraping(self):
        """Start historical data scraping"""
        logger.info("Starting historical blockchain scraping")
        
        for chain in self.config.chains:
            task = asyncio.create_task(self._scrape_historical_chain(chain))
            self.tasks.append(task)
    
    async def _start_real_time_monitoring(self):
        """Start real-time event monitoring"""
        if not self.config.enable_real_time_monitoring:
            return
        
        logger.info("Starting real-time blockchain monitoring")
        
        # Add event callback
        real_time_monitor.add_event_callback(self._handle_real_time_event)
        
        # Start monitoring
        task = asyncio.create_task(real_time_monitor.start_monitoring(self.config.chains))
        self.tasks.append(task)
    
    async def _scrape_historical_chain(self, chain: str):
        """Scrape historical data for a specific chain"""
        logger.info(f"Starting historical scraping for {chain}")
        
        try:
            client = multi_chain_manager.get_client(chain)
            if not client:
                logger.error(f"No client available for {chain}")
                return
            
            # Get latest block
            latest_block = await client.get_latest_block_number()
            from_block = max(0, latest_block - self.config.historical_blocks_back)
            
            logger.info(f"Scraping {chain} from block {from_block} to {latest_block}")
            
            # Process blocks in batches
            batch_size = 100
            for start_block in range(from_block, latest_block, batch_size):
                if not self.is_running:
                    break
                
                end_block = min(start_block + batch_size - 1, latest_block)
                await self._process_block_range(chain, start_block, end_block)
                
                # Small delay to avoid overwhelming the RPC
                await asyncio.sleep(1)
            
            logger.info(f"Completed historical scraping for {chain}")
            
        except Exception as e:
            logger.error(f"Error in historical scraping for {chain}: {e}")
            self.stats.errors_encountered += 1
    
    async def _process_block_range(self, chain: str, from_block: int, to_block: int):
        """Process a range of blocks for gaming events and contracts"""
        try:
            # Emit blockchain scanning event
            await emit_blockchain_event(
                event_type=WebhookEventType.BLOCK_MINED,
                blockchain=chain,
                block_number=to_block,
                data={
                    "from_block": from_block,
                    "to_block": to_block,
                    "blocks_processed": to_block - from_block + 1
                }
            )

            # Scan for new contracts
            contracts = await blockchain_event_scraper.scan_new_contracts(chain, from_block)

            # Emit contract discovery events
            for contract in contracts:
                await emit_blockchain_event(
                    event_type=WebhookEventType.CONTRACT_DEPLOYED,
                    blockchain=chain,
                    contract_address=contract.get('contract_address'),
                    block_number=contract.get('block_number'),
                    data=contract
                )

            # Process events in the range
            events = await blockchain_event_scraper._process_blocks(chain, from_block, to_block)

            # Emit gaming events
            for event in events:
                if hasattr(event, 'event_type') and event.event_type == GameEventType.TOKEN_TRANSFER:
                    await emit_gaming_event(
                        event_type=WebhookEventType.TOKEN_TRANSFER,
                        blockchain=chain,
                        gaming_project=getattr(event, 'project_name', None),
                        gaming_action="token_transfer",
                        data=event.__dict__ if hasattr(event, '__dict__') else {}
                    )
                elif hasattr(event, 'event_type') and event.event_type == GameEventType.NFT_TRANSFER:
                    await emit_gaming_event(
                        event_type=WebhookEventType.NFT_TRANSFERRED,
                        blockchain=chain,
                        gaming_project=getattr(event, 'project_name', None),
                        gaming_action="nft_transfer",
                        data=event.__dict__ if hasattr(event, '__dict__') else {}
                    )

        except Exception as e:
            logger.error(f"Error processing blocks {from_block}-{to_block} on {chain}: {e}")
    
    async def _periodic_contract_discovery(self):
        """Periodically discover new gaming contracts"""
        while self.is_running:
            try:
                logger.debug("Running periodic contract discovery")
                
                for chain in self.config.chains:
                    await self._discover_contracts_on_chain(chain)
                
                # Wait for next discovery cycle
                await asyncio.sleep(self.config.contract_scan_interval)
                
            except Exception as e:
                logger.error(f"Error in periodic contract discovery: {e}")
                await asyncio.sleep(60)
    
    async def _discover_contracts_on_chain(self, chain: str):
        """Discover new gaming contracts on a specific chain"""
        try:
            client = multi_chain_manager.get_client(chain)
            if not client:
                return
            
            # Get recent contract deployments
            latest_block = await client.get_latest_block_number()
            from_block = latest_block - 100  # Check last 100 blocks
            to_block = latest_block

            contract_creations = await client.get_contract_creations(from_block, to_block)
            
            for creation in contract_creations:
                contract_address = creation.get('contractAddress')
                if contract_address and contract_address not in self.discovered_contracts:
                    await self._analyze_new_contract(chain, contract_address)
                    self.discovered_contracts.add(contract_address)
            
        except Exception as e:
            logger.error(f"Error discovering contracts on {chain}: {e}")
    
    async def _analyze_new_contract(self, chain: str, contract_address: str):
        """Analyze a newly discovered contract"""
        try:
            self.stats.contracts_analyzed += 1
            
            # Use heuristic analysis first
            heuristic_result = await gaming_contract_detector.analyze_contract(contract_address, chain)
            
            # Use ML classification if enabled and available
            ml_result = None
            if self.config.enable_ml_classification:
                ml_result = await gaming_ml_classifier.classify_contract(contract_address, chain)
            
            # Combine results
            is_gaming = self._combine_analysis_results(heuristic_result, ml_result)
            
            if is_gaming:
                self.stats.gaming_contracts_found += 1
                logger.info(f"Gaming contract discovered: {contract_address} on {chain}")

                # Create gaming project from contract analysis
                try:
                    gaming_project = await gaming_project_creator.create_project_from_contract(heuristic_result)
                    if gaming_project:
                        logger.info(f"✅ Auto-created gaming project: {gaming_project.project_name}")
                    else:
                        logger.debug(f"No gaming project created for contract {contract_address}")
                except Exception as e:
                    logger.error(f"Error creating gaming project from contract {contract_address}: {e}")

                # Add to monitoring
                real_time_monitor.add_monitored_contract(contract_address)
                blockchain_event_scraper.add_monitored_contract(contract_address)

                # Notify callbacks
                for callback in self.contract_callbacks:
                    try:
                        await callback(chain, contract_address, heuristic_result, ml_result)
                    except Exception as e:
                        logger.error(f"Error in contract callback: {e}")
            
        except Exception as e:
            logger.error(f"Error analyzing contract {contract_address}: {e}")
            self.stats.errors_encountered += 1
    
    def _combine_analysis_results(
        self, 
        heuristic_result: ContractAnalysisResult, 
        ml_result: Optional[ClassificationResult]
    ) -> bool:
        """Combine heuristic and ML analysis results"""
        # Start with heuristic result
        if heuristic_result.confidence in [ContractConfidence.HIGH, ContractConfidence.MEDIUM]:
            return True
        
        # Use ML result if available
        if ml_result and ml_result.is_gaming_probability > 0.7:
            return True
        
        # Conservative approach - require strong evidence
        return False
    
    async def _handle_real_time_event(self, event: GameEvent):
        """Handle real-time gaming events"""
        try:
            self.stats.events_processed += 1
            logger.debug(f"Real-time event: {event.event_name} on {event.blockchain}")
            
            # Notify callbacks
            for callback in self.event_callbacks:
                try:
                    await callback(event)
                except Exception as e:
                    logger.error(f"Error in event callback: {e}")
            
        except Exception as e:
            logger.error(f"Error handling real-time event: {e}")
    
    async def _periodic_stats_update(self):
        """Periodically update statistics"""
        while self.is_running:
            try:
                self.stats.last_update = datetime.utcnow()
                
                # Log stats periodically
                logger.info(
                    f"Scraping stats - Contracts: {self.stats.contracts_analyzed}, "
                    f"Gaming: {self.stats.gaming_contracts_found}, "
                    f"Events: {self.stats.events_processed}, "
                    f"Errors: {self.stats.errors_encountered}"
                )
                
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                logger.error(f"Error updating stats: {e}")
                await asyncio.sleep(60)
    
    def add_event_callback(self, callback):
        """Add callback for gaming events"""
        self.event_callbacks.append(callback)
    
    def add_contract_callback(self, callback):
        """Add callback for discovered gaming contracts"""
        self.contract_callbacks.append(callback)
    
    async def get_stats(self) -> ScrapingStats:
        """Get current scraping statistics"""
        return self.stats
    
    async def analyze_specific_contract(self, contract_address: str, chain: str) -> Dict[str, Any]:
        """Analyze a specific contract on demand"""
        try:
            # Handle Solana differently - use Solscan API analysis
            if chain.lower() in ['solana', 'sol']:
                return await self._analyze_solana_account(contract_address, chain)

            # Run both analyses for EVM chains
            heuristic_result = await gaming_contract_detector.analyze_contract(contract_address, chain)
            ml_result = None

            if self.config.enable_ml_classification:
                ml_result = await gaming_ml_classifier.classify_contract(contract_address, chain)

            combined_result = self._combine_analysis_results(heuristic_result, ml_result)

            # Create combined result dict for logging
            combined_result_dict = {
                'is_gaming': combined_result,
                'confidence_score': heuristic_result.confidence_score if heuristic_result else 0.0
            }

            # Log the detection to our tracking file
            await self._log_detection(contract_address, chain, combined_result_dict, heuristic_result, ml_result)

            return {
                'contract_address': contract_address,
                'blockchain': chain,
                'heuristic_analysis': {
                    'is_gaming': heuristic_result.is_gaming,
                    'confidence': heuristic_result.confidence.value,
                    'confidence_score': heuristic_result.confidence_score,
                    'detected_patterns': heuristic_result.detected_patterns
                },
                'ml_analysis': {
                    'is_gaming_probability': ml_result.is_gaming_probability if ml_result else None,
                    'confidence_score': ml_result.confidence_score if ml_result else None,
                    'feature_importance': ml_result.feature_importance if ml_result else None
                } if ml_result else None,
                'combined_result': combined_result,
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing contract {contract_address}: {e}")
            return {
                'error': str(e),
                'contract_address': contract_address,
                'blockchain': chain
            }

    async def _analyze_solana_account(self, account_address: str, chain: str) -> Dict[str, Any]:
        """Analyze Solana account using Solscan API"""
        try:
            from blockchain.data_clients.solscan import solscan_client

            # Use Solscan for Solana account analysis
            async with solscan_client:
                analysis_result = await solscan_client.analyze_account_for_gaming(account_address)

            if analysis_result and not analysis_result.get('error'):
                confidence_score = analysis_result.get('confidence_score', 0.0)
                gaming_indicators = analysis_result.get('gaming_indicators', [])
                is_gaming = confidence_score >= 0.3  # Lower threshold for Solana

                # Create result dict for logging
                combined_result_dict = {
                    'is_gaming': is_gaming,
                    'confidence_score': confidence_score
                }

                # Create mock heuristic result for logging compatibility
                class MockHeuristicResult:
                    def __init__(self, confidence_score, detected_patterns):
                        self.confidence_score = confidence_score
                        self.detected_patterns = detected_patterns

                mock_heuristic = MockHeuristicResult(confidence_score, gaming_indicators)

                # Log the detection
                await self._log_detection(account_address, chain, combined_result_dict, mock_heuristic, None)

                return {
                    'contract_address': account_address,
                    'blockchain': chain,
                    'heuristic_analysis': {
                        'is_gaming': is_gaming,
                        'confidence': 'high' if confidence_score >= 0.7 else 'medium' if confidence_score >= 0.3 else 'low',
                        'confidence_score': confidence_score,
                        'detected_patterns': gaming_indicators
                    },
                    'ml_analysis': None,
                    'combined_result': is_gaming,
                    'analysis_timestamp': analysis_result.get('analysis_timestamp'),
                    'solscan_data': analysis_result
                }
            else:
                # Return negative result
                return {
                    'contract_address': account_address,
                    'blockchain': chain,
                    'heuristic_analysis': {
                        'is_gaming': False,
                        'confidence': 'none',
                        'confidence_score': 0.0,
                        'detected_patterns': []
                    },
                    'ml_analysis': None,
                    'combined_result': False,
                    'analysis_timestamp': datetime.utcnow().isoformat(),
                    'error': analysis_result.get('error') if analysis_result else 'No analysis result'
                }

        except Exception as e:
            logger.error(f"Error analyzing Solana account {account_address}: {e}")
            return {
                'contract_address': account_address,
                'blockchain': chain,
                'heuristic_analysis': {
                    'is_gaming': False,
                    'confidence': 'none',
                    'confidence_score': 0.0,
                    'detected_patterns': []
                },
                'ml_analysis': None,
                'combined_result': False,
                'analysis_timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            }

    async def _log_detection(self, contract_address: str, chain: str, combined_result: Dict[str, Any],
                           heuristic_result: ContractAnalysisResult, ml_result: Optional[ClassificationResult]):
        """Log gaming contract detection to tracking file"""
        try:
            timestamp = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
            confidence = combined_result.get('confidence_score', 0.0)
            is_gaming = combined_result.get('is_gaming', False)

            # Determine classification
            if confidence >= 0.7:
                classification = "GAMING" if is_gaming else "NON_GAMING"
            else:
                classification = "UNCERTAIN"

            # Extract key features for logging
            features = []
            if heuristic_result.detected_patterns:
                features.extend(heuristic_result.detected_patterns[:3])  # Top 3 patterns

            if ml_result and ml_result.feature_importance:
                # Add top ML features
                top_features = sorted(ml_result.feature_importance.items(),
                                    key=lambda x: x[1], reverse=True)[:2]
                features.extend([f[0] for f in top_features])

            features_str = ",".join(features) if features else "NO_FEATURES"

            # Format log entry
            log_entry = f"[{timestamp}] {chain.upper()} {contract_address} {confidence:.2f} {classification} {features_str}\n"

            # Append to detection log file
            with open(DETECTION_LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(log_entry)

            # Update statistics in the file if it's a gaming contract
            if is_gaming and confidence >= 0.7:
                await self._update_detection_stats()

        except Exception as e:
            logger.error(f"Error logging detection for {contract_address}: {e}")

    async def _update_detection_stats(self):
        """Update statistics section in detection log file"""
        try:
            # Read current file
            if not os.path.exists(DETECTION_LOG_FILE):
                return

            with open(DETECTION_LOG_FILE, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Count gaming contracts from log entries
            gaming_count = 0
            total_count = 0
            confidence_sum = 0.0

            for line in lines:
                if line.startswith('[') and 'GAMING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        try:
                            confidence = float(parts[3])
                            confidence_sum += confidence
                            if 'GAMING' in parts[4] and parts[4] != 'NON_GAMING':
                                gaming_count += 1
                            total_count += 1
                        except (ValueError, IndexError):
                            continue

            avg_confidence = confidence_sum / total_count if total_count > 0 else 0.0

            # Update statistics section
            stats_section = f"""
================================================================================
STATISTICS
================================================================================

Total Contracts Analyzed: {total_count}
Gaming Contracts Found: {gaming_count}
Non-Gaming Contracts: {total_count - gaming_count}
Uncertain Classifications: 0
Average Gaming Confidence: {avg_confidence:.2f}
Last Update: {datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")}
"""

            # Find and replace statistics section
            new_lines = []
            in_stats = False
            stats_written = False

            for line in lines:
                if "STATISTICS" in line and "=" in line:
                    in_stats = True
                    if not stats_written:
                        new_lines.append(stats_section)
                        stats_written = True
                elif in_stats and line.startswith("================================================================================"):
                    in_stats = False
                    new_lines.append(line)
                elif not in_stats:
                    new_lines.append(line)

            # Write updated file
            with open(DETECTION_LOG_FILE, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)

        except Exception as e:
            logger.error(f"Error updating detection stats: {e}")


# Global instance with default configuration
default_config = ScrapingConfig(
    chains=['ethereum', 'polygon', 'bsc', 'solana'],
    mode=ScrapingMode.HYBRID,
    enable_ml_classification=True,
    enable_real_time_monitoring=True
)

blockchain_scraper_manager = BlockchainScraperManager(default_config)
