[package]
name = "analytics-engine"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
# Workspace dependencies
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
chrono.workspace = true

# Database
sqlx.workspace = true
redis.workspace = true

# Performance
rayon.workspace = true
dashmap.workspace = true

# High-performance data processing with Polars
polars = { version = "0.41", features = ["lazy", "temporal", "dtype-date"] }

# CPU detection for optimal threading
num_cpus = "1.0"

# ONNX Runtime for ML inference (optional for now)
# ort = { version = "1.16", features = ["ndarray"], optional = true }
# ndarray = { version = "0.15", optional = true }

# ML and data processing (removed for now)
# candle-core.workspace = true
# candle-nn.workspace = true
# ndarray.workspace = true

# Shared crates
data-models = { path = "../data-models" }
shared-utils = { path = "../shared-utils" }

[dev-dependencies]
mockall.workspace = true
