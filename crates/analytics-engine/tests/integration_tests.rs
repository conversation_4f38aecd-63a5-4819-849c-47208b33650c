//! Comprehensive Integration Tests for Analytics Engine
//! 
//! COMPONENT_ESSENCE::APOLLO_INTEGRATION_TEST_ORACLE
//! DANGER_LEVEL::ATHENA_VALIDATION_WISDOM_CRITICAL
//! PERFORMANCE_TARGET::FULL_ANALYTICS_ENGINE_VALIDATION
//! LAST_MODIFIED::ANALYTICS_ENGINE_COMPILATION_COMPLETE

use analytics_engine::*;
use anyhow::Result;
use serde_json::json;
use std::collections::HashMap;

/// Test data for analytics engine validation
#[derive(Debug)]
struct TestGameData {
    pub player_count: u64,
    pub transaction_volume: f64,
    pub retention_rate: f64,
    pub avg_session_duration: f64,
}

impl TestGameData {
    fn new() -> Self {
        Self {
            player_count: 15000,
            transaction_volume: 5000000.0,
            retention_rate: 0.75,
            avg_session_duration: 45.5,
        }
    }
}

#[tokio::test]
async fn test_analytics_engine_initialization() -> Result<()> {
    println!("🔧 Testing Analytics Engine Initialization");

    let engine = AnalyticsEngine::new().await?;
    
    // Test that semantic protection is working
    let health = engine.get_semantic_health();
    assert!(health.system_uptime <= chrono::Utc::now());
    
    println!("✅ Analytics Engine initialized successfully");
    Ok(())
}

#[tokio::test]
async fn test_polars_analytics_engine() -> Result<()> {
    println!("📊 Testing Polars Analytics Engine");

    let engine = PolarsAnalyticsEngine::new().await?;
    
    // Test basic functionality
    let metrics = engine.get_performance_metrics();
    assert_eq!(metrics.operations_count.load(std::sync::atomic::Ordering::Relaxed), 0);
    
    println!("✅ Polars Analytics Engine validated");
    Ok(())
}

#[tokio::test]
async fn test_ml_engine_predictions() -> Result<()> {
    println!("🤖 Testing ML Engine Predictions");

    let mut ml_engine = MLEngine::new().await?;
    
    // Test player behavior prediction
    let mut features = HashMap::new();
    features.insert("activity_score".to_string(), 0.8);
    features.insert("engagement_time".to_string(), 60.0);
    features.insert("transaction_frequency".to_string(), 2.5);
    
    let request = MLPredictionRequest {
        model_type: MLModelType::PlayerBehaviorPrediction,
        features,
        player_id: Some("test_player_123".to_string()),
        game_id: Some("test_game_456".to_string()),
        context: None,
    };
    
    let result = ml_engine.predict(request).await?;
    
    assert!(result.prediction >= 0.0 && result.prediction <= 1.0);
    assert!(result.confidence >= 0.0 && result.confidence <= 1.0);
    assert!(result.semantic_validation_passed);
    assert!(result.inference_time_ms > 0);
    
    println!("✅ ML Engine predictions validated");
    println!("   Prediction: {:.3}", result.prediction);
    println!("   Confidence: {:.3}", result.confidence);
    Ok(())
}

#[tokio::test]
async fn test_core_analytics_calculations() -> Result<()> {
    println!("🧮 Testing Core Analytics Calculations");

    let mut analytics_engine = CoreAnalyticsEngine::new().await?;
    
    // Test Player Lifetime Value calculation
    let mut input_data = HashMap::new();
    input_data.insert("avg_session_value".to_string(), json!(15.0));
    input_data.insert("sessions_per_month".to_string(), json!(25.0));
    input_data.insert("retention_months".to_string(), json!(8.0));
    input_data.insert("churn_rate".to_string(), json!(0.15));
    
    let request = AnalyticsRequest {
        algorithm_type: AlgorithmType::PlayerLifetimeValue,
        input_data,
        parameters: HashMap::new(),
        timeframe: Some("monthly".to_string()),
        filters: None,
    };
    
    let result = analytics_engine.calculate(request).await?;
    
    assert!(result.result_value > 0.0);
    assert!(result.confidence >= 0.0 && result.confidence <= 1.0);
    assert!(result.semantic_validation_passed);
    assert_eq!(result.metadata.data_points_used, 4);
    
    println!("✅ Core Analytics calculations validated");
    println!("   LTV Result: ${:.2}", result.result_value);
    println!("   Confidence: {:.3}", result.confidence);
    Ok(())
}

#[tokio::test]
async fn test_game_popularity_index() -> Result<()> {
    println!("🎮 Testing Game Popularity Index");

    let mut analytics_engine = CoreAnalyticsEngine::new().await?;
    
    let mut input_data = HashMap::new();
    input_data.insert("daily_active_users".to_string(), json!(5000.0));
    input_data.insert("transaction_volume".to_string(), json!(2500000.0));
    input_data.insert("social_mentions".to_string(), json!(500.0));
    input_data.insert("retention_rate".to_string(), json!(0.85));
    
    let request = AnalyticsRequest {
        algorithm_type: AlgorithmType::GamePopularityIndex,
        input_data,
        parameters: HashMap::new(),
        timeframe: Some("daily".to_string()),
        filters: None,
    };
    
    let result = analytics_engine.calculate(request).await?;
    
    assert!(result.result_value > 0.0);
    assert!(result.confidence >= 0.0);
    assert!(result.semantic_validation_passed);
    
    println!("✅ Game Popularity Index validated");
    println!("   Popularity Score: {:.2}", result.result_value);
    Ok(())
}

#[tokio::test]
async fn test_whale_detection_ml() -> Result<()> {
    println!("🐋 Testing Whale Detection ML");

    let mut ml_engine = MLEngine::new().await?;
    
    // Test whale detection with high-value features
    let mut features = HashMap::new();
    features.insert("total_volume_usd".to_string(), 500000.0);
    features.insert("transaction_count".to_string(), 150.0);
    features.insert("avg_transaction_size".to_string(), 25000.0);
    
    let request = MLPredictionRequest {
        model_type: MLModelType::WhaleDetection,
        features,
        player_id: Some("potential_whale_789".to_string()),
        game_id: None,
        context: Some(json!({"analysis_type": "whale_detection"})),
    };
    
    let result = ml_engine.predict(request).await?;
    
    // High-value features should result in high whale probability
    assert!(result.prediction > 0.5);
    assert!(result.confidence > 0.8);
    assert!(result.semantic_validation_passed);
    
    println!("✅ Whale Detection ML validated");
    println!("   Whale Probability: {:.3}", result.prediction);
    Ok(())
}

#[tokio::test]
async fn test_churn_prediction() -> Result<()> {
    println!("📉 Testing Churn Prediction");

    let mut ml_engine = MLEngine::new().await?;
    
    // Test churn prediction with inactive player features
    let mut features = HashMap::new();
    features.insert("days_since_last_login".to_string(), 15.0);
    features.insert("session_count_7d".to_string(), 2.0);
    features.insert("avg_session_duration".to_string(), 10.0);
    
    let request = MLPredictionRequest {
        model_type: MLModelType::ChurnPrediction,
        features,
        player_id: Some("inactive_player_456".to_string()),
        game_id: Some("test_game".to_string()),
        context: None,
    };
    
    let result = ml_engine.predict(request).await?;
    
    // Inactive features should result in higher churn probability
    assert!(result.prediction >= 0.0 && result.prediction <= 1.0);
    assert!(result.confidence > 0.0);
    assert!(result.semantic_validation_passed);
    
    println!("✅ Churn Prediction validated");
    println!("   Churn Probability: {:.3}", result.prediction);
    Ok(())
}

#[tokio::test]
async fn test_token_velocity_analysis() -> Result<()> {
    println!("💰 Testing Token Velocity Analysis");

    let mut analytics_engine = CoreAnalyticsEngine::new().await?;
    
    let mut input_data = HashMap::new();
    input_data.insert("transaction_volume".to_string(), json!(10000000.0));
    input_data.insert("circulating_supply".to_string(), json!(50000000.0));
    input_data.insert("avg_hold_time_days".to_string(), json!(45.0));
    
    let request = AnalyticsRequest {
        algorithm_type: AlgorithmType::TokenVelocityAnalysis,
        input_data,
        parameters: HashMap::new(),
        timeframe: Some("monthly".to_string()),
        filters: None,
    };
    
    let result = analytics_engine.calculate(request).await?;
    
    assert!(result.result_value > 0.0);
    assert!(result.confidence >= 0.0);
    assert!(result.semantic_validation_passed);
    
    println!("✅ Token Velocity Analysis validated");
    println!("   Velocity: {:.4}", result.result_value);
    Ok(())
}

#[tokio::test]
async fn test_semantic_protection_integration() -> Result<()> {
    println!("🛡️ Testing Semantic Protection Integration");

    let mut ml_engine = MLEngine::new().await?;
    let mut analytics_engine = CoreAnalyticsEngine::new().await?;
    
    // Test that semantic protection is active
    let ml_health = ml_engine.get_semantic_health();
    let analytics_health = analytics_engine.get_semantic_health();
    
    assert!(ml_health.total_validations >= 0);
    assert!(analytics_health.total_validations >= 0);
    
    // Test that both engines can handle multiple operations
    for i in 0..5 {
        let mut features = HashMap::new();
        features.insert("test_feature".to_string(), i as f64);
        
        let ml_request = MLPredictionRequest {
            model_type: MLModelType::GameRecommendation,
            features,
            player_id: Some(format!("test_player_{}", i)),
            game_id: None,
            context: None,
        };
        
        let _ml_result = ml_engine.predict(ml_request).await?;
        
        let mut input_data = HashMap::new();
        input_data.insert("test_value".to_string(), json!(i as f64 * 10.0));
        
        let analytics_request = AnalyticsRequest {
            algorithm_type: AlgorithmType::GameEconomyHealth,
            input_data,
            parameters: HashMap::new(),
            timeframe: None,
            filters: None,
        };
        
        let _analytics_result = analytics_engine.calculate(analytics_request).await?;
    }
    
    println!("✅ Semantic Protection Integration validated");
    println!("   ML validations: {}", ml_engine.get_semantic_health().total_validations);
    println!("   Analytics validations: {}", analytics_engine.get_semantic_health().total_validations);
    Ok(())
}

#[tokio::test]
async fn test_performance_metrics() -> Result<()> {
    println!("⚡ Testing Performance Metrics");

    let mut ml_engine = MLEngine::new().await?;
    let mut analytics_engine = CoreAnalyticsEngine::new().await?;
    
    // Perform some operations to generate metrics
    let features = HashMap::from([
        ("test_metric".to_string(), 1.0),
    ]);
    
    let ml_request = MLPredictionRequest {
        model_type: MLModelType::FraudDetection,
        features,
        player_id: None,
        game_id: None,
        context: None,
    };
    
    let _ml_result = ml_engine.predict(ml_request).await?;
    
    let input_data = HashMap::from([
        ("test_data".to_string(), json!(100.0)),
    ]);
    
    let analytics_request = AnalyticsRequest {
        algorithm_type: AlgorithmType::PlayerRetentionAnalysis,
        input_data,
        parameters: HashMap::new(),
        timeframe: None,
        filters: None,
    };
    
    let _analytics_result = analytics_engine.calculate(analytics_request).await?;
    
    // Check metrics
    let ml_metrics = ml_engine.get_performance_metrics();
    let analytics_metrics = analytics_engine.get_performance_metrics();
    
    assert!(ml_metrics.total_predictions > 0);
    assert!(analytics_metrics.total_calculations > 0);
    
    println!("✅ Performance Metrics validated");
    println!("   ML Predictions: {}", ml_metrics.total_predictions);
    println!("   Analytics Calculations: {}", analytics_metrics.total_calculations);
    Ok(())
}

/// Run all analytics engine integration tests
#[tokio::test]
async fn run_full_analytics_engine_integration_suite() -> Result<()> {
    println!("🧬 Running Full Analytics Engine Integration Suite");
    println!("=" .repeat(60));

    test_analytics_engine_initialization().await?;
    test_polars_analytics_engine().await?;
    test_ml_engine_predictions().await?;
    test_core_analytics_calculations().await?;
    test_game_popularity_index().await?;
    test_whale_detection_ml().await?;
    test_churn_prediction().await?;
    test_token_velocity_analysis().await?;
    test_semantic_protection_integration().await?;
    test_performance_metrics().await?;

    println!("=" .repeat(60));
    println!("🎉 All Analytics Engine Integration Tests PASSED!");
    println!("🚀 Analytics Engine is ready for production deployment!");
    Ok(())
}
