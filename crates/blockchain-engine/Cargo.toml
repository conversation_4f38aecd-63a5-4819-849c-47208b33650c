[package]
name = "blockchain-engine"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
# Workspace dependencies
tokio.workspace = true
reqwest.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
chrono.workspace = true
uuid.workspace = true

# Blockchain specific
ethers.workspace = true
web3.workspace = true

# Solana support via JSON-RPC (avoiding SDK dependency conflicts)
base64.workspace = true
bs58.workspace = true

# Async utilities
futures-util.workspace = true
tokio-util.workspace = true
async-trait = "0.1"

# Performance
dashmap.workspace = true
parking_lot.workspace = true

# Shared crates
data-models = { path = "../data-models" }
shared-utils = { path = "../shared-utils" }

[dev-dependencies]
mockall.workspace = true
wiremock.workspace = true
tokio-test = "0.4"
