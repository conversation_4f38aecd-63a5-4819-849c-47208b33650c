//! High-performance Solana JSON-RPC client implementation
//!
//! COMPONENT_ESSENCE::APOLLO_SOLANA_GAMING_ORACLE
//! DANGER_LEVEL::MEDUSA_DEPENDENCY_CONFLICT_RESOLVED
//! PERFORMANCE_TARGET::SUB_100MS_SOLANA_GAMING_ANALYTICS
//! LAST_MODIFIED::PHASE_2_SOLANA_INTEGRATION_TRIUMPH
//! SOLUTION_TYPE::CUSTOM_JSON_RPC_OVER_OFFICIAL_SDK
//!
//! This module provides Solana blockchain integration using JSON-RPC calls
//! to avoid dependency conflicts with the official Solana SDK.

use anyhow::Result;
use base64::{engine::general_purpose::STANDARD as BASE64, Engine};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use shared_utils::{Address, Chain, Web3GamingError};
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, Semaphore};
use tokio::time::sleep;
use tracing::{debug, info, warn, error};
use crate::gaming_detection::{GamingContractDetector, GamingContractAnalysis};

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub requests_per_second: u32,
    pub burst_capacity: u32,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_second: 100, // Conservative default for Solana RPC
            burst_capacity: 200,
        }
    }
}

/// Connection pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionConfig {
    pub max_connections: usize,
    pub timeout_seconds: u64,
    pub retry_attempts: u32,
    pub retry_delay_ms: u64,
}

impl Default for ConnectionConfig {
    fn default() -> Self {
        Self {
            max_connections: 10,
            timeout_seconds: 30,
            retry_attempts: 3,
            retry_delay_ms: 1000,
        }
    }
}

/// Enhanced Solana JSON-RPC client with connection pooling and rate limiting
#[derive(Debug)]
pub struct SolanaClient {
    rpc_url: String,
    http_client: reqwest::Client,
    rate_limiter: Arc<Semaphore>,
    rate_limit_config: RateLimitConfig,
    connection_config: ConnectionConfig,
    request_counter: AtomicU64,
    last_request_time: Arc<RwLock<Instant>>,
    gaming_detector: Arc<GamingContractDetector>,
}

/// Solana account information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaAccountInfo {
    pub lamports: u64,
    pub owner: String,
    pub executable: bool,
    pub rent_epoch: u64,
    pub data: Vec<String>, // [data, encoding]
}

/// Solana transaction information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaTransaction {
    pub slot: u64,
    pub transaction: SolanaTransactionData,
    pub block_time: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaTransactionData {
    pub signatures: Vec<String>,
    pub message: SolanaMessage,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaMessage {
    #[serde(rename = "accountKeys")]
    pub account_keys: Vec<String>,
    pub instructions: Vec<SolanaInstruction>,
    #[serde(rename = "recentBlockhash")]
    pub recent_blockhash: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaInstruction {
    #[serde(rename = "programIdIndex")]
    pub program_id_index: u8,
    pub accounts: Vec<u8>,
    pub data: String,
}

/// Solana token account information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaTokenAccount {
    pub account: String,
    pub mint: String,
    pub owner: String,
    #[serde(rename = "uiTokenAmount")]
    pub ui_token_amount: SolanaTokenAmount,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaTokenAmount {
    pub amount: String,
    pub decimals: u8,
    #[serde(rename = "uiAmount")]
    pub ui_amount: Option<f64>,
    #[serde(rename = "uiAmountString")]
    pub ui_amount_string: String,
}

/// Gaming-related Solana analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SolanaGamingAnalysis {
    pub address: Address,
    pub is_gaming_related: bool,
    pub confidence_score: f64,
    pub gaming_indicators: Vec<String>,
    pub account_type: String,
    pub token_accounts: Vec<SolanaTokenAccount>,
    pub recent_activity: u32,
}

impl SolanaClient {
    /// Create a new Solana client with default configuration
    pub fn new(rpc_url: impl Into<String>) -> Self {
        Self::with_config(rpc_url, Default::default(), Default::default())
    }

    /// Create a new Solana client with custom configuration
    pub fn with_config(
        rpc_url: impl Into<String>,
        rate_limit_config: RateLimitConfig,
        connection_config: ConnectionConfig,
    ) -> Self {
        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_secs(connection_config.timeout_seconds))
            .pool_max_idle_per_host(connection_config.max_connections)
            .pool_idle_timeout(Duration::from_secs(60))
            .tcp_keepalive(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        let rate_limiter = Arc::new(Semaphore::new(rate_limit_config.burst_capacity as usize));
        let gaming_detector = Arc::new(GamingContractDetector::new());

        Self {
            rpc_url: rpc_url.into(),
            http_client,
            rate_limiter,
            rate_limit_config,
            connection_config,
            request_counter: AtomicU64::new(0),
            last_request_time: Arc::new(RwLock::new(Instant::now())),
            gaming_detector,
        }
    }

    /// Apply rate limiting before making requests
    async fn apply_rate_limit(&self) -> Result<()> {
        // Acquire semaphore permit
        let _permit = self.rate_limiter.acquire().await
            .map_err(|e| Web3GamingError::blockchain_rpc(format!("Rate limit semaphore error: {}", e)))?;

        // Calculate delay needed to maintain rate limit
        let now = Instant::now();
        let last_request = *self.last_request_time.read().await;
        let min_interval = Duration::from_millis(1000 / self.rate_limit_config.requests_per_second as u64);

        if let Some(time_since_last) = now.checked_duration_since(last_request) {
            if time_since_last < min_interval {
                let delay = min_interval - time_since_last;
                sleep(delay).await;
            }
        }

        // Update last request time
        *self.last_request_time.write().await = Instant::now();

        Ok(())
    }

    /// Make a JSON-RPC call to Solana with retry logic and rate limiting
    async fn rpc_call(&self, method: &str, params: Value) -> Result<Value> {
        self.apply_rate_limit().await?;

        let request_id = self.request_counter.fetch_add(1, Ordering::Relaxed);
        let request_body = json!({
            "jsonrpc": "2.0",
            "id": request_id,
            "method": method,
            "params": params
        });

        let mut last_error = None;

        for attempt in 0..self.connection_config.retry_attempts {
            match self.execute_request(&request_body).await {
                Ok(result) => {
                    if attempt > 0 {
                        info!("Solana RPC call succeeded on attempt {}/{}", attempt + 1, self.connection_config.retry_attempts);
                    }
                    return Ok(result);
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < self.connection_config.retry_attempts - 1 {
                        let delay = Duration::from_millis(
                            self.connection_config.retry_delay_ms * (2_u64.pow(attempt))
                        );
                        warn!("Solana RPC call failed (attempt {}/{}), retrying in {:?}: {}",
                              attempt + 1, self.connection_config.retry_attempts, delay, last_error.as_ref().unwrap());
                        sleep(delay).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(||
            Web3GamingError::blockchain_rpc("All retry attempts failed").into()
        ))
    }

    /// Execute a single RPC request
    async fn execute_request(&self, request_body: &Value) -> Result<Value> {
        let response = self
            .http_client
            .post(&self.rpc_url)
            .json(request_body)
            .send()
            .await
            .map_err(|e| Web3GamingError::blockchain_rpc(format!("HTTP request failed: {}", e)))?;

        if !response.status().is_success() {
            return Err(Web3GamingError::blockchain_rpc(format!(
                "Solana RPC error: HTTP {}", response.status()
            )).into());
        }

        let response_json: Value = response.json().await
            .map_err(|e| Web3GamingError::blockchain_rpc(format!("Failed to parse JSON response: {}", e)))?;

        if let Some(error) = response_json.get("error") {
            return Err(Web3GamingError::blockchain_rpc(format!(
                "Solana RPC error: {}", error
            )).into());
        }

        response_json.get("result")
            .cloned()
            .ok_or_else(|| Web3GamingError::blockchain_rpc("No result in Solana RPC response").into())
    }

    /// Get account information
    pub async fn get_account_info(&self, address: &str) -> Result<Option<SolanaAccountInfo>> {
        let params = json!([
            address,
            {
                "encoding": "base64",
                "commitment": "confirmed"
            }
        ]);

        let result = self.rpc_call("getAccountInfo", params).await?;
        
        if result.is_null() {
            return Ok(None);
        }

        let account_info: SolanaAccountInfo = serde_json::from_value(result.get("value").unwrap().clone())?;
        Ok(Some(account_info))
    }

    /// Get account balance in lamports
    pub async fn get_balance(&self, address: &str) -> Result<u64> {
        let params = json!([address]);
        let result = self.rpc_call("getBalance", params).await?;

        result.get("value")
            .and_then(|v| v.as_u64())
            .ok_or_else(|| Web3GamingError::blockchain_rpc("Invalid balance response").into())
    }

    /// Get token accounts by owner
    pub async fn get_token_accounts_by_owner(&self, owner: &str) -> Result<Vec<SolanaTokenAccount>> {
        let params = json!([
            owner,
            {
                "programId": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"
            },
            {
                "encoding": "jsonParsed",
                "commitment": "confirmed"
            }
        ]);

        let result = self.rpc_call("getTokenAccountsByOwner", params).await?;

        let empty_vec = vec![];
        let accounts = result.get("value")
            .and_then(|v| v.as_array())
            .unwrap_or(&empty_vec);

        let mut token_accounts = Vec::new();
        for account in accounts {
            if let Ok(token_account) = self.parse_token_account(account) {
                token_accounts.push(token_account);
            }
        }

        Ok(token_accounts)
    }

    /// Parse token account from RPC response
    fn parse_token_account(&self, account_data: &Value) -> Result<SolanaTokenAccount> {
        let account = account_data.get("account")
            .ok_or_else(|| Web3GamingError::blockchain_rpc("Missing account data"))?;
        
        let parsed = account.get("data")
            .and_then(|d| d.get("parsed"))
            .and_then(|p| p.get("info"))
            .ok_or_else(|| Web3GamingError::blockchain_rpc("Missing parsed account info"))?;

        let token_account = SolanaTokenAccount {
            account: account_data.get("pubkey")
                .and_then(|p| p.as_str())
                .unwrap_or("")
                .to_string(),
            mint: parsed.get("mint")
                .and_then(|m| m.as_str())
                .unwrap_or("")
                .to_string(),
            owner: parsed.get("owner")
                .and_then(|o| o.as_str())
                .unwrap_or("")
                .to_string(),
            ui_token_amount: serde_json::from_value(
                parsed.get("tokenAmount").cloned().unwrap_or(json!({}))
            )?,
        };

        Ok(token_account)
    }

    /// Get signatures for address (recent transactions)
    pub async fn get_signatures_for_address(&self, address: &str, limit: Option<usize>) -> Result<Vec<Value>> {
        let mut params = json!([address]);
        
        if let Some(limit) = limit {
            params = json!([
                address,
                {
                    "limit": limit,
                    "commitment": "confirmed"
                }
            ]);
        }

        let result = self.rpc_call("getSignaturesForAddress", params).await?;

        let empty_vec = vec![];
        Ok(result.as_array().unwrap_or(&empty_vec).clone())
    }

    /// Analyze account for gaming relevance
    pub async fn analyze_gaming_relevance(&self, address: &str) -> Result<SolanaGamingAnalysis> {
        let mut analysis = SolanaGamingAnalysis {
            address: Address::new(address),
            is_gaming_related: false,
            confidence_score: 0.0,
            gaming_indicators: Vec::new(),
            account_type: "unknown".to_string(),
            token_accounts: Vec::new(),
            recent_activity: 0,
        };

        // Get account info
        if let Some(account_info) = self.get_account_info(address).await? {
            analysis.account_type = if account_info.executable {
                "program".to_string()
            } else {
                "account".to_string()
            };

            // Check for gaming program indicators
            if account_info.executable {
                analysis.gaming_indicators.push("executable_program".to_string());
                analysis.confidence_score += 0.1;
            }
        }

        // Get token accounts
        match self.get_token_accounts_by_owner(address).await {
            Ok(token_accounts) => {
                analysis.token_accounts = token_accounts.clone();
                
                // Analyze token holdings for gaming indicators
                for token_account in &token_accounts {
                    if self.is_gaming_token(&token_account.mint).await {
                        analysis.gaming_indicators.push(format!("gaming_token:{}", token_account.mint));
                        analysis.confidence_score += 0.2;
                    }
                }

                // High token diversity might indicate gaming activity
                if token_accounts.len() > 10 {
                    analysis.gaming_indicators.push("high_token_diversity".to_string());
                    analysis.confidence_score += 0.1;
                }
            }
            Err(e) => {
                debug!("Failed to get token accounts for {}: {}", address, e);
            }
        }

        // Get recent transaction activity
        match self.get_signatures_for_address(address, Some(100)).await {
            Ok(signatures) => {
                analysis.recent_activity = signatures.len() as u32;
                
                // High activity might indicate gaming
                if signatures.len() > 50 {
                    analysis.gaming_indicators.push("high_transaction_activity".to_string());
                    analysis.confidence_score += 0.1;
                }
            }
            Err(e) => {
                debug!("Failed to get signatures for {}: {}", address, e);
            }
        }

        // Determine if gaming-related
        analysis.is_gaming_related = analysis.confidence_score > 0.3;

        if analysis.is_gaming_related {
            info!(
                "Gaming-related Solana account detected: {} with confidence {:.2}",
                address, analysis.confidence_score
            );
        }

        Ok(analysis)
    }

    /// Check if a token mint is gaming-related (simplified heuristic)
    async fn is_gaming_token(&self, mint: &str) -> bool {
        // Known gaming token mints on Solana (this would be expanded with a real database)
        let known_gaming_tokens = [
            "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", // Bonk (gaming meme)
            "So11111111111111111111111111111111111111112",  // Wrapped SOL (used in many games)
            // Add more known gaming tokens here
        ];

        known_gaming_tokens.contains(&mint)
    }

    /// Get current slot (block height equivalent)
    pub async fn get_slot(&self) -> Result<u64> {
        let result = self.rpc_call("getSlot", json!([])).await?;
        Ok(result.as_u64().unwrap_or(0))
    }

    /// Get block information
    pub async fn get_block(&self, slot: u64) -> Result<Value> {
        let params = json!([
            slot,
            {
                "encoding": "json",
                "transactionDetails": "full",
                "rewards": false
            }
        ]);

        self.rpc_call("getBlock", params).await
    }

    /// Get multiple accounts in a single request (batch optimization)
    pub async fn get_multiple_accounts(&self, addresses: &[&str]) -> Result<Vec<Option<SolanaAccountInfo>>> {
        let params = json!([
            addresses,
            {
                "encoding": "base64",
                "commitment": "confirmed"
            }
        ]);

        let result = self.rpc_call("getMultipleAccounts", params).await?;

        let empty_vec = vec![];
        let accounts = result.get("value")
            .and_then(|v| v.as_array())
            .unwrap_or(&empty_vec);

        let mut account_infos = Vec::new();
        for account in accounts {
            if account.is_null() {
                account_infos.push(None);
            } else {
                match serde_json::from_value(account.clone()) {
                    Ok(account_info) => account_infos.push(Some(account_info)),
                    Err(_) => account_infos.push(None),
                }
            }
        }

        Ok(account_infos)
    }

    /// Get program accounts (useful for finding gaming contracts)
    pub async fn get_program_accounts(&self, program_id: &str, filters: Option<Value>) -> Result<Vec<Value>> {
        let mut params = json!([program_id]);

        if let Some(filters) = filters {
            params = json!([program_id, filters]);
        }

        let result = self.rpc_call("getProgramAccounts", params).await?;

        let empty_vec = vec![];
        Ok(result.as_array().unwrap_or(&empty_vec).clone())
    }

    /// Get transaction details
    pub async fn get_transaction(&self, signature: &str) -> Result<Option<SolanaTransaction>> {
        let params = json!([
            signature,
            {
                "encoding": "json",
                "commitment": "confirmed"
            }
        ]);

        let result = self.rpc_call("getTransaction", params).await?;

        if result.is_null() {
            return Ok(None);
        }

        let transaction: SolanaTransaction = serde_json::from_value(result)?;
        Ok(Some(transaction))
    }

    /// Get health status of the RPC endpoint
    pub async fn get_health(&self) -> Result<String> {
        let result = self.rpc_call("getHealth", json!([])).await?;
        Ok(result.as_str().unwrap_or("unknown").to_string())
    }

    /// Get version information
    pub async fn get_version(&self) -> Result<Value> {
        self.rpc_call("getVersion", json!([])).await
    }

    /// Get cluster nodes information
    pub async fn get_cluster_nodes(&self) -> Result<Vec<Value>> {
        let result = self.rpc_call("getClusterNodes", json!([])).await?;
        let empty_vec = vec![];
        Ok(result.as_array().unwrap_or(&empty_vec).clone())
    }

    /// Get performance samples
    pub async fn get_recent_performance_samples(&self, limit: Option<usize>) -> Result<Vec<Value>> {
        let params = if let Some(limit) = limit {
            json!([limit])
        } else {
            json!([])
        };

        let result = self.rpc_call("getRecentPerformanceSamples", params).await?;
        let empty_vec = vec![];
        Ok(result.as_array().unwrap_or(&empty_vec).clone())
    }

    /// Get connection statistics
    pub fn get_connection_stats(&self) -> ConnectionStats {
        ConnectionStats {
            total_requests: self.request_counter.load(Ordering::Relaxed),
            rate_limit_config: self.rate_limit_config.clone(),
            connection_config: self.connection_config.clone(),
        }
    }

    /// Analyze transaction for gaming relevance
    pub async fn analyze_transaction_gaming(&self, signature: &str) -> Result<GamingTransactionAnalysis> {
        let transaction = self.get_transaction(signature).await?
            .ok_or_else(|| Web3GamingError::blockchain_rpc("Transaction not found"))?;

        let mut analysis = GamingTransactionAnalysis {
            is_gaming_related: false,
            confidence_score: 0.0,
            gaming_indicators: Vec::new(),
            token_transfers: Vec::new(),
            nft_transfers: Vec::new(),
            program_interactions: Vec::new(),
            estimated_value_usd: None,
            transaction_type: TransactionType::Unknown,
        };

        // Analyze program interactions
        self.analyze_program_interactions(&transaction, &mut analysis).await?;

        // Analyze token transfers
        self.analyze_token_transfers(&transaction, &mut analysis).await?;

        // Analyze NFT transfers
        self.analyze_nft_transfers(&transaction, &mut analysis).await?;

        // Calculate overall gaming relevance
        self.calculate_gaming_confidence(&mut analysis);

        // Classify transaction type
        self.classify_transaction_type(&mut analysis);

        Ok(analysis)
    }

    /// Analyze program interactions for gaming indicators
    async fn analyze_program_interactions(
        &self,
        transaction: &SolanaTransaction,
        analysis: &mut GamingTransactionAnalysis,
    ) -> Result<()> {
        let instructions = &transaction.transaction.message.instructions;

        for instruction in instructions {
            let program_id = &transaction.transaction.message.account_keys[instruction.program_id_index as usize];

            let interaction = ProgramInteraction {
                program_id: program_id.clone(),
                instruction_name: self.decode_instruction_name(program_id, &instruction.data).await,
                instruction_data: instruction.data.clone(),
                accounts: instruction.accounts.iter()
                    .map(|&idx| transaction.transaction.message.account_keys[idx as usize].clone())
                    .collect(),
                is_gaming_program: self.is_gaming_program(program_id).await,
                program_type: self.get_program_type(program_id).await,
            };

            if interaction.is_gaming_program {
                analysis.gaming_indicators.push(GamingIndicator {
                    indicator_type: "gaming_program_interaction".to_string(),
                    description: format!("Interaction with known gaming program: {}", program_id),
                    confidence: 0.8,
                    evidence: vec![format!("Program ID: {}", program_id)],
                });
            }

            analysis.program_interactions.push(interaction);
        }

        Ok(())
    }

    /// Analyze token transfers for gaming tokens
    async fn analyze_token_transfers(
        &self,
        transaction: &SolanaTransaction,
        analysis: &mut GamingTransactionAnalysis,
    ) -> Result<()> {
        // This is a simplified implementation - in practice, you'd parse the actual logs
        // to extract SPL token transfer information

        // For now, we'll check if any of the accounts are known gaming token mints
        for account in &transaction.transaction.message.account_keys {
            if self.is_gaming_token_mint(account).await {
                analysis.gaming_indicators.push(GamingIndicator {
                    indicator_type: "gaming_token_interaction".to_string(),
                    description: format!("Interaction with gaming token: {}", account),
                    confidence: 0.7,
                    evidence: vec![format!("Token mint: {}", account)],
                });
            }
        }

        Ok(())
    }

    /// Analyze NFT transfers for gaming NFTs
    async fn analyze_nft_transfers(
        &self,
        transaction: &SolanaTransaction,
        analysis: &mut GamingTransactionAnalysis,
    ) -> Result<()> {
        // Check for Metaplex NFT program interactions
        let metaplex_program = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s";

        for account in &transaction.transaction.message.account_keys {
            if account == metaplex_program {
                analysis.gaming_indicators.push(GamingIndicator {
                    indicator_type: "nft_interaction".to_string(),
                    description: "Metaplex NFT program interaction detected".to_string(),
                    confidence: 0.6,
                    evidence: vec!["Metaplex program interaction".to_string()],
                });
                break;
            }
        }

        Ok(())
    }

    /// Calculate overall gaming confidence score
    fn calculate_gaming_confidence(&self, analysis: &mut GamingTransactionAnalysis) {
        if analysis.gaming_indicators.is_empty() {
            analysis.confidence_score = 0.0;
            analysis.is_gaming_related = false;
            return;
        }

        // Calculate weighted average of indicator confidences
        let total_confidence: f64 = analysis.gaming_indicators.iter()
            .map(|indicator| indicator.confidence)
            .sum();

        analysis.confidence_score = total_confidence / analysis.gaming_indicators.len() as f64;
        analysis.is_gaming_related = analysis.confidence_score > 0.5;
    }

    /// Classify transaction type based on analysis
    fn classify_transaction_type(&self, analysis: &mut GamingTransactionAnalysis) {
        // Simple classification based on indicators
        for indicator in &analysis.gaming_indicators {
            match indicator.indicator_type.as_str() {
                "gaming_program_interaction" => {
                    analysis.transaction_type = TransactionType::GamePlay;
                    return;
                }
                "gaming_token_interaction" => {
                    analysis.transaction_type = TransactionType::TokenStaking;
                    return;
                }
                "nft_interaction" => {
                    analysis.transaction_type = TransactionType::NftTransfer;
                    return;
                }
                _ => {}
            }
        }
    }

    /// Check if a program ID is a known gaming program
    async fn is_gaming_program(&self, program_id: &str) -> bool {
        // Known gaming programs on Solana
        let gaming_programs = [
            "BAP315i1xoAXqbJcTT1LrUS45N3tAQnNnPuNQkCcvbAr", // Star Atlas
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM", // Aurory
            "GDfnEsia2WLAW5t8yx2X5j2mkfA74i5kwGdDuZHt7XmG", // Genopets
            "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s", // Metaplex
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", // SPL Token (used by games)
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL", // Associated Token Account
        ];

        gaming_programs.contains(&program_id)
    }

    /// Get program type classification
    async fn get_program_type(&self, program_id: &str) -> Option<String> {
        match program_id {
            "BAP315i1xoAXqbJcTT1LrUS45N3tAQnNnPuNQkCcvbAr" => Some("star_atlas_game".to_string()),
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM" => Some("aurory_game".to_string()),
            "GDfnEsia2WLAW5t8yx2X5j2mkfA74i5kwGdDuZHt7XmG" => Some("genopets_game".to_string()),
            "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s" => Some("nft_metadata".to_string()),
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" => Some("spl_token".to_string()),
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL" => Some("associated_token".to_string()),
            _ => None,
        }
    }

    /// Check if a mint address is a gaming token
    async fn is_gaming_token_mint(&self, mint: &str) -> bool {
        // Known gaming token mints
        let gaming_tokens = [
            "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx", // ATLAS (Star Atlas)
            "poLisWXnNRwC6oBu1vHiuKQzFjGL4XDSu4g9qjz9qVk",  // POLIS (Star Atlas)
            "AURYydfxJib1ZkTir1Jn1J9ECYUtjb6rKQVmtYaixWPP", // AURY (Aurory)
            "GENEtH5amGSi8kHAtQoezp1XEXwZJ8vcuePYnXdKrMYz", // GENE (Genopets)
            "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", // BONK (gaming meme)
            "So11111111111111111111111111111111111111112",  // Wrapped SOL
        ];

        gaming_tokens.contains(&mint)
    }

    /// Decode instruction name from instruction data (simplified)
    async fn decode_instruction_name(&self, program_id: &str, instruction_data: &str) -> Option<String> {
        // This is a simplified decoder - in practice, you'd need proper IDL parsing
        match program_id {
            "BAP315i1xoAXqbJcTT1LrUS45N3tAQnNnPuNQkCcvbAr" => {
                // Star Atlas instruction decoding
                if instruction_data.starts_with("01") {
                    Some("mine_asteroid".to_string())
                } else if instruction_data.starts_with("02") {
                    Some("craft_item".to_string())
                } else {
                    Some("star_atlas_action".to_string())
                }
            }
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM" => {
                Some("aurory_game_action".to_string())
            }
            "GDfnEsia2WLAW5t8yx2X5j2mkfA74i5kwGdDuZHt7XmG" => {
                Some("genopets_action".to_string())
            }
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" => {
                if instruction_data.starts_with("03") {
                    Some("transfer".to_string())
                } else if instruction_data.starts_with("07") {
                    Some("mint_to".to_string())
                } else {
                    Some("spl_token_action".to_string())
                }
            }
            _ => None,
        }
    }

    /// Get recent gaming transactions for analysis
    pub async fn get_recent_gaming_transactions(&self, limit: Option<usize>) -> Result<Vec<GamingTransactionAnalysis>> {
        let limit = limit.unwrap_or(10);
        let mut gaming_transactions = Vec::new();

        // Get recent signatures from known gaming programs
        let gaming_programs = [
            "BAP315i1xoAXqbJcTT1LrUS45N3tAQnNnPuNQkCcvbAr", // Star Atlas
            "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM", // Aurory
        ];

        for program_id in &gaming_programs {
            match self.get_signatures_for_address(program_id, Some(limit / gaming_programs.len())).await {
                Ok(signatures) => {
                    for sig_info in signatures.iter().take(limit / gaming_programs.len()) {
                        if let Some(signature) = sig_info.get("signature").and_then(|s| s.as_str()) {
                            match self.analyze_transaction_gaming(signature).await {
                                Ok(analysis) => {
                                    if analysis.is_gaming_related {
                                        gaming_transactions.push(analysis);
                                    }
                                }
                                Err(e) => {
                                    debug!("Failed to analyze transaction {}: {}", signature, e);
                                }
                            }
                        }
                    }
                }
                Err(e) => {
                    debug!("Failed to get signatures for program {}: {}", program_id, e);
                }
            }
        }

        gaming_transactions.sort_by(|a, b| b.confidence_score.partial_cmp(&a.confidence_score).unwrap_or(std::cmp::Ordering::Equal));
        gaming_transactions.truncate(limit);

        Ok(gaming_transactions)
    }

    /// Batch analyze multiple transactions
    pub async fn batch_analyze_transactions(&self, signatures: &[&str]) -> Result<Vec<GamingTransactionAnalysis>> {
        let mut analyses = Vec::new();

        for signature in signatures {
            match self.analyze_transaction_gaming(signature).await {
                Ok(analysis) => analyses.push(analysis),
                Err(e) => {
                    warn!("Failed to analyze transaction {}: {}", signature, e);
                    // Continue with other transactions
                }
            }
        }

        Ok(analyses)
    }

    /// Analyze a program for gaming contract features
    pub async fn analyze_gaming_contract(&self, program_id: &str) -> Result<GamingContractAnalysis> {
        self.gaming_detector.analyze_contract(program_id).await
    }

    /// Get gaming contract detection statistics
    pub fn get_gaming_detection_stats(&self) -> std::collections::HashMap<String, usize> {
        self.gaming_detector.get_detection_stats()
    }

    /// Batch analyze multiple programs for gaming features
    pub async fn batch_analyze_gaming_contracts(&self, program_ids: &[&str]) -> Result<Vec<GamingContractAnalysis>> {
        let mut analyses = Vec::new();

        for program_id in program_ids {
            match self.analyze_gaming_contract(program_id).await {
                Ok(analysis) => analyses.push(analysis),
                Err(e) => {
                    warn!("Failed to analyze gaming contract {}: {}", program_id, e);
                    // Continue with other contracts
                }
            }
        }

        Ok(analyses)
    }

    /// Find gaming contracts by scanning recent program deployments
    pub async fn discover_gaming_contracts(&self, limit: Option<usize>) -> Result<Vec<GamingContractAnalysis>> {
        let limit = limit.unwrap_or(50);
        let mut gaming_contracts = Vec::new();

        // This is a simplified implementation
        // In practice, you would:
        // 1. Scan recent program deployments
        // 2. Analyze program bytecode
        // 3. Check interaction patterns
        // 4. Use ML models for classification

        // For now, we'll analyze some known program addresses
        let candidate_programs = [
            "11111111111111111111111111111111", // System Program
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", // SPL Token
            "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL", // Associated Token
            "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s", // Metaplex
            "CndyV3LdqHUfDLmE5naZjVN8rBZz4tqhdefbAnjHG3JR", // Candy Machine
        ];

        for program_id in candidate_programs.iter().take(limit) {
            match self.analyze_gaming_contract(program_id).await {
                Ok(analysis) => {
                    if analysis.is_gaming_contract {
                        gaming_contracts.push(analysis);
                    }
                }
                Err(e) => {
                    debug!("Failed to analyze program {}: {}", program_id, e);
                }
            }
        }

        // Sort by confidence score
        gaming_contracts.sort_by(|a, b| b.confidence_score.partial_cmp(&a.confidence_score).unwrap_or(std::cmp::Ordering::Equal));

        Ok(gaming_contracts)
    }

    /// Enhanced gaming relevance analysis using contract detection
    pub async fn enhanced_gaming_analysis(&self, address: &str) -> Result<SolanaGamingAnalysis> {
        let mut analysis = self.analyze_gaming_relevance(address).await?;

        // Enhance with contract detection if this is a program
        if let Some(account_info) = self.get_account_info(address).await? {
            if account_info.executable {
                match self.analyze_gaming_contract(address).await {
                    Ok(contract_analysis) => {
                        if contract_analysis.is_gaming_contract {
                            analysis.gaming_indicators.push(format!(
                                "gaming_contract_detected:{}:confidence:{}",
                                format!("{:?}", contract_analysis.contract_type),
                                contract_analysis.confidence_score
                            ));

                            // Boost confidence based on contract analysis
                            analysis.confidence_score = (analysis.confidence_score + contract_analysis.confidence_score) / 2.0;
                            analysis.is_gaming_related = analysis.confidence_score > 0.3;
                        }
                    }
                    Err(e) => {
                        debug!("Contract analysis failed for {}: {}", address, e);
                    }
                }
            }
        }

        Ok(analysis)
    }
}

/// Connection statistics for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionStats {
    pub total_requests: u64,
    pub rate_limit_config: RateLimitConfig,
    pub connection_config: ConnectionConfig,
}

/// Gaming-specific transaction analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GamingTransactionAnalysis {
    pub is_gaming_related: bool,
    pub confidence_score: f64, // 0.0 to 1.0
    pub gaming_indicators: Vec<GamingIndicator>,
    pub token_transfers: Vec<TokenTransfer>,
    pub nft_transfers: Vec<NftTransfer>,
    pub program_interactions: Vec<ProgramInteraction>,
    pub estimated_value_usd: Option<f64>,
    pub transaction_type: TransactionType,
}

/// Gaming indicators found in transaction
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GamingIndicator {
    pub indicator_type: String,
    pub description: String,
    pub confidence: f64,
    pub evidence: Vec<String>,
}

/// Token transfer information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenTransfer {
    pub mint: String,
    pub from: Option<String>,
    pub to: Option<String>,
    pub amount: u64,
    pub decimals: u8,
    pub token_name: Option<String>,
    pub is_gaming_token: bool,
}

/// NFT transfer information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NftTransfer {
    pub mint: String,
    pub from: Option<String>,
    pub to: Option<String>,
    pub metadata_uri: Option<String>,
    pub collection: Option<String>,
    pub is_gaming_nft: bool,
}

/// Program interaction details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgramInteraction {
    pub program_id: String,
    pub instruction_name: Option<String>,
    pub instruction_data: String,
    pub accounts: Vec<String>,
    pub is_gaming_program: bool,
    pub program_type: Option<String>,
}

/// Transaction type classification
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionType {
    GamePlay,
    ItemPurchase,
    ItemSale,
    TokenStaking,
    TokenUnstaking,
    NftMint,
    NftTransfer,
    MarketplaceTrade,
    GuildActivity,
    TournamentEntry,
    RewardClaim,
    Unknown,
}

impl Clone for SolanaClient {
    fn clone(&self) -> Self {
        Self {
            rpc_url: self.rpc_url.clone(),
            http_client: self.http_client.clone(),
            rate_limiter: Arc::clone(&self.rate_limiter),
            rate_limit_config: self.rate_limit_config.clone(),
            connection_config: self.connection_config.clone(),
            request_counter: AtomicU64::new(self.request_counter.load(Ordering::Relaxed)),
            last_request_time: Arc::clone(&self.last_request_time),
            gaming_detector: Arc::clone(&self.gaming_detector),
        }
    }
}
