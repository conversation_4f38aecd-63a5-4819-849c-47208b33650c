//! Multi-Chain RPC Client
//!
//! COMPONENT_ESSENCE::HERMES_MULTI_CHAIN_BRIDGE_WISDOM
//! DANGER_LEVEL::APOLLO_UNIFIED_INTERFACE_GUARDIAN
//! PERFORMANCE_TARGET::SUB_100MS_CROSS_CHAIN_QUERIES
//! LAST_MODIFIED::PHASE_2_MULTI_CHAIN_TRIUMPH
//! SOLUTION_TYPE::UNIFIED_BLOCKCHAIN_ABSTRACTION_LAYER
//!
//! This module provides a unified interface for interacting with multiple
//! blockchain networks including Ethereum, Polygon, Arbitrum, and Solana.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use shared_utils::{Address, Chain, Web3GamingError};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};

use crate::solana::SolanaClient;

/// Unified blockchain client supporting multiple chains
#[derive(Debug)]
pub struct MultiChainClient {
    clients: Arc<RwLock<HashMap<Chain, Arc<dyn BlockchainClient + Send + Sync>>>>,
    default_timeouts: HashMap<Chain, Duration>,
    chain_configs: HashMap<Chain, ChainConfig>,
}

/// Configuration for each blockchain
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainConfig {
    pub rpc_url: String,
    pub chain_id: Option<u64>,
    pub block_time_ms: u64,
    pub max_retries: u32,
    pub timeout_seconds: u64,
    pub rate_limit_per_second: u32,
}

/// Unified blockchain client trait
#[async_trait::async_trait]
pub trait BlockchainClient: std::fmt::Debug {
    /// Get the chain this client supports
    fn chain(&self) -> Chain;
    
    /// Get current block number
    async fn get_block_number(&self) -> Result<u64>;
    
    /// Get block by number
    async fn get_block(&self, block_number: u64) -> Result<BlockInfo>;
    
    /// Get transaction by hash
    async fn get_transaction(&self, tx_hash: &str) -> Result<Option<TransactionInfo>>;
    
    /// Get account/address information
    async fn get_account_info(&self, address: &str) -> Result<Option<AccountInfo>>;
    
    /// Get token balance for address
    async fn get_token_balance(&self, address: &str, token_address: Option<&str>) -> Result<TokenBalance>;
    
    /// Get recent transactions for address
    async fn get_address_transactions(&self, address: &str, limit: Option<usize>) -> Result<Vec<TransactionInfo>>;
    
    /// Health check
    async fn health_check(&self) -> Result<HealthStatus>;
}

/// Unified block information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockInfo {
    pub number: u64,
    pub hash: String,
    pub parent_hash: String,
    pub timestamp: u64,
    pub transaction_count: usize,
    pub gas_used: Option<u64>,
    pub gas_limit: Option<u64>,
    pub chain: Chain,
}

/// Unified transaction information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionInfo {
    pub hash: String,
    pub block_number: Option<u64>,
    pub from: Option<String>,
    pub to: Option<String>,
    pub value: String,
    pub gas_used: Option<u64>,
    pub gas_price: Option<String>,
    pub status: TransactionStatus,
    pub timestamp: Option<u64>,
    pub chain: Chain,
}

/// Transaction status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransactionStatus {
    Pending,
    Success,
    Failed,
    Unknown,
}

/// Unified account information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountInfo {
    pub address: String,
    pub balance: String,
    pub nonce: Option<u64>,
    pub code: Option<String>,
    pub is_contract: bool,
    pub chain: Chain,
}

/// Token balance information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenBalance {
    pub address: String,
    pub token_address: Option<String>,
    pub balance: String,
    pub decimals: u8,
    pub symbol: Option<String>,
    pub name: Option<String>,
    pub chain: Chain,
}

/// Health status for blockchain client
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    pub chain: Chain,
    pub is_healthy: bool,
    pub latest_block: Option<u64>,
    pub response_time_ms: u64,
    pub error_message: Option<String>,
}

impl MultiChainClient {
    /// Create a new multi-chain client
    pub fn new() -> Self {
        let mut default_timeouts = HashMap::new();
        default_timeouts.insert(Chain::Ethereum, Duration::from_secs(30));
        default_timeouts.insert(Chain::Polygon, Duration::from_secs(20));
        default_timeouts.insert(Chain::Arbitrum, Duration::from_secs(15));
        default_timeouts.insert(Chain::Solana, Duration::from_secs(10));

        Self {
            clients: Arc::new(RwLock::new(HashMap::new())),
            default_timeouts,
            chain_configs: HashMap::new(),
        }
    }

    /// Add a blockchain client for a specific chain
    pub async fn add_chain(&mut self, chain: Chain, config: ChainConfig) -> Result<()> {
        let client: Arc<dyn BlockchainClient + Send + Sync> = match chain {
            Chain::Solana => {
                let solana_client = SolanaClient::new(&config.rpc_url);
                Arc::new(SolanaClientWrapper::new(solana_client))
            }
            Chain::Ethereum => {
                Arc::new(EthereumClient::new(config.clone()).await?)
            }
            Chain::Polygon => {
                Arc::new(PolygonClient::new(config.clone()).await?)
            }
            Chain::Arbitrum => {
                Arc::new(ArbitrumClient::new(config.clone()).await?)
            }
            _ => {
                return Err(Web3GamingError::blockchain_rpc(format!("Unsupported chain: {:?}", chain)).into());
            }
        };

        self.chain_configs.insert(chain, config);
        self.clients.write().await.insert(chain, client);

        info!("Added blockchain client for chain: {:?}", chain);
        Ok(())
    }

    /// Get client for specific chain
    async fn get_client(&self, chain: Chain) -> Result<Arc<dyn BlockchainClient + Send + Sync>> {
        let clients = self.clients.read().await;
        clients.get(&chain)
            .map(|client| Arc::clone(client))
            .ok_or_else(|| Web3GamingError::blockchain_rpc(format!("No client configured for chain: {:?}", chain)).into())
    }

    /// Get block number for specific chain
    pub async fn get_block_number(&self, chain: Chain) -> Result<u64> {
        let client = self.get_client(chain).await?;
        client.get_block_number().await
    }

    /// Get block information for specific chain
    pub async fn get_block(&self, chain: Chain, block_number: u64) -> Result<BlockInfo> {
        let client = self.get_client(chain).await?;
        client.get_block(block_number).await
    }

    /// Get transaction information across chains
    pub async fn get_transaction(&self, chain: Chain, tx_hash: &str) -> Result<Option<TransactionInfo>> {
        let client = self.get_client(chain).await?;
        client.get_transaction(tx_hash).await
    }

    /// Get account information across chains
    pub async fn get_account_info(&self, chain: Chain, address: &str) -> Result<Option<AccountInfo>> {
        let client = self.get_client(chain).await?;
        client.get_account_info(address).await
    }

    /// Get token balance across chains
    pub async fn get_token_balance(&self, chain: Chain, address: &str, token_address: Option<&str>) -> Result<TokenBalance> {
        let client = self.get_client(chain).await?;
        client.get_token_balance(address, token_address).await
    }

    /// Health check for all configured chains
    pub async fn health_check_all(&self) -> Result<HashMap<Chain, HealthStatus>> {
        let clients = self.clients.read().await;
        let mut health_statuses = HashMap::new();

        for (chain, client) in clients.iter() {
            match client.health_check().await {
                Ok(status) => {
                    health_statuses.insert(*chain, status);
                }
                Err(e) => {
                    warn!("Health check failed for chain {:?}: {}", chain, e);
                    health_statuses.insert(*chain, HealthStatus {
                        chain: *chain,
                        is_healthy: false,
                        latest_block: None,
                        response_time_ms: 0,
                        error_message: Some(e.to_string()),
                    });
                }
            }
        }

        Ok(health_statuses)
    }

    /// Get supported chains
    pub async fn get_supported_chains(&self) -> Vec<Chain> {
        self.clients.read().await.keys().cloned().collect()
    }

    /// Batch query across multiple chains
    pub async fn batch_query<T, F>(&self, chains: &[Chain], query_fn: F) -> HashMap<Chain, Result<T>>
    where
        F: Fn(Arc<dyn BlockchainClient + Send + Sync>) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T>> + Send>> + Send + Sync + Clone,
        T: Send + 'static,
    {
        let mut results = HashMap::new();
        let clients = self.clients.read().await;

        for &chain in chains {
            if let Some(client) = clients.get(&chain) {
                let client_clone = Arc::clone(client);
                let query_fn_clone = query_fn.clone();

                let result = query_fn_clone(client_clone).await;
                results.insert(chain, result);
            } else {
                results.insert(chain, Err(Web3GamingError::blockchain_rpc(format!("No client for chain: {:?}", chain)).into()));
            }
        }

        results
    }
}

impl Clone for MultiChainClient {
    fn clone(&self) -> Self {
        Self {
            clients: Arc::clone(&self.clients),
            default_timeouts: self.default_timeouts.clone(),
            chain_configs: self.chain_configs.clone(),
        }
    }
}

impl Default for MultiChainClient {
    fn default() -> Self {
        Self::new()
    }
}

// Wrapper for Solana client to implement BlockchainClient trait
#[derive(Debug)]
struct SolanaClientWrapper {
    client: SolanaClient,
}

impl SolanaClientWrapper {
    fn new(client: SolanaClient) -> Self {
        Self { client }
    }
}

#[async_trait::async_trait]
impl BlockchainClient for SolanaClientWrapper {
    fn chain(&self) -> Chain {
        Chain::Solana
    }

    async fn get_block_number(&self) -> Result<u64> {
        self.client.get_slot().await
    }

    async fn get_block(&self, block_number: u64) -> Result<BlockInfo> {
        let block = self.client.get_block(block_number).await?;
        
        Ok(BlockInfo {
            number: block_number,
            hash: block.get("blockhash").and_then(|v| v.as_str()).unwrap_or("").to_string(),
            parent_hash: block.get("previousBlockhash").and_then(|v| v.as_str()).unwrap_or("").to_string(),
            timestamp: block.get("blockTime").and_then(|v| v.as_u64()).unwrap_or(0),
            transaction_count: block.get("transactions").and_then(|v| v.as_array()).map(|arr| arr.len()).unwrap_or(0),
            gas_used: None, // Solana doesn't use gas
            gas_limit: None,
            chain: Chain::Solana,
        })
    }

    async fn get_transaction(&self, tx_hash: &str) -> Result<Option<TransactionInfo>> {
        match self.client.get_transaction(tx_hash).await? {
            Some(tx) => {
                Ok(Some(TransactionInfo {
                    hash: tx_hash.to_string(),
                    block_number: Some(tx.slot),
                    from: None, // Solana transactions have multiple signers
                    to: None,
                    value: "0".to_string(), // Solana doesn't have simple value transfers
                    gas_used: None,
                    gas_price: None,
                    status: if tx.transaction.message.recent_blockhash.is_empty() {
                        TransactionStatus::Failed
                    } else {
                        TransactionStatus::Success
                    },
                    timestamp: tx.block_time.map(|t| t as u64),
                    chain: Chain::Solana,
                }))
            }
            None => Ok(None),
        }
    }

    async fn get_account_info(&self, address: &str) -> Result<Option<AccountInfo>> {
        match self.client.get_account_info(address).await? {
            Some(account) => {
                Ok(Some(AccountInfo {
                    address: address.to_string(),
                    balance: account.lamports.to_string(),
                    nonce: None,
                    code: if account.executable { Some("executable".to_string()) } else { None },
                    is_contract: account.executable,
                    chain: Chain::Solana,
                }))
            }
            None => Ok(None),
        }
    }

    async fn get_token_balance(&self, address: &str, _token_address: Option<&str>) -> Result<TokenBalance> {
        let balance = self.client.get_balance(address).await?;
        
        Ok(TokenBalance {
            address: address.to_string(),
            token_address: None,
            balance: balance.to_string(),
            decimals: 9, // SOL has 9 decimals
            symbol: Some("SOL".to_string()),
            name: Some("Solana".to_string()),
            chain: Chain::Solana,
        })
    }

    async fn get_address_transactions(&self, _address: &str, _limit: Option<usize>) -> Result<Vec<TransactionInfo>> {
        // This would require implementing signature lookup for Solana
        // For now, return empty list
        Ok(Vec::new())
    }

    async fn health_check(&self) -> Result<HealthStatus> {
        let start = std::time::Instant::now();
        
        match self.client.get_health().await {
            Ok(health) => {
                let response_time = start.elapsed().as_millis() as u64;
                let latest_block = self.client.get_slot().await.ok();
                
                Ok(HealthStatus {
                    chain: Chain::Solana,
                    is_healthy: health == "ok",
                    latest_block,
                    response_time_ms: response_time,
                    error_message: None,
                })
            }
            Err(e) => {
                Ok(HealthStatus {
                    chain: Chain::Solana,
                    is_healthy: false,
                    latest_block: None,
                    response_time_ms: start.elapsed().as_millis() as u64,
                    error_message: Some(e.to_string()),
                })
            }
        }
    }
}

// Placeholder implementations for other chains
// These would be implemented with actual RPC clients

#[derive(Debug)]
struct EthereumClient {
    config: ChainConfig,
    http_client: reqwest::Client,
}

impl EthereumClient {
    async fn new(config: ChainConfig) -> Result<Self> {
        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()?;

        Ok(Self { config, http_client })
    }
}

#[async_trait::async_trait]
impl BlockchainClient for EthereumClient {
    fn chain(&self) -> Chain {
        Chain::Ethereum
    }

    async fn get_block_number(&self) -> Result<u64> {
        // Placeholder implementation
        // Would use eth_blockNumber RPC call
        Ok(0)
    }

    async fn get_block(&self, _block_number: u64) -> Result<BlockInfo> {
        // Placeholder implementation
        Ok(BlockInfo {
            number: 0,
            hash: "".to_string(),
            parent_hash: "".to_string(),
            timestamp: 0,
            transaction_count: 0,
            gas_used: Some(0),
            gas_limit: Some(0),
            chain: Chain::Ethereum,
        })
    }

    async fn get_transaction(&self, _tx_hash: &str) -> Result<Option<TransactionInfo>> {
        // Placeholder implementation
        Ok(None)
    }

    async fn get_account_info(&self, _address: &str) -> Result<Option<AccountInfo>> {
        // Placeholder implementation
        Ok(None)
    }

    async fn get_token_balance(&self, address: &str, _token_address: Option<&str>) -> Result<TokenBalance> {
        // Placeholder implementation
        Ok(TokenBalance {
            address: address.to_string(),
            token_address: None,
            balance: "0".to_string(),
            decimals: 18,
            symbol: Some("ETH".to_string()),
            name: Some("Ethereum".to_string()),
            chain: Chain::Ethereum,
        })
    }

    async fn get_address_transactions(&self, _address: &str, _limit: Option<usize>) -> Result<Vec<TransactionInfo>> {
        Ok(Vec::new())
    }

    async fn health_check(&self) -> Result<HealthStatus> {
        Ok(HealthStatus {
            chain: Chain::Ethereum,
            is_healthy: true,
            latest_block: Some(0),
            response_time_ms: 100,
            error_message: None,
        })
    }
}

// Similar placeholder implementations for Polygon and Arbitrum
#[derive(Debug)]
struct PolygonClient {
    config: ChainConfig,
}

impl PolygonClient {
    async fn new(config: ChainConfig) -> Result<Self> {
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl BlockchainClient for PolygonClient {
    fn chain(&self) -> Chain { Chain::Polygon }
    async fn get_block_number(&self) -> Result<u64> { Ok(0) }
    async fn get_block(&self, _block_number: u64) -> Result<BlockInfo> {
        Ok(BlockInfo {
            number: 0, hash: "".to_string(), parent_hash: "".to_string(),
            timestamp: 0, transaction_count: 0, gas_used: Some(0), gas_limit: Some(0),
            chain: Chain::Polygon,
        })
    }
    async fn get_transaction(&self, _tx_hash: &str) -> Result<Option<TransactionInfo>> { Ok(None) }
    async fn get_account_info(&self, _address: &str) -> Result<Option<AccountInfo>> { Ok(None) }
    async fn get_token_balance(&self, address: &str, _token_address: Option<&str>) -> Result<TokenBalance> {
        Ok(TokenBalance {
            address: address.to_string(), token_address: None, balance: "0".to_string(),
            decimals: 18, symbol: Some("MATIC".to_string()), name: Some("Polygon".to_string()),
            chain: Chain::Polygon,
        })
    }
    async fn get_address_transactions(&self, _address: &str, _limit: Option<usize>) -> Result<Vec<TransactionInfo>> { Ok(Vec::new()) }
    async fn health_check(&self) -> Result<HealthStatus> {
        Ok(HealthStatus {
            chain: Chain::Polygon, is_healthy: true, latest_block: Some(0),
            response_time_ms: 80, error_message: None,
        })
    }
}

#[derive(Debug)]
struct ArbitrumClient {
    config: ChainConfig,
}

impl ArbitrumClient {
    async fn new(config: ChainConfig) -> Result<Self> {
        Ok(Self { config })
    }
}

#[async_trait::async_trait]
impl BlockchainClient for ArbitrumClient {
    fn chain(&self) -> Chain { Chain::Arbitrum }
    async fn get_block_number(&self) -> Result<u64> { Ok(0) }
    async fn get_block(&self, _block_number: u64) -> Result<BlockInfo> {
        Ok(BlockInfo {
            number: 0, hash: "".to_string(), parent_hash: "".to_string(),
            timestamp: 0, transaction_count: 0, gas_used: Some(0), gas_limit: Some(0),
            chain: Chain::Arbitrum,
        })
    }
    async fn get_transaction(&self, _tx_hash: &str) -> Result<Option<TransactionInfo>> { Ok(None) }
    async fn get_account_info(&self, _address: &str) -> Result<Option<AccountInfo>> { Ok(None) }
    async fn get_token_balance(&self, address: &str, _token_address: Option<&str>) -> Result<TokenBalance> {
        Ok(TokenBalance {
            address: address.to_string(), token_address: None, balance: "0".to_string(),
            decimals: 18, symbol: Some("ETH".to_string()), name: Some("Ethereum".to_string()),
            chain: Chain::Arbitrum,
        })
    }
    async fn get_address_transactions(&self, _address: &str, _limit: Option<usize>) -> Result<Vec<TransactionInfo>> { Ok(Vec::new()) }
    async fn health_check(&self) -> Result<HealthStatus> {
        Ok(HealthStatus {
            chain: Chain::Arbitrum, is_healthy: true, latest_block: Some(0),
            response_time_ms: 60, error_message: None,
        })
    }
}
