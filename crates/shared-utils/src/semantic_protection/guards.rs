//! Function Guard Wrappers for Semantic Protection
//! 
//! COMPONENT_ESSENCE::HERMES_FUNCTION_PROTECTION_MESSENGER
//! DANGER_LEVEL::APOLLO_PRECISION_GUARD_CRITICAL
//! PERFORMANCE_TARGET::ZERO_OVERHEAD_PROTECTION_WRAPPING
//! LAST_MODIFIED::SEMANTIC_GUARD_IMPLEMENTATION

use super::*;
use std::future::Future;
use tracing::{debug, warn, instrument};

/// Generic function guard that wraps any function with semantic protection
pub struct FunctionGuard<F> {
    function: F,
    protection: SemanticProtection,
    context: String,
}

impl<F> FunctionGuard<F> {
    /// Create a new function guard
    pub fn new(function: F, protection: SemanticProtection, context: String) -> Self {
        Self {
            function,
            protection,
            context,
        }
    }
}

impl<F> FunctionGuard<F> {
    /// Execute the guarded function with semantic protection
    #[instrument(skip(self, args), fields(context = %self.context))]
    pub fn call<Args, Output>(&self, args: Args) -> SemanticResult<Output>
    where
        F: Fn(Args) -> Result<Output, Box<dyn std::error::Error + Send + Sync>>,
        Args: serde::Serialize,
        Output: serde::Serialize,
    {
        debug!("Executing guarded function with semantic protection");

        // Pre-execution semantic validation
        self.protection.validate_semantic_integrity(&args, &self.context)
            .map_err(|e| {
                warn!("Pre-execution semantic validation failed: {}", e);
                e
            })?;

        // Execute the protected function
        let result = (self.function)(args)
            .map_err(|e| SemanticProtectionError::ValidationFailed(
                format!("Function execution failed: {}", e)
            ))?;

        // Post-execution semantic validation
        self.protection.validate_semantic_integrity(&result, &format!("{}_result", self.context))
            .map_err(|e| {
                warn!("Post-execution semantic validation failed: {}", e);
                e
            })?;

        debug!("Function execution completed with semantic integrity preserved");
        Ok(result)
    }
}

/// Asynchronous function guard implementation
pub struct AsyncFunctionGuard<F> {
    function: F,
    protection: SemanticProtection,
    context: String,
}

impl<F> AsyncFunctionGuard<F> {
    /// Create a new async function guard
    pub fn new(function: F, protection: SemanticProtection, context: String) -> Self {
        Self {
            function,
            protection,
            context,
        }
    }
}

impl<F> AsyncFunctionGuard<F> {
    /// Execute the guarded async function with semantic protection
    #[instrument(skip(self, args), fields(context = %self.context))]
    pub async fn call<Args, Output, Fut>(&self, args: Args) -> SemanticResult<Output>
    where
        F: Fn(Args) -> Fut,
        Fut: Future<Output = Result<Output, Box<dyn std::error::Error + Send + Sync>>>,
        Args: serde::Serialize + Send,
        Output: serde::Serialize + Send,
    {
        debug!("Executing async guarded function with semantic protection");

        // Pre-execution semantic validation
        self.protection.validate_semantic_integrity(&args, &self.context)
            .map_err(|e| {
                warn!("Pre-execution semantic validation failed: {}", e);
                e
            })?;

        // Execute the protected async function
        let result = (self.function)(args).await
            .map_err(|e| SemanticProtectionError::ValidationFailed(
                format!("Async function execution failed: {}", e)
            ))?;

        // Post-execution semantic validation
        self.protection.validate_semantic_integrity(&result, &format!("{}_result", self.context))
            .map_err(|e| {
                warn!("Post-execution semantic validation failed: {}", e);
                e
            })?;

        debug!("Async function execution completed with semantic integrity preserved");
        Ok(result)
    }
}

/// Convenience trait for adding semantic protection to functions
pub trait SemanticGuard<Args, Output> {
    /// Guard this function with semantic protection
    fn with_semantic_protection(
        self,
        protection: SemanticProtection,
        context: String,
    ) -> FunctionGuard<Self>
    where
        Self: Sized;
}

impl<F, Args, Output> SemanticGuard<Args, Output> for F
where
    F: Fn(Args) -> Result<Output, Box<dyn std::error::Error + Send + Sync>>,
    Args: serde::Serialize,
    Output: serde::Serialize,
{
    fn with_semantic_protection(
        self,
        protection: SemanticProtection,
        context: String,
    ) -> FunctionGuard<Self> {
        FunctionGuard::new(self, protection, context)
    }
}

/// Semantic protection builder for easy configuration
pub struct SemanticProtectionBuilder {
    protection_level: Option<ProtectionLevel>,
    semantic_profile: Vec<SemanticBase>,
    mutation_threshold: Option<f64>,
    evolution_enabled: Option<bool>,
    context: Option<String>,
}

impl SemanticProtectionBuilder {
    /// Create a new builder
    pub fn new() -> Self {
        Self {
            protection_level: None,
            semantic_profile: Vec::new(),
            mutation_threshold: None,
            evolution_enabled: None,
            context: None,
        }
    }

    /// Set protection level
    pub fn protection_level(mut self, level: ProtectionLevel) -> Self {
        self.protection_level = Some(level);
        self
    }

    /// Add semantic base to profile
    pub fn semantic_base(mut self, base: SemanticBase) -> Self {
        self.semantic_profile.push(base);
        self
    }

    /// Set semantic profile
    pub fn semantic_profile(mut self, profile: Vec<SemanticBase>) -> Self {
        self.semantic_profile = profile;
        self
    }

    /// Set mutation threshold
    pub fn mutation_threshold(mut self, threshold: f64) -> Self {
        self.mutation_threshold = Some(threshold);
        self
    }

    /// Enable or disable evolution
    pub fn evolution_enabled(mut self, enabled: bool) -> Self {
        self.evolution_enabled = Some(enabled);
        self
    }

    /// Set context
    pub fn context(mut self, context: String) -> Self {
        self.context = Some(context);
        self
    }

    /// Build the semantic protection system
    pub fn build(self) -> SemanticResult<SemanticProtection> {
        if self.semantic_profile.is_empty() {
            return Err(SemanticProtectionError::ConfigurationError(
                "Semantic profile cannot be empty".to_string()
            ));
        }

        let mut config = match self.protection_level {
            Some(ProtectionLevel::Critical) => SemanticProtectionConfig::critical(self.semantic_profile),
            Some(ProtectionLevel::Evolutionary) => SemanticProtectionConfig::evolutionary(self.semantic_profile),
            Some(ProtectionLevel::ApiEndpoint) => SemanticProtectionConfig::api_endpoint(self.semantic_profile),
            Some(ProtectionLevel::Internal) | None => {
                let mut config = SemanticProtectionConfig::default();
                config.semantic_profile = self.semantic_profile;
                config
            }
        };

        if let Some(threshold) = self.mutation_threshold {
            config.mutation_threshold = threshold;
        }

        if let Some(enabled) = self.evolution_enabled {
            config.evolution_enabled = enabled;
        }

        Ok(SemanticProtection::new(config))
    }

    /// Guard a function with the built protection
    pub fn guard_function<F>(
        self,
        function: F,
    ) -> SemanticResult<FunctionGuard<F>> {
        let context = self.context.clone().unwrap_or_else(|| "guarded_function".to_string());
        let protection = self.build()?;
        Ok(FunctionGuard::new(function, protection, context))
    }

    /// Guard an async function with the built protection
    pub fn guard_async_function<F>(
        self,
        function: F,
    ) -> SemanticResult<AsyncFunctionGuard<F>> {
        let context = self.context.clone().unwrap_or_else(|| "guarded_async_function".to_string());
        let protection = self.build()?;
        Ok(AsyncFunctionGuard::new(function, protection, context))
    }
}

impl Default for SemanticProtectionBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Convenience macros for semantic protection
#[macro_export]
macro_rules! semantic_guard {
    ($func:expr, $level:expr, $profile:expr) => {
        SemanticProtectionBuilder::new()
            .protection_level($level)
            .semantic_profile($profile)
            .guard_function($func)
    };
}

#[macro_export]
macro_rules! semantic_guard_critical {
    ($func:expr, $profile:expr) => {
        semantic_guard!($func, ProtectionLevel::Critical, $profile)
    };
}

#[macro_export]
macro_rules! semantic_guard_evolutionary {
    ($func:expr, $profile:expr) => {
        semantic_guard!($func, ProtectionLevel::Evolutionary, $profile)
    };
}

/// Pre-configured protection for common use cases
pub struct CommonProtections;

impl CommonProtections {
    /// Authentication system protection
    pub fn authentication() -> SemanticProtection {
        SemanticProtection::new_critical(vec![
            SemanticBase::Artemis, // Boundary protection
            SemanticBase::Athena,  // Wisdom for security decisions
        ])
    }

    /// API endpoint protection
    pub fn api_endpoint() -> SemanticProtection {
        SemanticProtection::new_api_endpoint(vec![
            SemanticBase::Hermes,  // Communication
            SemanticBase::Apollo,  // Precision
            SemanticBase::Artemis, // Protection
        ])
    }

    /// Blockchain RPC protection
    pub fn blockchain_rpc() -> SemanticProtection {
        SemanticProtection::new_critical(vec![
            SemanticBase::Apollo,  // Precision
            SemanticBase::Artemis, // Security
            SemanticBase::Hermes,  // Communication
        ])
    }

    /// Analytics engine protection
    pub fn analytics_engine() -> SemanticProtection {
        SemanticProtection::new_evolutionary(vec![
            SemanticBase::Athena,   // Wisdom
            SemanticBase::Dionysus, // Creativity
            SemanticBase::Apollo,   // Precision
        ])
    }

    /// OCTAVE context protection
    pub fn octave_context() -> SemanticProtection {
        SemanticProtection::new_critical(vec![
            SemanticBase::Zeus,   // Authority
            SemanticBase::Athena, // Wisdom
        ])
    }
}
