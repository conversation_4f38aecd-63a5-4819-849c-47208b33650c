//! Comprehensive Tests for Semantic Protection System
//! 
//! COMPONENT_ESSENCE::APOLLO_TESTING_PRECISION_ORACLE
//! DANGER_LEVEL::ATHENA_VALIDATION_WISDOM_CRITICAL
//! PERFORMANCE_TARGET::100_PERCENT_COVERAGE_VALIDATION
//! LAST_MODIFIED::SEMANTIC_PROTECTION_TEST_SUITE

#[cfg(test)]
mod tests {
    use super::super::*;
    use serde_json::json;

    /// Test data structure for semantic validation
    #[derive(Debug, serde::Serialize, serde::Deserialize)]
    struct TestData {
        pub id: String,
        pub content: String,
        pub metadata: std::collections::HashMap<String, String>,
    }

    impl TestData {
        fn new(id: &str, content: &str) -> Self {
            Self {
                id: id.to_string(),
                content: content.to_string(),
                metadata: std::collections::HashMap::new(),
            }
        }
    }

    #[test]
    fn test_semantic_base_display() {
        assert_eq!(SemanticBase::Zeus.to_string(), "ZEUS");
        assert_eq!(SemanticBase::Athena.to_string(), "ATHENA");
        assert_eq!(SemanticBase::Apollo.to_string(), "APOLLO");
    }

    #[test]
    fn test_protection_level_thresholds() {
        assert_eq!(ProtectionLevel::Critical.mutation_threshold(), 0.95);
        assert_eq!(ProtectionLevel::ApiEndpoint.mutation_threshold(), 0.8);
        assert_eq!(ProtectionLevel::Internal.mutation_threshold(), 0.5);
        assert_eq!(ProtectionLevel::Evolutionary.mutation_threshold(), 0.6);
    }

    #[test]
    fn test_protection_level_evolution_allowance() {
        assert!(!ProtectionLevel::Critical.allows_evolution());
        assert!(!ProtectionLevel::ApiEndpoint.allows_evolution());
        assert!(ProtectionLevel::Internal.allows_evolution());
        assert!(ProtectionLevel::Evolutionary.allows_evolution());
    }

    #[test]
    fn test_semantic_protection_config_creation() {
        let profile = vec![SemanticBase::Apollo, SemanticBase::Athena];
        
        let critical_config = SemanticProtectionConfig::critical(profile.clone());
        assert_eq!(critical_config.protection_level, ProtectionLevel::Critical);
        assert_eq!(critical_config.mutation_threshold, 0.95);
        assert!(!critical_config.evolution_enabled);

        let evolutionary_config = SemanticProtectionConfig::evolutionary(profile.clone());
        assert_eq!(evolutionary_config.protection_level, ProtectionLevel::Evolutionary);
        assert_eq!(evolutionary_config.mutation_threshold, 0.6);
        assert!(evolutionary_config.evolution_enabled);
    }

    #[test]
    fn test_semantic_protection_initialization() {
        let config = SemanticProtectionConfig::critical(vec![
            SemanticBase::Artemis,
            SemanticBase::Athena,
        ]);
        
        let protection = SemanticProtection::new(config);
        let health = protection.get_health_report();
        
        assert_eq!(health.total_validations, 0);
        assert_eq!(health.mutations_detected, 0);
        assert_eq!(health.mutations_prevented, 0);
    }

    #[test]
    fn test_semantic_validation_success() {
        let protection = SemanticProtection::new_critical(vec![
            SemanticBase::Apollo,
            SemanticBase::Athena,
        ]);

        let test_data = TestData::new("test_1", "Valid semantic content");
        let result = protection.validate_semantic_integrity(&test_data, "test_context");
        
        assert!(result.is_ok());
        
        let health = protection.get_health_report();
        assert_eq!(health.total_validations, 1);
    }

    #[test]
    fn test_forbidden_pairing_detection() {
        let protection = SemanticProtection::new_critical(vec![
            SemanticBase::Ares,      // Conflict
            SemanticBase::Dionysus,  // Chaos - This is a forbidden pairing
        ]);

        let test_data = TestData::new("test_forbidden", "Destructive chaos content");
        let result = protection.validate_semantic_integrity(&test_data, "forbidden_test");
        
        // Should detect forbidden pairing and reject
        assert!(result.is_err());
        
        if let Err(SemanticProtectionError::MutationDetected(msg)) = result {
            assert!(msg.contains("Forbidden pairing detected"));
        } else {
            panic!("Expected MutationDetected error");
        }
    }

    #[test]
    fn test_protection_builder() {
        let result = SemanticProtectionBuilder::new()
            .protection_level(ProtectionLevel::Critical)
            .semantic_base(SemanticBase::Apollo)
            .semantic_base(SemanticBase::Athena)
            .mutation_threshold(0.9)
            .evolution_enabled(false)
            .build();

        assert!(result.is_ok());
        let protection = result.unwrap();
        let health = protection.get_health_report();
        assert_eq!(health.total_validations, 0);
    }

    #[test]
    fn test_protection_builder_empty_profile_error() {
        let result = SemanticProtectionBuilder::new()
            .protection_level(ProtectionLevel::Critical)
            .build();

        assert!(result.is_err());
        if let Err(SemanticProtectionError::ConfigurationError(msg)) = result {
            assert!(msg.contains("Semantic profile cannot be empty"));
        } else {
            panic!("Expected ConfigurationError");
        }
    }

    #[test]
    fn test_function_guard_success() {
        let protection = SemanticProtection::new_critical(vec![
            SemanticBase::Apollo,
            SemanticBase::Athena,
        ]);

        let test_function = |data: TestData| -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
            Ok(format!("Processed: {}", data.content))
        };

        let guard = FunctionGuard::new(test_function, protection, "test_function".to_string());
        let test_data = TestData::new("test_1", "Valid content");
        
        let result = guard.call(test_data);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Processed: Valid content");
    }

    #[test]
    fn test_function_guard_semantic_failure() {
        let protection = SemanticProtection::new_critical(vec![
            SemanticBase::Ares,      // Conflict
            SemanticBase::Dionysus,  // Chaos - Forbidden pairing
        ]);

        let test_function = |data: TestData| -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
            Ok(format!("Processed: {}", data.content))
        };

        let guard = FunctionGuard::new(test_function, protection, "test_function".to_string());
        let test_data = TestData::new("test_1", "Forbidden content");
        
        let result = guard.call(test_data);
        assert!(result.is_err());
    }

    #[test]
    fn test_common_protections() {
        let auth_protection = CommonProtections::authentication();
        let api_protection = CommonProtections::api_endpoint();
        let blockchain_protection = CommonProtections::blockchain_rpc();
        let analytics_protection = CommonProtections::analytics_engine();
        let octave_protection = CommonProtections::octave_context();

        // Test that all protections are properly configured
        let test_data = TestData::new("test", "Valid content");
        
        assert!(auth_protection.validate_semantic_integrity(&test_data, "auth").is_ok());
        assert!(api_protection.validate_semantic_integrity(&test_data, "api").is_ok());
        assert!(blockchain_protection.validate_semantic_integrity(&test_data, "blockchain").is_ok());
        assert!(analytics_protection.validate_semantic_integrity(&test_data, "analytics").is_ok());
        assert!(octave_protection.validate_semantic_integrity(&test_data, "octave").is_ok());
    }

    #[test]
    fn test_guardian_antibody_creation() {
        let mut antibody = GuardianAntibody::new(
            "TEST_GUARDIAN".to_string(),
            SemanticBase::Athena,
        );

        let threat_pattern = ThreatPattern {
            pattern_id: "test_threat".to_string(),
            pattern_type: ThreatType::LogicalInconsistency,
            severity: MutationSeverity::High,
            detection_regex: Some(r"contradiction|inconsistent".to_string()),
            semantic_signature: vec!["logical_error".to_string()],
            confidence_threshold: 0.7,
        };

        antibody.add_threat_pattern(threat_pattern);
        assert_eq!(antibody.detects.len(), 1);
    }

    #[test]
    fn test_antibody_threat_detection() {
        let mut antibody = GuardianAntibody::new(
            "ATHENA_WISDOM_GUARDIAN".to_string(),
            SemanticBase::Athena,
        );

        let threat_pattern = ThreatPattern {
            pattern_id: "logical_inconsistency".to_string(),
            pattern_type: ThreatType::LogicalInconsistency,
            severity: MutationSeverity::High,
            detection_regex: Some(r"contradiction".to_string()),
            semantic_signature: vec!["inconsistent".to_string()],
            confidence_threshold: 0.5,
        };

        antibody.add_threat_pattern(threat_pattern);

        let threats = antibody.scan_for_threats(
            "This statement is inconsistent and contains a contradiction",
            "test_context"
        );

        assert!(!threats.is_empty());
        assert_eq!(threats[0].mutation_type, MutationType::Harmful);
        assert_eq!(threats[0].severity, MutationSeverity::High);
    }

    #[test]
    fn test_antibody_false_positive_handling() {
        let mut antibody = GuardianAntibody::new(
            "TEST_GUARDIAN".to_string(),
            SemanticBase::Apollo,
        );

        // Report false positives
        antibody.report_false_positive("test_pattern");
        antibody.report_false_positive("test_pattern");

        let metrics = antibody.get_health_metrics();
        assert!(metrics.false_positive_rate >= 0.0);
    }

    #[test]
    fn test_mutation_severity_ordering() {
        assert!(MutationSeverity::Critical > MutationSeverity::High);
        assert!(MutationSeverity::High > MutationSeverity::Medium);
        assert!(MutationSeverity::Medium > MutationSeverity::Low);
    }

    #[test]
    fn test_semantic_token_creation() {
        let token = SemanticToken {
            id: uuid::Uuid::new_v4(),
            base: SemanticBase::Apollo,
            value: json!({"test": "value"}),
            essence: SemanticEssence {
                archetype: "test_archetype".to_string(),
                domain: vec!["test_domain".to_string()],
                context: "test_context".to_string(),
                mutation_generation: 0,
            },
            metadata: SemanticMetadata {
                source: "test_source".to_string(),
                timestamp: chrono::Utc::now(),
                checksum: "test_checksum".to_string(),
                parent_tokens: Vec::new(),
            },
        };

        assert_eq!(token.base, SemanticBase::Apollo);
        assert_eq!(token.essence.archetype, "test_archetype");
    }

    #[test]
    fn test_semantic_strand_creation() {
        let strand = SemanticStrand {
            id: uuid::Uuid::new_v4(),
            tokens: Vec::new(),
            integrity: 1.0,
            evolution_generation: 0,
            parent_strand: None,
            mutation_history: Vec::new(),
            fitness_score: 1.0,
            created_at: chrono::Utc::now(),
            last_modified: chrono::Utc::now(),
        };

        assert_eq!(strand.integrity, 1.0);
        assert_eq!(strand.evolution_generation, 0);
        assert!(strand.tokens.is_empty());
    }

    #[test]
    fn test_health_metrics_tracking() {
        let protection = SemanticProtection::new_critical(vec![
            SemanticBase::Apollo,
            SemanticBase::Athena,
        ]);

        let test_data = TestData::new("test", "Valid content");
        
        // Perform multiple validations
        for _ in 0..5 {
            let _ = protection.validate_semantic_integrity(&test_data, "test");
        }

        let health = protection.get_health_report();
        assert_eq!(health.total_validations, 5);
    }

    #[test]
    fn test_error_types() {
        let mutation_error = SemanticProtectionError::MutationDetected("test".to_string());
        let validation_error = SemanticProtectionError::ValidationFailed("test".to_string());
        let forbidden_error = SemanticProtectionError::ForbiddenSequence("test".to_string());
        let integrity_error = SemanticProtectionError::IntegrityCompromised("test".to_string());

        assert!(matches!(mutation_error, SemanticProtectionError::MutationDetected(_)));
        assert!(matches!(validation_error, SemanticProtectionError::ValidationFailed(_)));
        assert!(matches!(forbidden_error, SemanticProtectionError::ForbiddenSequence(_)));
        assert!(matches!(integrity_error, SemanticProtectionError::IntegrityCompromised(_)));
    }
}
