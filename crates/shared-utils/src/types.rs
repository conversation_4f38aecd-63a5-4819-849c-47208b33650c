//! Common types and utilities for the Web3 Gaming Intelligence Platform

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Blockchain identifier
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
#[cfg_attr(feature = "sqlx", derive(sqlx::Type))]
#[cfg_attr(feature = "sqlx", sqlx(type_name = "text"))]
pub enum Chain {
    Ethereum,
    Polygon,
    BSC,
    Arbitrum,
    Optimism,
    Solana,
    Base,
    Avalanche,
    Immutable,
    Ronin,
}

impl Chain {
    pub fn as_str(&self) -> &'static str {
        match self {
            Chain::Ethereum => "ethereum",
            Chain::Polygon => "polygon",
            Chain::BSC => "bsc",
            Chain::Arbitrum => "arbitrum",
            Chain::Optimism => "optimism",
            Chain::Solana => "solana",
            Chain::Base => "base",
            Chain::Avalanche => "avalanche",
            Chain::Immutable => "immutable",
            Chain::Ronin => "ronin",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "ethereum" | "eth" => Some(Chain::Ethereum),
            "polygon" | "matic" => Some(Chain::Polygon),
            "bsc" | "binance" => Some(Chain::BSC),
            "arbitrum" | "arb" => Some(Chain::Arbitrum),
            "optimism" | "op" => Some(Chain::Optimism),
            "solana" | "sol" => Some(Chain::Solana),
            "base" => Some(Chain::Base),
            "avalanche" | "avax" => Some(Chain::Avalanche),
            "immutable" | "imx" => Some(Chain::Immutable),
            "ronin" | "ron" => Some(Chain::Ronin),
            _ => None,
        }
    }

    pub fn is_evm(&self) -> bool {
        !matches!(self, Chain::Solana)
    }
}

impl std::fmt::Display for Chain {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

/// Address type that works across different blockchains
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
#[cfg_attr(feature = "sqlx", derive(sqlx::Type))]
#[cfg_attr(feature = "sqlx", sqlx(transparent))]
pub struct Address(pub String);

impl Address {
    pub fn new(address: impl Into<String>) -> Self {
        Self(address.into())
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Validate address format for the given chain
    pub fn is_valid_for_chain(&self, chain: &Chain) -> bool {
        match chain {
            Chain::Solana => {
                // Solana addresses are base58 encoded and typically 32-44 characters
                self.0.len() >= 32 && self.0.len() <= 44 && self.0.chars().all(|c| c.is_ascii_alphanumeric())
            }
            _ => {
                // EVM addresses are 42 characters starting with 0x
                self.0.len() == 42 && self.0.starts_with("0x") && self.0[2..].chars().all(|c| c.is_ascii_hexdigit())
            }
        }
    }
}

impl std::fmt::Display for Address {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Transaction hash type
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TxHash(pub String);

impl TxHash {
    pub fn new(hash: impl Into<String>) -> Self {
        Self(hash.into())
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }
}

/// Block number type
pub type BlockNumber = u64;

/// Gaming project category
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum GamingCategory {
    PlayToEarn,
    NFTGame,
    Metaverse,
    DeFiGaming,
    SocialGaming,
    Strategy,
    RPG,
    Action,
    Casual,
    Other,
}

impl GamingCategory {
    pub fn as_str(&self) -> &'static str {
        match self {
            GamingCategory::PlayToEarn => "play_to_earn",
            GamingCategory::NFTGame => "nft_game",
            GamingCategory::Metaverse => "metaverse",
            GamingCategory::DeFiGaming => "defi_gaming",
            GamingCategory::SocialGaming => "social_gaming",
            GamingCategory::Strategy => "strategy",
            GamingCategory::RPG => "rpg",
            GamingCategory::Action => "action",
            GamingCategory::Casual => "casual",
            GamingCategory::Other => "other",
        }
    }
}

/// Pagination parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub limit: u32,
    pub offset: u32,
}

impl Pagination {
    pub fn new(page: u32, limit: u32) -> Self {
        let offset = (page.saturating_sub(1)) * limit;
        Self { page, limit, offset }
    }
}

/// Paginated response wrapper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub page: u32,
    pub limit: u32,
    pub total: u64,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

/// Time range for queries
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

impl TimeRange {
    pub fn new(start: DateTime<Utc>, end: DateTime<Utc>) -> Self {
        Self { start, end }
    }

    pub fn last_24h() -> Self {
        let end = Utc::now();
        let start = end - chrono::Duration::hours(24);
        Self { start, end }
    }

    pub fn last_7d() -> Self {
        let end = Utc::now();
        let start = end - chrono::Duration::days(7);
        Self { start, end }
    }

    pub fn last_30d() -> Self {
        let end = Utc::now();
        let start = end - chrono::Duration::days(30);
        Self { start, end }
    }
}

/// Generic metadata type
pub type Metadata = HashMap<String, serde_json::Value>;
