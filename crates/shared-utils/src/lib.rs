//! Shared utilities for the Web3 Gaming Intelligence Platform
//! 
//! This crate provides common functionality used across all other crates:
//! - Configuration management
//! - Error handling patterns
//! - Logging setup
//! - Common types and traits

pub mod config;
pub mod error;
pub mod logging;
pub mod types;
pub mod semantic_errors;
pub mod semantic_patterns;
pub mod semantic_protection;

pub use config::*;
pub use error::*;
pub use logging::*;
pub use types::*;
pub use semantic_errors::*;
pub use semantic_patterns::*;
pub use semantic_protection::*;
