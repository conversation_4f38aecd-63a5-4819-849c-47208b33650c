//! Error handling patterns for the Web3 Gaming Intelligence Platform

use thiserror::Error;

/// Main error type for the Web3 Gaming Intelligence Platform
#[derive(Error, Debug)]
pub enum Web3GamingError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("HTTP request error: {0}")]
    Http(#[from] reqwest::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    
    #[error("Configuration error: {0}")]
    Config(#[from] config::ConfigError),
    
    #[error("Blockchain RPC error: {message}")]
    BlockchainRpc { message: String },
    
    #[error("Contract analysis error: {message}")]
    ContractAnalysis { message: String },
    
    #[error("Authentication error: {message}")]
    Authentication { message: String },
    
    #[error("Rate limit exceeded: {message}")]
    RateLimit { message: String },
    
    #[error("WebSocket error: {message}")]
    WebSocket { message: String },
    
    #[error("Analytics error: {message}")]
    Analytics { message: String },

    #[error("Data processing error: {0}")]
    DataProcessing(String),

    #[error("Internal error: {message}")]
    Internal { message: String },
}

/// Result type alias for convenience
pub type Result<T> = std::result::Result<T, Web3GamingError>;

/// Error context trait for adding context to errors
pub trait ErrorContext<T> {
    fn with_context(self, context: &str) -> Result<T>;
}

impl<T, E> ErrorContext<T> for std::result::Result<T, E>
where
    E: std::fmt::Display,
{
    fn with_context(self, context: &str) -> Result<T> {
        self.map_err(|e| Web3GamingError::Internal {
            message: format!("{}: {}", context, e),
        })
    }
}

/// Blockchain-specific error helpers
impl Web3GamingError {
    pub fn blockchain_rpc(message: impl Into<String>) -> Self {
        Self::BlockchainRpc {
            message: message.into(),
        }
    }
    
    pub fn contract_analysis(message: impl Into<String>) -> Self {
        Self::ContractAnalysis {
            message: message.into(),
        }
    }
    
    pub fn authentication(message: impl Into<String>) -> Self {
        Self::Authentication {
            message: message.into(),
        }
    }
    
    pub fn rate_limit(message: impl Into<String>) -> Self {
        Self::RateLimit {
            message: message.into(),
        }
    }
    
    pub fn websocket(message: impl Into<String>) -> Self {
        Self::WebSocket {
            message: message.into(),
        }
    }
    
    pub fn analytics(message: impl Into<String>) -> Self {
        Self::Analytics {
            message: message.into(),
        }
    }
    
    pub fn internal(message: impl Into<String>) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }

    pub fn ml_inference(message: impl Into<String>) -> Self {
        Self::Analytics {
            message: format!("ML Inference: {}", message.into()),
        }
    }

    pub fn database_custom(message: impl Into<String>) -> Self {
        Self::Internal {
            message: format!("Database: {}", message.into()),
        }
    }
}
