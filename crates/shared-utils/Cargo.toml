[package]
name = "shared-utils"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[dependencies]
# Workspace dependencies
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
chrono.workspace = true
uuid.workspace = true
sqlx = { workspace = true, optional = true }
reqwest.workspace = true

# Configuration
config.workspace = true
dotenvy.workspace = true

# Performance
parking_lot.workspace = true

# Regex for pattern matching
regex = "1.10"

[features]
default = ["sqlx"]
sqlx = ["dep:sqlx"]

[dev-dependencies]
mockall.workspace = true
