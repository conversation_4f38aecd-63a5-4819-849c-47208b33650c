//! Dynamic Protocol System Demo
//!
//! This example demonstrates the new database-driven dynamic protocol system
//! that automatically loads gaming protocols from the database instead of
//! requiring hardcoded implementations for each game.

use data_scraper::{
    DataScrapingEngine, ScrapingConfig, LoggingConfig, MonitoringConfig, 
    GamingFilterConfig, RateLimitingConfig, SourcesConfig, DatabaseConfig,
    ChainConfig, LogFormat, LogOutput
};
use data_scraper::config::{BlockchainSourceConfig, DexSourceConfig, SocialSourceConfig, NewsSourceConfig};
use shared_utils::Chain;
use std::collections::HashMap;
use tokio::time::{sleep, Duration};
use tracing::{info, error};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_env_filter("info,data_scraper=debug")
        .init();

    info!("🚀 Starting Dynamic Gaming Protocol System Demo");

    // Create configuration for dynamic system
    let config = create_dynamic_config();

    // Initialize the data scraping engine with dynamic protocol loading
    let engine = DataScrapingEngine::new(config).await?;

    // The protocols are now automatically loaded from database!
    // No need to manually register each protocol

    // Demonstrate dynamic protocol scraping
    demonstrate_dynamic_protocol_system(&engine).await?;

    // Show system statistics
    show_dynamic_system_stats(&engine).await?;

    info!("✅ Dynamic Gaming Protocol System ready for production");
    
    Ok(())
}

/// Demonstrate the dynamic protocol system
async fn demonstrate_dynamic_protocol_system(engine: &DataScrapingEngine) -> anyhow::Result<()> {
    info!("🔄 Demonstrating dynamic protocol system...");

    // The system automatically discovers available protocols from database
    // and creates appropriate scrapers for each one

    // Test scraping all protocols for different data types
    let data_types = ["stats", "marketplace", "tokens"];

    for data_type in data_types {
        info!("📊 Scraping {} data from all protocols...", data_type);
        
        match engine.scrape_protocol_data("all", data_type).await {
            Ok(data) => {
                info!("   ✅ Collected {} data points from dynamic protocols", data.len());
                
                // Show sample data from each protocol
                let mut protocols_seen = std::collections::HashSet::new();
                for item in &data {
                    if protocols_seen.insert(&item.protocol) {
                        info!("      🎮 {}: {} (quality: {:.2})", 
                              item.protocol, item.data_type, item.quality_score);
                    }
                }
            }
            Err(e) => error!("   ❌ Failed to scrape {}: {}", data_type, e),
        }
    }

    Ok(())
}

/// Show dynamic system statistics
async fn show_dynamic_system_stats(engine: &DataScrapingEngine) -> anyhow::Result<()> {
    info!("📊 Dynamic Gaming Protocol System Statistics:");

    let stats = engine.get_stats().await?;

    info!("🎮 Dynamic Protocol Performance:");
    info!("   - Total Scrapes: {}", stats.protocol_stats.total_scrapes);
    info!("   - Success Rate: {:.1}%", 
          (stats.protocol_stats.successful_scrapes as f64 / stats.protocol_stats.total_scrapes.max(1) as f64) * 100.0);
    info!("   - Protocols Auto-Loaded: {}", stats.protocol_stats.protocols_active);
    info!("   - Data Points Collected: {}", stats.protocol_stats.data_points_collected);

    info!("🔗 System Benefits:");
    info!("   ✅ No hardcoded protocol implementations");
    info!("   ✅ Automatic protocol discovery from database");
    info!("   ✅ Easy addition of new games without code changes");
    info!("   ✅ Centralized configuration management");
    info!("   ✅ Consistent data processing across all protocols");

    info!("🚀 Ready for Production:");
    info!("   ✅ Add new games by inserting database records");
    info!("   ✅ Update protocol configurations without deployments");
    info!("   ✅ Scale to hundreds of gaming protocols");
    info!("   ✅ Maintain single source of truth in database");

    Ok(())
}

/// Create configuration for dynamic system
fn create_dynamic_config() -> ScrapingConfig {
    ScrapingConfig {
        logging: LoggingConfig {
            level: "info".to_string(),
            format: LogFormat::Pretty,
            output: LogOutput {
                console: true,
                file: Some("logs/dynamic_protocol_system.log".to_string()),
                database: false,
                metrics: true,
            },
            structured: true,
            include_metadata: true,
            buffer_size: 1000,
            flush_interval_ms: 100,
            retention_days: 90,
        },
        monitoring: MonitoringConfig {
            enabled: true,
            websocket_url: None,
            poll_interval_ms: 1000,
            batch_size: 500,
            max_connections: 20,
            reconnect_delay_ms: 1000,
            heartbeat_interval_ms: 10000,
            buffer_size: 10000,
            gaming_filter: GamingFilterConfig {
                enabled: true,
                min_confidence: 0.4,
                gaming_contracts: vec![], // Loaded dynamically from database
                gaming_tokens: vec![],    // Loaded dynamically from database
                keywords: vec![
                    "gaming".to_string(),
                    "nft".to_string(),
                    "play-to-earn".to_string(),
                    "p2e".to_string(),
                    "metaverse".to_string(),
                ],
                exclude_patterns: vec!["test".to_string(), "demo".to_string()],
            },
        },
        rate_limiting: RateLimitingConfig {
            enabled: true,
            default_requests_per_second: 20,
            burst_capacity: 100,
            adaptive: true,
            backoff_multiplier: 2.0,
            max_backoff_seconds: 300,
            per_source_limits: HashMap::new(),
        },
        chains: {
            let mut chains = HashMap::new();
            
            // Solana (for protocols like Star Atlas, Honeyland)
            chains.insert(Chain::Solana, ChainConfig {
                enabled: true,
                rpc_url: "https://api.mainnet-beta.solana.com".to_string(),
                websocket_url: Some("wss://api.mainnet-beta.solana.com".to_string()),
                requests_per_second: 25,
                block_confirmations: 1,
                start_block: Some(250000000),
                gaming_contracts: vec![], // Loaded dynamically from database
                priority: 10,
            });
            
            // Ethereum (for protocols like Axie Infinity)
            chains.insert(Chain::Ethereum, ChainConfig {
                enabled: true,
                rpc_url: "https://eth-mainnet.g.alchemy.com/v2/demo".to_string(),
                websocket_url: Some("wss://eth-mainnet.g.alchemy.com/v2/demo".to_string()),
                requests_per_second: 15,
                block_confirmations: 3,
                start_block: Some(19000000),
                gaming_contracts: vec![], // Loaded dynamically from database
                priority: 9,
            });
            
            chains
        },
        sources: SourcesConfig {
            blockchain: BlockchainSourceConfig {
                enabled: true,
                real_time: true,
                historical_backfill: true,
                transaction_monitoring: true,
                event_monitoring: true,
                contract_monitoring: true,
            },
            dex: DexSourceConfig {
                enabled: true,
                exchanges: vec!["raydium".to_string(), "orca".to_string(), "uniswap".to_string()],
                gaming_pairs_only: true,
                price_feeds: true,
                volume_tracking: true,
                liquidity_monitoring: true,
            },
            social: SocialSourceConfig {
                enabled: false,
                platforms: vec![],
                gaming_keywords: vec![],
                sentiment_analysis: false,
                influencer_tracking: false,
            },
            news: NewsSourceConfig {
                enabled: false,
                sources: vec![],
                gaming_categories: vec![],
                real_time_feeds: false,
                sentiment_analysis: false,
            },
        },
        database: DatabaseConfig {
            url: "postgresql://localhost:5432/gaming_tracker".to_string(),
            max_connections: 20,
            connection_timeout_seconds: 30,
            query_timeout_seconds: 90,
            batch_insert_size: 200,
            retry_attempts: 5,
        },
    }
}
