[package]
name = "data-scraper"
version = "0.1.0"
edition = "2021"

[dependencies]
# Async runtime
tokio = { workspace = true, features = ["full"] }
futures = "0.3"
futures-util = "0.3"
async-trait = "0.1"

# WebSocket support
tokio-tungstenite = { version = "0.21", features = ["native-tls"] }
tungstenite = "0.21"

# HTTP client
reqwest = { workspace = true, features = ["json", "stream"] }

# Serialization
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

# Logging and tracing
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }

# Time handling
chrono = { workspace = true, features = ["serde"] }

# Error handling
anyhow = { workspace = true }
thiserror = "1.0"

# Metrics and monitoring
metrics = "0.22"
metrics-exporter-prometheus = "0.13"

# Database
sqlx = { workspace = true, features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid"] }
uuid = { workspace = true, features = ["v4", "serde"] }

# Shared crates
shared-utils = { path = "../shared-utils" }
blockchain-engine = { path = "../blockchain-engine" }
database = { path = "../database" }

# Rate limiting
governor = "0.6"
nonzero_ext = "0.3"

# Configuration
config = "0.14"
