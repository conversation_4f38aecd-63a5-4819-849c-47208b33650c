//! Data Validation and Quality Engine
//!
//! COMPONENT_ESSENCE::ATHENA_DATA_QUALITY_WISDOM
//! DANGER_LEVEL::APOLLO_VALIDATION_GUARDIAN
//! PERFORMANCE_TARGET::SUB_5MS_VALIDATION_LATENCY
//! LAST_MODIFIED::PHASE_2_QUALITY_ENGINE_TRIUMPH
//! SOLUTION_TYPE::COMPREHENSIVE_DATA_QUALITY_ASSURANCE
//!
//! This module provides comprehensive data validation, quality scoring,
//! anomaly detection, and duplicate detection for scraped gaming data.

use crate::logger::{ScrapingLogger, DataQuality};
use crate::types::*;
use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use shared_utils::Chain;
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Comprehensive data validation and quality engine
#[derive(Debug)]
pub struct DataValidationEngine {
    logger: Arc<ScrapingLogger>,
    schemas: Arc<RwLock<HashMap<String, ValidationSchema>>>,
    validators: Arc<RwLock<HashMap<String, Box<dyn DataValidator + Send + Sync>>>>,
    anomaly_detector: Arc<AnomalyDetector>,
    duplicate_detector: Arc<DuplicateDetector>,
    quality_scorer: Arc<QualityScorer>,
    stats: Arc<RwLock<ValidationStats>>,
}

/// Validation schema for specific data types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationSchema {
    pub data_type: String,
    pub version: String,
    pub required_fields: Vec<FieldSchema>,
    pub optional_fields: Vec<FieldSchema>,
    pub constraints: Vec<DataConstraint>,
    pub business_rules: Vec<BusinessRule>,
    pub quality_thresholds: QualityThresholds,
}

/// Schema for individual fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldSchema {
    pub name: String,
    pub field_type: FieldType,
    pub constraints: Vec<FieldConstraint>,
    pub description: String,
    pub examples: Vec<serde_json::Value>,
}

/// Supported field types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FieldType {
    String { max_length: Option<usize> },
    Number { min: Option<f64>, max: Option<f64> },
    Integer { min: Option<i64>, max: Option<i64> },
    Boolean,
    DateTime,
    Array { item_type: Box<FieldType> },
    Object { schema: HashMap<String, FieldSchema> },
    Address,
    Hash,
    Url,
    Email,
}

/// Field-level constraints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FieldConstraint {
    NotNull,
    NotEmpty,
    MinLength(usize),
    MaxLength(usize),
    Pattern(String),
    Range(f64, f64),
    OneOf(Vec<serde_json::Value>),
    Custom(String), // Custom validation function name
}

/// Data-level constraints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataConstraint {
    pub name: String,
    pub constraint_type: ConstraintType,
    pub description: String,
    pub severity: ValidationSeverity,
}

/// Types of data constraints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConstraintType {
    UniqueKey(Vec<String>),
    ForeignKey { field: String, reference_table: String, reference_field: String },
    CheckConstraint(String), // SQL-like check constraint
    BusinessLogic(String),   // Custom business logic
}

/// Business rules for domain-specific validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusinessRule {
    pub name: String,
    pub rule_type: BusinessRuleType,
    pub condition: String,
    pub action: ValidationAction,
    pub severity: ValidationSeverity,
}

/// Types of business rules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BusinessRuleType {
    GamingTokenValidation,
    TransactionAmountCheck,
    PlayerActivityValidation,
    MarketplaceRules,
    NFTOwnershipValidation,
    Custom(String),
}

/// Actions to take when validation fails
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationAction {
    Reject,
    Warn,
    Flag,
    Quarantine,
    AutoCorrect(String),
}

/// Validation severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationSeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

/// Quality thresholds for different metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityThresholds {
    pub min_completeness: f64,
    pub min_accuracy: f64,
    pub min_consistency: f64,
    pub min_timeliness: f64,
    pub min_overall_score: f64,
}

/// Data validator trait
pub trait DataValidator: std::fmt::Debug + Send + Sync {
    fn validate(&self, data: &serde_json::Value) -> Result<ValidationResult>;
    fn validator_type(&self) -> &str;
    fn supports_data_type(&self, data_type: &str) -> bool;
}

/// Anomaly detector for identifying unusual patterns
#[derive(Debug)]
pub struct AnomalyDetector {
    patterns: Arc<RwLock<HashMap<String, AnomalyPattern>>>,
    detection_history: Arc<RwLock<Vec<AnomalyDetection>>>,
    learning_enabled: bool,
}

/// Anomaly pattern definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyPattern {
    pub pattern_id: String,
    pub data_type: String,
    pub pattern_type: AnomalyType,
    pub threshold: f64,
    pub confidence: f64,
    pub last_updated: DateTime<Utc>,
}

/// Types of anomalies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnomalyType {
    StatisticalOutlier,
    TemporalAnomaly,
    VolumeSpike,
    PatternDeviation,
    DataDrift,
    Custom(String),
}

/// Anomaly detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyDetection {
    pub detection_id: Uuid,
    pub data_id: String,
    pub anomaly_type: AnomalyType,
    pub severity: f64,
    pub description: String,
    pub detected_at: DateTime<Utc>,
    pub false_positive_probability: f64,
}

/// Duplicate detector for identifying duplicate data
#[derive(Debug)]
pub struct DuplicateDetector {
    fingerprints: Arc<RwLock<HashMap<String, DataFingerprint>>>,
    similarity_threshold: f64,
    deduplication_rules: Arc<RwLock<HashMap<String, DeduplicationRule>>>,
}

/// Data fingerprint for duplicate detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataFingerprint {
    pub fingerprint_id: String,
    pub data_type: String,
    pub hash: String,
    pub key_fields: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub source: String,
}

/// Deduplication rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeduplicationRule {
    pub data_type: String,
    pub key_fields: Vec<String>,
    pub similarity_algorithm: SimilarityAlgorithm,
    pub threshold: f64,
    pub action: DeduplicationAction,
}

/// Similarity algorithms for duplicate detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SimilarityAlgorithm {
    ExactMatch,
    FuzzyMatch,
    SemanticSimilarity,
    StructuralSimilarity,
}

/// Actions for handling duplicates
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeduplicationAction {
    Reject,
    Merge,
    KeepLatest,
    KeepBest,
    Flag,
}

/// Quality scorer for calculating data quality metrics
#[derive(Debug)]
pub struct QualityScorer {
    scoring_rules: Arc<RwLock<HashMap<String, QualityScoringRule>>>,
    weights: QualityWeights,
}

/// Quality scoring rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityScoringRule {
    pub data_type: String,
    pub completeness_weight: f64,
    pub accuracy_weight: f64,
    pub consistency_weight: f64,
    pub timeliness_weight: f64,
    pub custom_metrics: HashMap<String, f64>,
}

/// Weights for different quality dimensions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityWeights {
    pub completeness: f64,
    pub accuracy: f64,
    pub consistency: f64,
    pub timeliness: f64,
}

/// Validation statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStats {
    pub total_validations: u64,
    pub successful_validations: u64,
    pub failed_validations: u64,
    pub anomalies_detected: u64,
    pub duplicates_detected: u64,
    pub average_quality_score: f64,
    pub validation_performance: HashMap<String, ValidationPerformance>,
    pub last_updated: DateTime<Utc>,
}

/// Performance metrics for validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationPerformance {
    pub data_type: String,
    pub average_validation_time_ms: f64,
    pub success_rate: f64,
    pub quality_trend: f64,
    pub last_validation: Option<DateTime<Utc>>,
}

impl DataValidationEngine {
    /// Create a new data validation engine
    pub async fn new(logger: Arc<ScrapingLogger>) -> Result<Self> {
        let anomaly_detector = Arc::new(AnomalyDetector::new());
        let duplicate_detector = Arc::new(DuplicateDetector::new());
        let quality_scorer = Arc::new(QualityScorer::new());

        let engine = Self {
            logger,
            schemas: Arc::new(RwLock::new(HashMap::new())),
            validators: Arc::new(RwLock::new(HashMap::new())),
            anomaly_detector,
            duplicate_detector,
            quality_scorer,
            stats: Arc::new(RwLock::new(ValidationStats::default())),
        };

        // Initialize with default schemas
        engine.initialize_default_schemas().await?;
        
        info!("🛡️ Data Validation Engine initialized");
        Ok(engine)
    }

    /// Register a validation schema
    pub async fn register_schema(&self, schema: ValidationSchema) -> Result<()> {
        let data_type = schema.data_type.clone();
        self.schemas.write().await.insert(data_type.clone(), schema);
        info!("📋 Registered validation schema for: {}", data_type);
        Ok(())
    }

    /// Register a custom validator
    pub async fn register_validator(&self, validator: Box<dyn DataValidator + Send + Sync>) -> Result<()> {
        let validator_type = validator.validator_type().to_string();
        self.validators.write().await.insert(validator_type.clone(), validator);
        info!("🔧 Registered custom validator: {}", validator_type);
        Ok(())
    }

    /// Validate data comprehensively
    pub async fn validate_data(
        &self,
        data_type: &str,
        data: &serde_json::Value,
        source: &str,
    ) -> Result<ValidationResult> {
        let start_time = Instant::now();
        let validation_id = Uuid::new_v4();

        debug!("🔍 Starting validation for data type: {}", data_type);

        // Schema validation
        let schema_result = self.validate_schema(data_type, data).await?;
        
        // Custom validator validation
        let custom_result = self.run_custom_validators(data_type, data).await?;
        
        // Anomaly detection
        let anomaly_result = self.detect_anomalies(data_type, data).await?;
        
        // Duplicate detection
        let duplicate_result = self.detect_duplicates(data_type, data, source).await?;
        
        // Quality scoring
        let quality_score = self.calculate_quality_score(data_type, data, &schema_result).await?;

        // Combine all validation results
        let mut combined_result = ValidationResult {
            is_valid: schema_result.is_valid && custom_result.is_valid && !duplicate_result.is_duplicate,
            quality_score: quality_score.score,
            errors: schema_result.errors,
            warnings: schema_result.warnings,
            metadata: HashMap::new(),
        };

        // Add custom validation errors
        combined_result.errors.extend(custom_result.errors);
        combined_result.warnings.extend(custom_result.warnings);

        // Add anomaly warnings
        if !anomaly_result.is_empty() {
            combined_result.warnings.push(format!("Detected {} anomalies", anomaly_result.len()));
        }

        // Add duplicate warnings
        if duplicate_result.is_duplicate {
            combined_result.warnings.push("Potential duplicate detected".to_string());
            combined_result.is_valid = false;
        }

        // Update statistics
        self.update_validation_stats(data_type, &combined_result, start_time.elapsed()).await?;

        let duration = start_time.elapsed();
        debug!("✅ Validation completed in {:?} (valid: {}, quality: {:.2})", 
               duration, combined_result.is_valid, combined_result.quality_score);

        Ok(combined_result)
    }

    /// Validate against schema
    async fn validate_schema(&self, data_type: &str, data: &serde_json::Value) -> Result<ValidationResult> {
        let schemas = self.schemas.read().await;
        
        if let Some(schema) = schemas.get(data_type) {
            // Simplified schema validation
            let mut errors = Vec::new();
            let mut warnings = Vec::new();

            // Check required fields
            if let serde_json::Value::Object(obj) = data {
                for field_schema in &schema.required_fields {
                    if !obj.contains_key(&field_schema.name) {
                        errors.push(ValidationError {
                            field: field_schema.name.clone(),
                            error_type: ValidationErrorType::MissingField,
                            message: format!("Required field '{}' is missing", field_schema.name),
                            severity: ErrorSeverity::High,
                        });
                    }
                }
            }

            Ok(ValidationResult {
                is_valid: errors.is_empty(),
                quality_score: if errors.is_empty() { 1.0 } else { 0.5 },
                errors,
                warnings,
                metadata: HashMap::new(),
            })
        } else {
            warn!("⚠️ No schema found for data type: {}", data_type);
            Ok(ValidationResult {
                is_valid: true,
                quality_score: 0.8, // Lower score for unvalidated data
                errors: vec![],
                warnings: vec!["No validation schema available".to_string()],
                metadata: HashMap::new(),
            })
        }
    }

    /// Run custom validators
    async fn run_custom_validators(&self, data_type: &str, data: &serde_json::Value) -> Result<ValidationResult> {
        let validators = self.validators.read().await;
        let mut combined_result = ValidationResult {
            is_valid: true,
            quality_score: 1.0,
            errors: vec![],
            warnings: vec![],
            metadata: HashMap::new(),
        };

        for validator in validators.values() {
            if validator.supports_data_type(data_type) {
                match validator.validate(data) {
                    Ok(result) => {
                        combined_result.is_valid &= result.is_valid;
                        combined_result.quality_score = combined_result.quality_score.min(result.quality_score);
                        combined_result.errors.extend(result.errors);
                        combined_result.warnings.extend(result.warnings);
                    }
                    Err(e) => {
                        warn!("Custom validator failed: {}", e);
                        combined_result.warnings.push(format!("Validator error: {}", e));
                    }
                }
            }
        }

        Ok(combined_result)
    }

    /// Detect anomalies in data
    async fn detect_anomalies(&self, data_type: &str, data: &serde_json::Value) -> Result<Vec<AnomalyDetection>> {
        self.anomaly_detector.detect_anomalies(data_type, data).await
    }

    /// Detect duplicates
    async fn detect_duplicates(&self, data_type: &str, data: &serde_json::Value, source: &str) -> Result<DuplicateDetectionResult> {
        self.duplicate_detector.check_duplicate(data_type, data, source).await
    }

    /// Calculate quality score
    async fn calculate_quality_score(&self, data_type: &str, data: &serde_json::Value, validation_result: &ValidationResult) -> Result<DataQuality> {
        self.quality_scorer.calculate_quality(data_type, data, validation_result).await
    }

    /// Initialize default schemas for gaming data
    async fn initialize_default_schemas(&self) -> Result<()> {
        // Gaming transaction schema
        let gaming_transaction_schema = ValidationSchema {
            data_type: "gaming_transaction".to_string(),
            version: "1.0".to_string(),
            required_fields: vec![
                FieldSchema {
                    name: "transaction_hash".to_string(),
                    field_type: FieldType::Hash,
                    constraints: vec![FieldConstraint::NotNull, FieldConstraint::NotEmpty],
                    description: "Unique transaction hash".to_string(),
                    examples: vec![serde_json::json!("0x1234567890abcdef")],
                },
                FieldSchema {
                    name: "chain".to_string(),
                    field_type: FieldType::String { max_length: Some(20) },
                    constraints: vec![FieldConstraint::OneOf(vec![
                        serde_json::json!("Solana"),
                        serde_json::json!("Ethereum"),
                        serde_json::json!("Polygon"),
                    ])],
                    description: "Blockchain network".to_string(),
                    examples: vec![serde_json::json!("Solana")],
                },
            ],
            optional_fields: vec![],
            constraints: vec![],
            business_rules: vec![],
            quality_thresholds: QualityThresholds {
                min_completeness: 0.9,
                min_accuracy: 0.95,
                min_consistency: 0.9,
                min_timeliness: 0.8,
                min_overall_score: 0.85,
            },
        };

        self.register_schema(gaming_transaction_schema).await?;
        Ok(())
    }

    /// Update validation statistics
    async fn update_validation_stats(&self, data_type: &str, result: &ValidationResult, duration: std::time::Duration) -> Result<()> {
        let mut stats = self.stats.write().await;
        stats.total_validations += 1;
        
        if result.is_valid {
            stats.successful_validations += 1;
        } else {
            stats.failed_validations += 1;
        }

        // Update running average
        let total = stats.total_validations as f64;
        stats.average_quality_score = (stats.average_quality_score * (total - 1.0) + result.quality_score) / total;

        // Update per-data-type performance
        let performance = stats.validation_performance.entry(data_type.to_string()).or_insert(ValidationPerformance {
            data_type: data_type.to_string(),
            average_validation_time_ms: 0.0,
            success_rate: 0.0,
            quality_trend: 0.0,
            last_validation: None,
        });

        performance.average_validation_time_ms = duration.as_millis() as f64;
        performance.last_validation = Some(Utc::now());

        stats.last_updated = Utc::now();
        Ok(())
    }

    /// Get validation statistics
    pub async fn get_stats(&self) -> ValidationStats {
        self.stats.read().await.clone()
    }
}

/// Duplicate detection result
#[derive(Debug, Clone)]
pub struct DuplicateDetectionResult {
    pub is_duplicate: bool,
    pub similarity_score: f64,
    pub matching_fingerprints: Vec<String>,
}

impl AnomalyDetector {
    pub fn new() -> Self {
        Self {
            patterns: Arc::new(RwLock::new(HashMap::new())),
            detection_history: Arc::new(RwLock::new(Vec::new())),
            learning_enabled: true,
        }
    }

    pub async fn detect_anomalies(&self, _data_type: &str, _data: &serde_json::Value) -> Result<Vec<AnomalyDetection>> {
        // Simplified anomaly detection - would be more sophisticated in real implementation
        Ok(vec![])
    }
}

impl DuplicateDetector {
    pub fn new() -> Self {
        Self {
            fingerprints: Arc::new(RwLock::new(HashMap::new())),
            similarity_threshold: 0.95,
            deduplication_rules: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn check_duplicate(&self, _data_type: &str, _data: &serde_json::Value, _source: &str) -> Result<DuplicateDetectionResult> {
        // Simplified duplicate detection
        Ok(DuplicateDetectionResult {
            is_duplicate: false,
            similarity_score: 0.0,
            matching_fingerprints: vec![],
        })
    }
}

impl QualityScorer {
    pub fn new() -> Self {
        Self {
            scoring_rules: Arc::new(RwLock::new(HashMap::new())),
            weights: QualityWeights {
                completeness: 0.3,
                accuracy: 0.3,
                consistency: 0.2,
                timeliness: 0.2,
            },
        }
    }

    pub async fn calculate_quality(&self, _data_type: &str, _data: &serde_json::Value, validation_result: &ValidationResult) -> Result<DataQuality> {
        // Simplified quality calculation
        let completeness = if validation_result.is_valid { 1.0 } else { 0.8 };
        let accuracy = validation_result.quality_score;
        let consistency = 1.0; // Would be calculated based on historical data
        let timeliness = 1.0;   // Would be calculated based on data freshness

        let overall_score = completeness * self.weights.completeness +
                           accuracy * self.weights.accuracy +
                           consistency * self.weights.consistency +
                           timeliness * self.weights.timeliness;

        Ok(DataQuality {
            score: overall_score,
            completeness,
            accuracy,
            consistency,
            timeliness,
            issues: validation_result.errors.iter().map(|e| e.message.clone()).collect(),
            validation_errors: validation_result.warnings.clone(),
        })
    }
}

impl Default for ValidationStats {
    fn default() -> Self {
        Self {
            total_validations: 0,
            successful_validations: 0,
            failed_validations: 0,
            anomalies_detected: 0,
            duplicates_detected: 0,
            average_quality_score: 0.0,
            validation_performance: HashMap::new(),
            last_updated: Utc::now(),
        }
    }
}
