//! Real-Time Transaction Monitor
//!
//! This module provides WebSocket-based real-time monitoring of transactions
//! and events across all supported blockchains with comprehensive logging.

use crate::config::{ScrapingConfig, MonitoringConfig, GamingFilterConfig};
use crate::logger::{ScrapingLogger, ScrapingAttempt, ScrapingResult, PerformanceMetrics, DataQuality};
use crate::types::*;
use anyhow::Result;
use blockchain_engine::multi_chain::{MultiChainClient, ChainConfig};
use chrono::{DateTime, Utc};
use futures_util::{SinkExt, StreamExt};
use serde::{Deserialize, Serialize};
use shared_utils::{Chain, Web3GamingError};
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Real-time transaction monitor
#[derive(Debug)]
pub struct TransactionMonitor {
    config: ScrapingConfig,
    logger: Arc<ScrapingLogger>,
    multi_chain_client: Arc<MultiChainClient>,
    connections: Arc<RwLock<HashMap<Chain, WebSocketConnection>>>,
    stats: Arc<RwLock<MonitorStats>>,
    event_sender: mpsc::UnboundedSender<MonitorEvent>,
    event_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<MonitorEvent>>>>,
    counters: MonitorCounters,
}

/// WebSocket connection for a specific chain
#[derive(Debug)]
struct WebSocketConnection {
    chain: Chain,
    url: String,
    connected: bool,
    last_heartbeat: DateTime<Utc>,
    reconnect_attempts: u32,
    message_count: u64,
}

/// Monitor statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorStats {
    pub total_connections: usize,
    pub active_connections: usize,
    pub total_transactions_monitored: u64,
    pub gaming_transactions_detected: u64,
    pub total_events_processed: u64,
    pub average_processing_time_ms: f64,
    pub uptime_seconds: u64,
    pub last_updated: DateTime<Utc>,
    pub chain_stats: HashMap<String, ChainMonitorStats>,
}

/// Per-chain monitoring statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainMonitorStats {
    pub chain: Chain,
    pub connected: bool,
    pub transactions_monitored: u64,
    pub gaming_transactions: u64,
    pub events_processed: u64,
    pub last_transaction_time: Option<DateTime<Utc>>,
    pub average_block_time_ms: f64,
    pub connection_uptime_seconds: u64,
}

/// Monitor event types
#[derive(Debug, Clone)]
enum MonitorEvent {
    TransactionDetected {
        chain: Chain,
        transaction: TransactionData,
        is_gaming: bool,
        confidence: f64,
    },
    ConnectionEstablished {
        chain: Chain,
        url: String,
    },
    ConnectionLost {
        chain: Chain,
        error: String,
    },
    HeartbeatReceived {
        chain: Chain,
        timestamp: DateTime<Utc>,
    },
    FilterMatch {
        chain: Chain,
        transaction_hash: String,
        match_type: String,
        confidence: f64,
    },
}

/// Atomic counters for performance
#[derive(Debug)]
struct MonitorCounters {
    total_transactions: Arc<AtomicU64>,
    gaming_transactions: Arc<AtomicU64>,
    total_events: Arc<AtomicU64>,
    connection_attempts: Arc<AtomicU64>,
    successful_connections: Arc<AtomicU64>,
}

impl TransactionMonitor {
    /// Create a new transaction monitor
    pub async fn new(config: ScrapingConfig, logger: Arc<ScrapingLogger>) -> Result<Self> {
        let multi_chain_client = Arc::new(MultiChainClient::new());
        
        // Initialize multi-chain client with configured chains
        let mut client = multi_chain_client.as_ref().clone();
        for (chain, chain_config) in &config.chains {
            let mc_config = ChainConfig {
                rpc_url: chain_config.rpc_url.clone(),
                chain_id: None,
                block_time_ms: 1000, // Default 1s
                max_retries: 3,
                timeout_seconds: 30,
                rate_limit_per_second: chain_config.requests_per_second,
            };
            client.add_chain(*chain, mc_config).await?;
        }

        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        
        let counters = MonitorCounters {
            total_transactions: Arc::new(AtomicU64::new(0)),
            gaming_transactions: Arc::new(AtomicU64::new(0)),
            total_events: Arc::new(AtomicU64::new(0)),
            connection_attempts: Arc::new(AtomicU64::new(0)),
            successful_connections: Arc::new(AtomicU64::new(0)),
        };

        Ok(Self {
            config,
            logger,
            multi_chain_client: Arc::new(client),
            connections: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(MonitorStats::default())),
            event_sender,
            event_receiver: Arc::new(RwLock::new(Some(event_receiver))),
            counters,
        })
    }

    /// Start the transaction monitor
    pub async fn start(&self) -> Result<()> {
        if !self.config.monitoring.enabled {
            info!("📊 Transaction monitoring is disabled");
            return Ok(());
        }

        info!("🚀 Starting Real-Time Transaction Monitor...");

        // Log monitoring start attempt
        let attempt = ScrapingAttempt {
            id: Uuid::new_v4(),
            source: "transaction_monitor".to_string(),
            chain: None,
            target: "multi_chain_websockets".to_string(),
            method: "WebSocket".to_string(),
            parameters: serde_json::to_value(&self.config.monitoring)?,
            timestamp: Utc::now(),
            expected_data_type: "real_time_transactions".to_string(),
        };
        let attempt_id = attempt.id;
        self.logger.log_scraping_attempt(attempt).await?;

        // Start event processing task
        self.start_event_processor().await?;

        // Start WebSocket connections for each configured chain
        self.start_websocket_connections().await?;

        // Start health monitoring task
        self.start_health_monitor().await;

        // Start statistics update task
        self.start_stats_updater().await;

        info!("✅ Real-Time Transaction Monitor started successfully");
        
        // Log successful start
        let result = ScrapingResult {
            attempt_id,
            success: true,
            timestamp: Utc::now(),
            duration_ms: 100, // Startup time
            data_size_bytes: None,
            items_count: Some(self.config.chains.len()),
            error_message: None,
            error_type: None,
            retry_count: 0,
            data_quality: Some(DataQuality {
                score: 1.0,
                completeness: 1.0,
                accuracy: 1.0,
                consistency: 1.0,
                timeliness: 1.0,
                issues: vec![],
                validation_errors: vec![],
            }),
            performance_metrics: Some(PerformanceMetrics {
                duration_ms: 100,
                data_size_bytes: 0,
                throughput_items_per_second: 0.0,
                memory_usage_mb: 0.0,
                cpu_usage_percent: 0.0,
                network_latency_ms: 0,
            }),
        };
        self.logger.log_scraping_result(result).await?;

        // Keep running indefinitely
        self.run_forever().await
    }

    /// Start WebSocket connections for all configured chains
    async fn start_websocket_connections(&self) -> Result<()> {
        for (chain, chain_config) in &self.config.chains {
            if !chain_config.enabled {
                continue;
            }

            if let Some(ws_url) = &chain_config.websocket_url {
                self.start_chain_websocket(*chain, ws_url.clone()).await?;
            } else {
                // Fall back to polling for chains without WebSocket support
                self.start_chain_polling(*chain).await?;
            }
        }

        Ok(())
    }

    /// Start WebSocket connection for a specific chain
    async fn start_chain_websocket(&self, chain: Chain, url: String) -> Result<()> {
        let connections = Arc::clone(&self.connections);
        let event_sender = self.event_sender.clone();
        let logger = Arc::clone(&self.logger);
        let config = self.config.monitoring.clone();
        
        self.counters.connection_attempts.fetch_add(1, Ordering::Relaxed);

        tokio::spawn(async move {
            let mut reconnect_attempts = 0;
            let max_reconnect_attempts = 10;

            loop {
                match Self::connect_websocket(&url, chain, &event_sender, &logger, &config).await {
                    Ok(_) => {
                        // Update connection status
                        {
                            let mut conns = connections.write().await;
                            conns.insert(chain, WebSocketConnection {
                                chain,
                                url: url.clone(),
                                connected: true,
                                last_heartbeat: Utc::now(),
                                reconnect_attempts,
                                message_count: 0,
                            });
                        }

                        // Send connection established event
                        let _ = event_sender.send(MonitorEvent::ConnectionEstablished {
                            chain,
                            url: url.clone(),
                        });

                        reconnect_attempts = 0;
                        info!("🔗 WebSocket connected for chain: {:?}", chain);
                    }
                    Err(e) => {
                        error!("❌ WebSocket connection failed for chain {:?}: {}", chain, e);
                        
                        // Send connection lost event
                        let _ = event_sender.send(MonitorEvent::ConnectionLost {
                            chain,
                            error: e.to_string(),
                        });

                        reconnect_attempts += 1;
                        if reconnect_attempts >= max_reconnect_attempts {
                            error!("🚫 Max reconnection attempts reached for chain {:?}", chain);
                            break;
                        }

                        // Exponential backoff
                        let delay = Duration::from_millis(config.reconnect_delay_ms * (2_u64.pow(reconnect_attempts.min(5))));
                        warn!("⏳ Retrying connection for chain {:?} in {:?}", chain, delay);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        });

        Ok(())
    }

    /// Connect to WebSocket and handle messages
    async fn connect_websocket(
        url: &str,
        chain: Chain,
        event_sender: &mpsc::UnboundedSender<MonitorEvent>,
        logger: &Arc<ScrapingLogger>,
        config: &MonitoringConfig,
    ) -> Result<()> {
        let (ws_stream, _) = connect_async(url).await?;
        let (mut write, mut read) = ws_stream.split();

        // Send subscription message based on chain
        let subscription_msg = Self::get_subscription_message(chain)?;
        write.send(Message::Text(subscription_msg)).await?;

        // Handle incoming messages
        while let Some(message) = read.next().await {
            match message {
                Ok(Message::Text(text)) => {
                    if let Err(e) = Self::process_websocket_message(
                        &text, chain, event_sender, logger, config
                    ).await {
                        warn!("Failed to process WebSocket message: {}", e);
                    }
                }
                Ok(Message::Ping(data)) => {
                    // Respond to ping with pong
                    if let Err(e) = write.send(Message::Pong(data)).await {
                        error!("Failed to send pong: {}", e);
                        break;
                    }
                }
                Ok(Message::Close(_)) => {
                    info!("WebSocket connection closed for chain: {:?}", chain);
                    break;
                }
                Err(e) => {
                    error!("WebSocket error for chain {:?}: {}", chain, e);
                    break;
                }
                _ => {}
            }
        }

        Ok(())
    }

    /// Get subscription message for a specific chain
    fn get_subscription_message(chain: Chain) -> Result<String> {
        match chain {
            Chain::Solana => {
                // Subscribe to all transactions
                Ok(serde_json::json!({
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "transactionSubscribe",
                    "params": [
                        {},
                        {
                            "commitment": "confirmed",
                            "encoding": "json",
                            "transactionDetails": "full",
                            "showRewards": false
                        }
                    ]
                }).to_string())
            }
            Chain::Ethereum | Chain::Polygon | Chain::Arbitrum => {
                // Subscribe to pending transactions
                Ok(serde_json::json!({
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "eth_subscribe",
                    "params": ["newPendingTransactions"]
                }).to_string())
            }
            _ => Err(Web3GamingError::blockchain_rpc(format!("Unsupported chain for WebSocket: {:?}", chain)).into()),
        }
    }

    /// Process incoming WebSocket message
    async fn process_websocket_message(
        message: &str,
        chain: Chain,
        event_sender: &mpsc::UnboundedSender<MonitorEvent>,
        logger: &Arc<ScrapingLogger>,
        config: &MonitoringConfig,
    ) -> Result<()> {
        let start_time = Instant::now();
        
        // Parse the message
        let parsed: serde_json::Value = serde_json::from_str(message)?;
        
        // Extract transaction data based on chain
        if let Some(transaction_data) = Self::extract_transaction_data(&parsed, chain)? {
            // Apply gaming filter
            let (is_gaming, confidence) = Self::apply_gaming_filter(&transaction_data, &config.gaming_filter);
            
            // Send event
            let _ = event_sender.send(MonitorEvent::TransactionDetected {
                chain,
                transaction: transaction_data,
                is_gaming,
                confidence,
            });

            // Log performance metrics
            let duration = start_time.elapsed();
            debug!("📊 Processed transaction in {:?} (gaming: {}, confidence: {:.2})", 
                   duration, is_gaming, confidence);
        }

        Ok(())
    }

    /// Extract transaction data from WebSocket message
    fn extract_transaction_data(parsed: &serde_json::Value, chain: Chain) -> Result<Option<TransactionData>> {
        match chain {
            Chain::Solana => {
                if let Some(params) = parsed.get("params") {
                    if let Some(result) = params.get("result") {
                        if let Some(transaction) = result.get("transaction") {
                            return Ok(Some(TransactionData {
                                hash: transaction.get("signatures")
                                    .and_then(|s| s.as_array())
                                    .and_then(|arr| arr.first())
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("unknown")
                                    .to_string(),
                                chain,
                                block_number: None,
                                from_address: None,
                                to_address: None,
                                value: "0".to_string(),
                                gas_used: None,
                                timestamp: Utc::now(),
                                raw_data: transaction.clone(),
                            }));
                        }
                    }
                }
            }
            Chain::Ethereum | Chain::Polygon | Chain::Arbitrum => {
                if let Some(params) = parsed.get("params") {
                    if let Some(result) = params.get("result") {
                        if let Some(tx_hash) = result.as_str() {
                            return Ok(Some(TransactionData {
                                hash: tx_hash.to_string(),
                                chain,
                                block_number: None,
                                from_address: None,
                                to_address: None,
                                value: "0".to_string(),
                                gas_used: None,
                                timestamp: Utc::now(),
                                raw_data: result.clone(),
                            }));
                        }
                    }
                }
            }
            _ => {}
        }

        Ok(None)
    }

    /// Apply gaming filter to transaction data
    fn apply_gaming_filter(transaction: &TransactionData, filter: &GamingFilterConfig) -> (bool, f64) {
        if !filter.enabled {
            return (false, 0.0);
        }

        let mut confidence = 0.0;
        let mut matches = 0;

        // Check gaming contracts
        if let Some(to_addr) = &transaction.to_address {
            if filter.gaming_contracts.contains(to_addr) {
                confidence += 0.8;
                matches += 1;
            }
        }

        // Check gaming tokens (simplified)
        let tx_str = transaction.raw_data.to_string().to_lowercase();
        for token in &filter.gaming_tokens {
            if tx_str.contains(&token.to_lowercase()) {
                confidence += 0.6;
                matches += 1;
            }
        }

        // Check keywords
        for keyword in &filter.keywords {
            if tx_str.contains(&keyword.to_lowercase()) {
                confidence += 0.3;
                matches += 1;
            }
        }

        // Normalize confidence
        if matches > 0 {
            confidence = (confidence / matches as f64).min(1.0);
        }

        let is_gaming = confidence >= filter.min_confidence;
        (is_gaming, confidence)
    }

    /// Start polling for chains without WebSocket support
    async fn start_chain_polling(&self, chain: Chain) -> Result<()> {
        let multi_chain_client = Arc::clone(&self.multi_chain_client);
        let event_sender = self.event_sender.clone();
        let poll_interval = Duration::from_millis(self.config.monitoring.poll_interval_ms);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(poll_interval);
            
            loop {
                interval.tick().await;
                
                // Get latest block and transactions
                match multi_chain_client.get_block_number(chain).await {
                    Ok(block_number) => {
                        debug!("📊 Polling chain {:?} at block {}", chain, block_number);
                        // Additional polling logic would go here
                    }
                    Err(e) => {
                        warn!("Failed to poll chain {:?}: {}", chain, e);
                    }
                }
            }
        });

        Ok(())
    }

    /// Start event processor
    async fn start_event_processor(&self) -> Result<()> {
        let mut receiver = self.event_receiver.write().await.take()
            .ok_or_else(|| Web3GamingError::internal("Event receiver already taken"))?;
        
        let logger = Arc::clone(&self.logger);
        let stats = Arc::clone(&self.stats);

        // Create owned counters for the task
        let total_transactions = self.counters.total_transactions.load(Ordering::Relaxed);
        let gaming_transactions = self.counters.gaming_transactions.load(Ordering::Relaxed);
        let total_events = self.counters.total_events.load(Ordering::Relaxed);

        tokio::spawn(async move {
            while let Some(event) = receiver.recv().await {
                if let Err(e) = Self::process_monitor_event(event, &logger, &stats, total_transactions, gaming_transactions, total_events).await {
                    error!("Failed to process monitor event: {}", e);
                }
            }
        });

        Ok(())
    }

    /// Process a monitor event
    async fn process_monitor_event(
        event: MonitorEvent,
        _logger: &Arc<ScrapingLogger>,
        stats: &Arc<RwLock<MonitorStats>>,
        _total_transactions: u64,
        _gaming_transactions: u64,
        _total_events: u64,
    ) -> Result<()> {
        match event {
            MonitorEvent::TransactionDetected { chain, transaction, is_gaming, confidence } => {
                // Note: In a real implementation, we would update counters here
                // For now, we'll just log the event

                if is_gaming {
                    info!("🎮 Gaming transaction detected on {:?}: {} (confidence: {:.2})",
                          chain, transaction.hash, confidence);
                }

                // Update chain stats
                {
                    let mut stats_guard = stats.write().await;
                    let chain_key = format!("{:?}", chain);
                    let chain_stats = stats_guard.chain_stats.entry(chain_key).or_insert(ChainMonitorStats {
                        chain,
                        connected: true,
                        transactions_monitored: 0,
                        gaming_transactions: 0,
                        events_processed: 0,
                        last_transaction_time: None,
                        average_block_time_ms: 0.0,
                        connection_uptime_seconds: 0,
                    });
                    
                    chain_stats.transactions_monitored += 1;
                    if is_gaming {
                        chain_stats.gaming_transactions += 1;
                    }
                    chain_stats.last_transaction_time = Some(transaction.timestamp);
                }
            }
            MonitorEvent::ConnectionEstablished { chain, url } => {
                info!("🔗 WebSocket connected: {:?} -> {}", chain, url);
            }
            MonitorEvent::ConnectionLost { chain, error } => {
                warn!("❌ WebSocket disconnected: {:?} -> {}", chain, error);
            }
            MonitorEvent::HeartbeatReceived { chain, timestamp } => {
                debug!("💓 Heartbeat from {:?} at {}", chain, timestamp);
            }
            MonitorEvent::FilterMatch { chain, transaction_hash, match_type, confidence } => {
                debug!("🎯 Filter match on {:?}: {} ({}, confidence: {:.2})", 
                       chain, transaction_hash, match_type, confidence);
            }
        }

        Ok(())
    }

    /// Start health monitor
    async fn start_health_monitor(&self) {
        let connections = Arc::clone(&self.connections);
        let logger = Arc::clone(&self.logger);
        let heartbeat_interval = Duration::from_millis(self.config.monitoring.heartbeat_interval_ms);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(heartbeat_interval);
            
            loop {
                interval.tick().await;
                
                let conns = connections.read().await;
                for (chain, conn) in conns.iter() {
                    let time_since_heartbeat = Utc::now().signed_duration_since(conn.last_heartbeat);
                    
                    if time_since_heartbeat.num_seconds() > 60 {
                        warn!("⚠️ No heartbeat from {:?} for {} seconds", chain, time_since_heartbeat.num_seconds());
                    }
                }
            }
        });
    }

    /// Start statistics updater
    async fn start_stats_updater(&self) {
        let stats = Arc::clone(&self.stats);

        // Create atomic references that can be moved into the task
        let total_transactions = Arc::clone(&self.counters.total_transactions);
        let gaming_transactions = Arc::clone(&self.counters.gaming_transactions);
        let total_events = Arc::clone(&self.counters.total_events);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));

            loop {
                interval.tick().await;

                let mut stats_guard = stats.write().await;
                stats_guard.total_transactions_monitored = total_transactions.load(Ordering::Relaxed);
                stats_guard.gaming_transactions_detected = gaming_transactions.load(Ordering::Relaxed);
                stats_guard.total_events_processed = total_events.load(Ordering::Relaxed);
                stats_guard.last_updated = Utc::now();
            }
        });
    }

    /// Run forever (main loop)
    async fn run_forever(&self) -> Result<()> {
        info!("🔄 Transaction Monitor running...");
        
        // Keep the monitor running indefinitely
        loop {
            tokio::time::sleep(Duration::from_secs(60)).await;
            
            // Periodic health check
            let stats = self.stats.read().await;
            debug!("📊 Monitor stats: {} transactions, {} gaming, {} events", 
                   stats.total_transactions_monitored,
                   stats.gaming_transactions_detected,
                   stats.total_events_processed);
        }
    }

    /// Get monitor statistics
    pub async fn get_stats(&self) -> MonitorStats {
        self.stats.read().await.clone()
    }
}

impl Default for MonitorStats {
    fn default() -> Self {
        Self {
            total_connections: 0,
            active_connections: 0,
            total_transactions_monitored: 0,
            gaming_transactions_detected: 0,
            total_events_processed: 0,
            average_processing_time_ms: 0.0,
            uptime_seconds: 0,
            last_updated: Utc::now(),
            chain_stats: HashMap::new(),
        }
    }
}
