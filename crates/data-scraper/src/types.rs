//! Data Scraper Types
//!
//! Common types and data structures used throughout the data scraping engine.

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use shared_utils::Chain;
use std::collections::HashMap;
use uuid::Uuid;

/// Transaction data from real-time monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionData {
    pub hash: String,
    pub chain: Chain,
    pub block_number: Option<u64>,
    pub from_address: Option<String>,
    pub to_address: Option<String>,
    pub value: String,
    pub gas_used: Option<u64>,
    pub timestamp: DateTime<Utc>,
    pub raw_data: serde_json::Value,
}

/// Gaming-specific transaction analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GamingTransactionAnalysis {
    pub transaction_hash: String,
    pub chain: Chain,
    pub is_gaming: bool,
    pub confidence_score: f64,
    pub gaming_type: Option<GamingTransactionType>,
    pub involved_contracts: Vec<String>,
    pub involved_tokens: Vec<String>,
    pub player_addresses: Vec<String>,
    pub game_actions: Vec<GameAction>,
    pub economic_impact: Option<EconomicImpact>,
    pub timestamp: DateTime<Utc>,
}

/// Types of gaming transactions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GamingTransactionType {
    PlayerAction,
    ItemTransfer,
    MarketplaceTrade,
    TokenMint,
    TokenBurn,
    StakingReward,
    GameReward,
    NFTMint,
    NFTTransfer,
    LiquidityProvision,
    GameStateUpdate,
    Unknown,
}

/// Game action details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GameAction {
    pub action_type: String,
    pub player: Option<String>,
    pub target: Option<String>,
    pub value: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Economic impact of a gaming transaction
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EconomicImpact {
    pub value_usd: Option<f64>,
    pub token_amounts: HashMap<String, f64>,
    pub fee_paid: Option<f64>,
    pub market_impact: Option<f64>,
}

/// Data source trait for different types of data providers
#[async_trait]
pub trait DataSource: std::fmt::Debug + Send + Sync {
    /// Get the name of this data source
    fn name(&self) -> &str;

    /// Get the type of data this source provides
    fn data_type(&self) -> DataSourceType;

    /// Start the data source
    async fn start(&self) -> anyhow::Result<()>;

    /// Stop the data source
    async fn stop(&self) -> anyhow::Result<()>;

    /// Check if the data source is healthy
    async fn health_check(&self) -> anyhow::Result<SourceHealth>;

    /// Get statistics for this data source
    async fn get_stats(&self) -> anyhow::Result<SourceStats>;

    /// Clone this data source (for trait objects)
    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync>;
}

/// Types of data sources
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataSourceType {
    Blockchain,
    DEX,
    SocialMedia,
    News,
    GameAPI,
    Oracle,
    Custom(String),
}

/// Health status of a data source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceHealth {
    pub is_healthy: bool,
    pub last_successful_fetch: Option<DateTime<Utc>>,
    pub error_rate: f64,
    pub response_time_ms: u64,
    pub issues: Vec<String>,
}

/// Statistics for a data source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_response_time_ms: f64,
    pub data_points_collected: u64,
    pub last_activity: Option<DateTime<Utc>>,
    pub rate_limit_hits: u64,
    pub uptime_percentage: f64,
}

/// Scraped data item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScrapedDataItem {
    pub id: Uuid,
    pub source: String,
    pub data_type: String,
    pub chain: Option<Chain>,
    pub timestamp: DateTime<Utc>,
    pub raw_data: serde_json::Value,
    pub processed_data: Option<serde_json::Value>,
    pub quality_score: Option<f64>,
    pub tags: Vec<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Data validation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub quality_score: f64,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Validation error details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    pub field: String,
    pub error_type: ValidationErrorType,
    pub message: String,
    pub severity: ErrorSeverity,
}

/// Types of validation errors
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationErrorType {
    MissingField,
    InvalidFormat,
    OutOfRange,
    InvalidType,
    ConstraintViolation,
    BusinessRuleViolation,
    DataInconsistency,
}

/// Error severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Rate limiting information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitInfo {
    pub requests_per_second: u32,
    pub burst_capacity: u32,
    pub current_usage: u32,
    pub reset_time: Option<DateTime<Utc>>,
    pub retry_after_seconds: Option<u64>,
}

/// Scraping job configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScrapingJob {
    pub id: Uuid,
    pub name: String,
    pub source: String,
    pub target: String,
    pub schedule: ScrapingSchedule,
    pub filters: Vec<DataFilter>,
    pub transformations: Vec<DataTransformation>,
    pub enabled: bool,
    pub created_at: DateTime<Utc>,
    pub last_run: Option<DateTime<Utc>>,
    pub next_run: Option<DateTime<Utc>>,
}

/// Scraping schedule configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScrapingSchedule {
    Continuous,
    Interval { seconds: u64 },
    Cron { expression: String },
    OnEvent { event_type: String },
    Manual,
}

/// Data filter for scraping jobs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataFilter {
    pub field: String,
    pub operator: FilterOperator,
    pub value: serde_json::Value,
    pub case_sensitive: bool,
}

/// Filter operators
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterOperator {
    Equals,
    NotEquals,
    Contains,
    NotContains,
    StartsWith,
    EndsWith,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    In,
    NotIn,
    Regex,
}

/// Data transformation for scraping jobs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataTransformation {
    pub name: String,
    pub transformation_type: TransformationType,
    pub parameters: HashMap<String, serde_json::Value>,
    pub order: u32,
}

/// Types of data transformations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransformationType {
    Map,
    Filter,
    Aggregate,
    Enrich,
    Normalize,
    Validate,
    Custom(String),
}

/// Backfill job for historical data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackfillJob {
    pub id: Uuid,
    pub name: String,
    pub source: String,
    pub chain: Option<Chain>,
    pub start_block: Option<u64>,
    pub end_block: Option<u64>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub batch_size: u32,
    pub status: BackfillStatus,
    pub progress: BackfillProgress,
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// Backfill job status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackfillStatus {
    Pending,
    Running,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

/// Backfill progress tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackfillProgress {
    pub total_items: u64,
    pub processed_items: u64,
    pub successful_items: u64,
    pub failed_items: u64,
    pub current_position: Option<u64>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub throughput_items_per_second: f64,
}

/// Alert configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertConfig {
    pub id: Uuid,
    pub name: String,
    pub alert_type: AlertType,
    pub condition: AlertCondition,
    pub threshold: f64,
    pub enabled: bool,
    pub channels: Vec<AlertChannel>,
    pub cooldown_seconds: u64,
    pub created_at: DateTime<Utc>,
}

/// Types of alerts
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertType {
    ScrapingFailure,
    DataQualityIssue,
    PerformanceDegradation,
    RateLimitExceeded,
    SourceUnhealthy,
    UnusualPattern,
    SystemError,
}

/// Alert condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertCondition {
    pub metric: String,
    pub operator: FilterOperator,
    pub value: f64,
    pub time_window_seconds: u64,
}

/// Alert delivery channels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertChannel {
    Email { address: String },
    Slack { webhook_url: String },
    Discord { webhook_url: String },
    Webhook { url: String },
    Database,
    Log,
}

/// Gaming protocol information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GamingProtocol {
    pub name: String,
    pub chain: Chain,
    pub contract_addresses: Vec<String>,
    pub token_addresses: Vec<String>,
    pub protocol_type: ProtocolType,
    pub tvl_usd: Option<f64>,
    pub active_users: Option<u64>,
    pub transaction_volume_24h: Option<f64>,
    pub last_updated: DateTime<Utc>,
}

/// Types of gaming protocols
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProtocolType {
    PlayToEarn,
    NFTMarketplace,
    GameFi,
    Metaverse,
    SocialGaming,
    GamingInfrastructure,
    Other(String),
}
