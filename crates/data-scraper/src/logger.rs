//! Comprehensive Scraping Logger
//!
//! This module provides detailed logging for all scraping attempts, successes,
//! failures, data quality metrics, and performance statistics.

use crate::config::LoggingConfig;
use crate::types::*;
use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use shared_utils::{Chain, Web3GamingError};
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Comprehensive scraping logger
#[derive(Debug)]
pub struct ScrapingLogger {
    config: LoggingConfig,
    db_pool: Option<PgPool>,
    stats: Arc<RwLock<LoggerStats>>,
    start_time: Instant,
    buffer: Arc<RwLock<Vec<LogEntry>>>,
    counters: LogCounters,
}

/// Atomic counters for performance
#[derive(Debug)]
struct LogCounters {
    total_attempts: AtomicU64,
    successful_attempts: AtomicU64,
    failed_attempts: AtomicU64,
    data_quality_issues: AtomicU64,
    rate_limit_hits: AtomicU64,
}

/// Log entry for structured logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: Uuid,
    pub timestamp: DateTime<Utc>,
    pub entry_type: LogEntryType,
    pub source: String,
    pub chain: Option<Chain>,
    pub message: String,
    pub metadata: serde_json::Value,
    pub performance_metrics: Option<PerformanceMetrics>,
    pub data_quality: Option<DataQuality>,
}

/// Types of log entries
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogEntryType {
    ScrapingAttempt,
    ScrapingSuccess,
    ScrapingFailure,
    DataQualityIssue,
    RateLimitHit,
    PerformanceAlert,
    SystemEvent,
    EngineEvent,
}

/// Performance metrics for scraping operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub duration_ms: u64,
    pub data_size_bytes: usize,
    pub throughput_items_per_second: f64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub network_latency_ms: u64,
}

/// Data quality metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataQuality {
    pub score: f64, // 0.0 to 1.0
    pub completeness: f64,
    pub accuracy: f64,
    pub consistency: f64,
    pub timeliness: f64,
    pub issues: Vec<String>,
    pub validation_errors: Vec<String>,
}

/// Scraping attempt details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScrapingAttempt {
    pub id: Uuid,
    pub source: String,
    pub chain: Option<Chain>,
    pub target: String, // URL, contract address, etc.
    pub method: String, // GET, POST, WebSocket, RPC, etc.
    pub parameters: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub expected_data_type: String,
}

/// Scraping result (success or failure)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScrapingResult {
    pub attempt_id: Uuid,
    pub success: bool,
    pub timestamp: DateTime<Utc>,
    pub duration_ms: u64,
    pub data_size_bytes: Option<usize>,
    pub items_count: Option<usize>,
    pub error_message: Option<String>,
    pub error_type: Option<String>,
    pub retry_count: u32,
    pub data_quality: Option<DataQuality>,
    pub performance_metrics: Option<PerformanceMetrics>,
}

/// Engine-level events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineEvent {
    pub event_type: EngineEventType,
    pub timestamp: DateTime<Utc>,
    pub message: String,
    pub metadata: serde_json::Value,
}

/// Types of engine events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EngineEventType {
    Startup,
    Shutdown,
    SourceAdded,
    SourceRemoved,
    SourceError,
    ConfigurationChange,
    PerformanceAlert,
    HealthCheck,
}

/// Logger statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggerStats {
    pub total_log_entries: u64,
    pub entries_by_type: HashMap<String, u64>,
    pub entries_by_source: HashMap<String, u64>,
    pub entries_by_chain: HashMap<String, u64>,
    pub average_data_quality_score: f64,
    pub success_rate: f64,
    pub performance_metrics: AggregatedPerformanceMetrics,
    pub last_updated: DateTime<Utc>,
}

/// Aggregated performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedPerformanceMetrics {
    pub average_duration_ms: f64,
    pub average_throughput: f64,
    pub total_data_processed_mb: f64,
    pub peak_memory_usage_mb: f64,
    pub average_cpu_usage: f64,
}

impl ScrapingLogger {
    /// Create a new scraping logger
    pub async fn new(config: &LoggingConfig) -> Result<Self> {
        let db_pool = if config.output.database {
            // Initialize database connection for logging
            // This would connect to the same database as the main app
            None // Placeholder for now
        } else {
            None
        };

        let stats = Arc::new(RwLock::new(LoggerStats::default()));
        let buffer = Arc::new(RwLock::new(Vec::with_capacity(config.buffer_size)));
        
        let counters = LogCounters {
            total_attempts: AtomicU64::new(0),
            successful_attempts: AtomicU64::new(0),
            failed_attempts: AtomicU64::new(0),
            data_quality_issues: AtomicU64::new(0),
            rate_limit_hits: AtomicU64::new(0),
        };

        let logger = Self {
            config: config.clone(),
            db_pool,
            stats,
            start_time: Instant::now(),
            buffer,
            counters,
        };

        // Start background flush task
        logger.start_flush_task().await;

        info!("📝 Scraping Logger initialized with structured logging");
        Ok(logger)
    }

    /// Log a scraping attempt
    pub async fn log_scraping_attempt(&self, attempt: ScrapingAttempt) -> Result<()> {
        self.counters.total_attempts.fetch_add(1, Ordering::Relaxed);

        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            entry_type: LogEntryType::ScrapingAttempt,
            source: attempt.source.clone(),
            chain: attempt.chain,
            message: format!("Scraping attempt: {} -> {}", attempt.source, attempt.target),
            metadata: serde_json::to_value(&attempt)?,
            performance_metrics: None,
            data_quality: None,
        };

        self.add_log_entry(entry).await?;
        debug!("📡 Logged scraping attempt: {} -> {}", attempt.source, attempt.target);
        Ok(())
    }

    /// Log a scraping result
    pub async fn log_scraping_result(&self, result: ScrapingResult) -> Result<()> {
        if result.success {
            self.counters.successful_attempts.fetch_add(1, Ordering::Relaxed);
        } else {
            self.counters.failed_attempts.fetch_add(1, Ordering::Relaxed);
        }

        if let Some(quality) = &result.data_quality {
            if quality.score < 0.7 {
                self.counters.data_quality_issues.fetch_add(1, Ordering::Relaxed);
            }
        }

        let entry_type = if result.success {
            LogEntryType::ScrapingSuccess
        } else {
            LogEntryType::ScrapingFailure
        };

        let message = if result.success {
            format!("Scraping success: {} items in {}ms", 
                   result.items_count.unwrap_or(0), result.duration_ms)
        } else {
            format!("Scraping failed: {}", 
                   result.error_message.as_deref().unwrap_or("Unknown error"))
        };

        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            entry_type,
            source: "scraping_result".to_string(),
            chain: None,
            message,
            metadata: serde_json::to_value(&result)?,
            performance_metrics: result.performance_metrics.clone(),
            data_quality: result.data_quality.clone(),
        };

        self.add_log_entry(entry).await?;

        if result.success {
            debug!("✅ Logged successful scraping result: {} items", result.items_count.unwrap_or(0));
        } else {
            warn!("❌ Logged failed scraping result: {}", result.error_message.as_deref().unwrap_or("Unknown"));
        }

        Ok(())
    }

    /// Log a rate limit hit
    pub async fn log_rate_limit_hit(&self, source: &str, chain: Option<Chain>, details: serde_json::Value) -> Result<()> {
        self.counters.rate_limit_hits.fetch_add(1, Ordering::Relaxed);

        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            entry_type: LogEntryType::RateLimitHit,
            source: source.to_string(),
            chain,
            message: format!("Rate limit hit for source: {}", source),
            metadata: details,
            performance_metrics: None,
            data_quality: None,
        };

        self.add_log_entry(entry).await?;
        warn!("⚠️ Rate limit hit: {}", source);
        Ok(())
    }

    /// Log a data quality issue
    pub async fn log_data_quality_issue(&self, source: &str, chain: Option<Chain>, quality: DataQuality) -> Result<()> {
        self.counters.data_quality_issues.fetch_add(1, Ordering::Relaxed);

        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            entry_type: LogEntryType::DataQualityIssue,
            source: source.to_string(),
            chain,
            message: format!("Data quality issue: score {:.2}", quality.score),
            metadata: serde_json::json!({
                "quality_score": quality.score,
                "issues": quality.issues,
                "validation_errors": quality.validation_errors
            }),
            performance_metrics: None,
            data_quality: Some(quality),
        };

        let quality_score = entry.data_quality.as_ref().unwrap().score;
        self.add_log_entry(entry).await?;
        warn!("⚠️ Data quality issue: {} (score: {:.2})", source, quality_score);
        Ok(())
    }

    /// Log an engine event
    pub async fn log_engine_event(&self, event: EngineEvent) -> Result<()> {
        let entry = LogEntry {
            id: Uuid::new_v4(),
            timestamp: event.timestamp,
            entry_type: LogEntryType::EngineEvent,
            source: "engine".to_string(),
            chain: None,
            message: event.message.clone(),
            metadata: event.metadata,
            performance_metrics: None,
            data_quality: None,
        };

        self.add_log_entry(entry).await?;
        info!("🔧 Engine event: {}", event.message);
        Ok(())
    }

    /// Add a log entry to the buffer
    async fn add_log_entry(&self, entry: LogEntry) -> Result<()> {
        // Add to buffer
        self.buffer.write().await.push(entry.clone());

        // Update statistics
        self.update_stats(&entry).await;

        // Console output if enabled
        if self.config.output.console {
            self.output_to_console(&entry);
        }

        // Check if buffer needs flushing
        if self.buffer.read().await.len() >= self.config.buffer_size {
            self.flush_buffer().await?;
        }

        Ok(())
    }

    /// Output log entry to console
    fn output_to_console(&self, entry: &LogEntry) {
        match self.config.format {
            crate::config::LogFormat::Json => {
                if let Ok(json) = serde_json::to_string(entry) {
                    println!("{}", json);
                }
            }
            crate::config::LogFormat::Pretty => {
                println!("[{}] {} - {} ({})", 
                        entry.timestamp.format("%Y-%m-%d %H:%M:%S"),
                        entry.source,
                        entry.message,
                        format!("{:?}", entry.entry_type));
            }
            crate::config::LogFormat::Compact => {
                println!("{} {} {}", 
                        entry.timestamp.format("%H:%M:%S"),
                        entry.source,
                        entry.message);
            }
        }
    }

    /// Update internal statistics
    async fn update_stats(&self, entry: &LogEntry) {
        let mut stats = self.stats.write().await;
        stats.total_log_entries += 1;
        
        let entry_type_key = format!("{:?}", entry.entry_type);
        *stats.entries_by_type.entry(entry_type_key).or_insert(0) += 1;
        *stats.entries_by_source.entry(entry.source.clone()).or_insert(0) += 1;
        
        if let Some(chain) = entry.chain {
            let chain_key = format!("{:?}", chain);
            *stats.entries_by_chain.entry(chain_key).or_insert(0) += 1;
        }

        stats.last_updated = Utc::now();
    }

    /// Start background flush task
    async fn start_flush_task(&self) {
        let buffer = Arc::clone(&self.buffer);
        let flush_interval = self.config.flush_interval_ms;
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_millis(flush_interval));
            
            loop {
                interval.tick().await;
                
                if !buffer.read().await.is_empty() {
                    // Flush logic would go here
                    debug!("🔄 Flushing log buffer ({} entries)", buffer.read().await.len());
                    buffer.write().await.clear();
                }
            }
        });
    }

    /// Flush the log buffer
    async fn flush_buffer(&self) -> Result<()> {
        let entries = {
            let mut buffer = self.buffer.write().await;
            let entries = buffer.clone();
            buffer.clear();
            entries
        };

        if entries.is_empty() {
            return Ok(());
        }

        // Database flush
        if self.config.output.database && self.db_pool.is_some() {
            // Database insertion logic would go here
            debug!("💾 Flushing {} entries to database", entries.len());
        }

        // File flush
        if let Some(file_path) = &self.config.output.file {
            // File writing logic would go here
            debug!("📁 Flushing {} entries to file: {}", entries.len(), file_path);
        }

        Ok(())
    }

    /// Get logger statistics
    pub async fn get_stats(&self) -> Result<LoggerStats> {
        let mut stats = self.stats.read().await.clone();
        
        // Calculate success rate
        let total = self.counters.total_attempts.load(Ordering::Relaxed);
        let successful = self.counters.successful_attempts.load(Ordering::Relaxed);
        stats.success_rate = if total > 0 {
            successful as f64 / total as f64
        } else {
            0.0
        };

        Ok(stats)
    }

    /// Get uptime in seconds
    pub fn get_uptime_seconds(&self) -> u64 {
        self.start_time.elapsed().as_secs()
    }
}

impl Default for LoggerStats {
    fn default() -> Self {
        Self {
            total_log_entries: 0,
            entries_by_type: HashMap::new(),
            entries_by_source: HashMap::new(),
            entries_by_chain: HashMap::new(),
            average_data_quality_score: 0.0,
            success_rate: 0.0,
            performance_metrics: AggregatedPerformanceMetrics::default(),
            last_updated: Utc::now(),
        }
    }
}

impl Default for AggregatedPerformanceMetrics {
    fn default() -> Self {
        Self {
            average_duration_ms: 0.0,
            average_throughput: 0.0,
            total_data_processed_mb: 0.0,
            peak_memory_usage_mb: 0.0,
            average_cpu_usage: 0.0,
        }
    }
}
