//! News Data Source
//!
//! News feed monitoring for gaming-related articles and announcements.

use crate::types::*;
use crate::sources::BaseDataSource;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// News data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewsSourceConfig {
    pub sources: Vec<String>,
    pub categories: Vec<String>,
    pub poll_interval_seconds: u64,
    pub sentiment_analysis: bool,
    pub real_time_feeds: bool,
}

/// News data source implementation
#[derive(Debug)]
pub struct NewsDataSource {
    base: Arc<BaseDataSource>,
    config: NewsSourceConfig,
    running: Arc<RwLock<bool>>,
}

impl NewsDataSource {
    /// Create a new news data source
    pub fn new(config_value: serde_json::Value) -> anyhow::Result<Self> {
        let config: NewsSourceConfig = serde_json::from_value(config_value)?;
        
        Ok(Self {
            base: Arc::new(BaseDataSource::new(
                "news".to_string(),
                DataSourceType::News,
            )),
            config,
            running: Arc::new(RwLock::new(false)),
        })
    }
}

#[async_trait]
impl DataSource for NewsDataSource {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn data_type(&self) -> DataSourceType {
        self.base.data_type.clone()
    }

    async fn start(&self) -> anyhow::Result<()> {
        info!("🚀 Starting news data source");
        *self.running.write().await = true;
        
        // Start monitoring task
        let base = Arc::clone(&self.base);
        let config = self.config.clone();
        let running = Arc::clone(&self.running);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(config.poll_interval_seconds)
            );

            while *running.read().await {
                interval.tick().await;
                
                // Simulate news data collection
                base.record_success(250, 2).await;
                debug!("📊 News data collected");
            }
        });
        
        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        info!("🛑 Stopping news data source");
        *self.running.write().await = false;
        Ok(())
    }

    async fn health_check(&self) -> anyhow::Result<SourceHealth> {
        Ok(self.base.health.read().await.clone())
    }

    async fn get_stats(&self) -> anyhow::Result<SourceStats> {
        Ok(self.base.stats.read().await.clone())
    }

    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync> {
        Box::new(Self {
            base: Arc::clone(&self.base),
            config: self.config.clone(),
            running: Arc::clone(&self.running),
        })
    }
}

impl Default for NewsSourceConfig {
    fn default() -> Self {
        Self {
            sources: vec!["coindesk".to_string(), "cointelegraph".to_string()],
            categories: vec!["gaming".to_string(), "nft".to_string()],
            poll_interval_seconds: 60,
            sentiment_analysis: false,
            real_time_feeds: false,
        }
    }
}
