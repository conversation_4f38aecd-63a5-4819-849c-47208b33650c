//! Blockchain Data Source
//!
//! Real-time blockchain data collection with comprehensive logging.

use crate::types::*;
use crate::sources::BaseDataSource;
use async_trait::async_trait;
use blockchain_engine::multi_chain::MultiChainClient;
use serde::{Deserialize, Serialize};
use shared_utils::Chain;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// Blockchain data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockchainSourceConfig {
    pub chains: Vec<Chain>,
    pub poll_interval_seconds: u64,
    pub batch_size: u32,
    pub gaming_filter_enabled: bool,
    pub track_transactions: bool,
    pub track_events: bool,
    pub track_contracts: bool,
}

/// Blockchain data source implementation
#[derive(Debug)]
pub struct BlockchainDataSource {
    base: Arc<BaseDataSource>,
    config: BlockchainSourceConfig,
    multi_chain_client: Arc<MultiChainClient>,
    running: Arc<RwLock<bool>>,
}

impl BlockchainDataSource {
    /// Create a new blockchain data source
    pub fn new(config_value: serde_json::Value) -> anyhow::Result<Self> {
        let config: BlockchainSourceConfig = serde_json::from_value(config_value)?;
        
        Ok(Self {
            base: Arc::new(BaseDataSource::new(
                "blockchain".to_string(),
                DataSourceType::Blockchain,
            )),
            config,
            multi_chain_client: Arc::new(MultiChainClient::new()),
            running: Arc::new(RwLock::new(false)),
        })
    }

    /// Start blockchain monitoring for all configured chains
    async fn start_monitoring(&self) -> anyhow::Result<()> {
        for chain in &self.config.chains {
            self.start_chain_monitoring(*chain).await?;
        }
        Ok(())
    }

    /// Start monitoring for a specific chain
    async fn start_chain_monitoring(&self, chain: Chain) -> anyhow::Result<()> {
        let base = Arc::clone(&self.base);
        let client = Arc::clone(&self.multi_chain_client);
        let config = self.config.clone();
        let running = Arc::clone(&self.running);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(config.poll_interval_seconds)
            );

            info!("🔗 Starting blockchain monitoring for chain: {:?}", chain);

            while *running.read().await {
                interval.tick().await;

                let start_time = Instant::now();
                
                match Self::collect_chain_data(&client, chain, &config).await {
                    Ok(data_points) => {
                        let duration = start_time.elapsed().as_millis() as u64;
                        base.record_success(duration, data_points).await;
                        
                        debug!("📊 Collected {} data points from {:?} in {}ms", 
                               data_points, chain, duration);
                    }
                    Err(e) => {
                        let error_msg = format!("Failed to collect data from {:?}: {}", chain, e);
                        base.record_failure(&error_msg).await;
                        warn!("{}", error_msg);
                    }
                }
            }

            info!("🛑 Stopped blockchain monitoring for chain: {:?}", chain);
        });

        Ok(())
    }

    /// Collect data from a specific chain
    async fn collect_chain_data(
        client: &MultiChainClient,
        chain: Chain,
        config: &BlockchainSourceConfig,
    ) -> anyhow::Result<u64> {
        let mut data_points = 0;

        // Get latest block number
        let block_number = client.get_block_number(chain).await?;
        data_points += 1;

        if config.track_transactions {
            // Get recent transactions
            match client.get_block(chain, block_number).await {
                Ok(block_info) => {
                    data_points += block_info.transaction_count as u64;
                    debug!("📦 Block {} on {:?} has {} transactions", 
                           block_number, chain, block_info.transaction_count);
                }
                Err(e) => {
                    warn!("Failed to get block {} on {:?}: {}", block_number, chain, e);
                }
            }
        }

        if config.track_events {
            // Event tracking would be implemented here
            debug!("📡 Event tracking for {:?} (placeholder)", chain);
            data_points += 1;
        }

        if config.track_contracts {
            // Contract tracking would be implemented here
            debug!("📋 Contract tracking for {:?} (placeholder)", chain);
            data_points += 1;
        }

        Ok(data_points)
    }
}

#[async_trait]
impl DataSource for BlockchainDataSource {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn data_type(&self) -> DataSourceType {
        self.base.data_type.clone()
    }

    async fn start(&self) -> anyhow::Result<()> {
        info!("🚀 Starting blockchain data source");
        
        *self.running.write().await = true;
        self.start_monitoring().await?;
        
        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        info!("🛑 Stopping blockchain data source");
        *self.running.write().await = false;
        Ok(())
    }

    async fn health_check(&self) -> anyhow::Result<SourceHealth> {
        Ok(self.base.health.read().await.clone())
    }

    async fn get_stats(&self) -> anyhow::Result<SourceStats> {
        Ok(self.base.stats.read().await.clone())
    }

    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync> {
        // For now, create a new instance with the same config
        Box::new(Self {
            base: Arc::clone(&self.base),
            config: self.config.clone(),
            multi_chain_client: Arc::clone(&self.multi_chain_client),
            running: Arc::clone(&self.running),
        })
    }
}

impl Default for BlockchainSourceConfig {
    fn default() -> Self {
        Self {
            chains: vec![Chain::Solana, Chain::Ethereum],
            poll_interval_seconds: 5,
            batch_size: 100,
            gaming_filter_enabled: true,
            track_transactions: true,
            track_events: true,
            track_contracts: true,
        }
    }
}
