//! DEX Data Source
//!
//! Decentralized exchange data collection for gaming tokens and liquidity.

use crate::types::*;
use crate::sources::BaseDataSource;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// DEX data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DexSourceConfig {
    pub exchanges: Vec<String>,
    pub gaming_tokens: Vec<String>,
    pub poll_interval_seconds: u64,
    pub track_prices: bool,
    pub track_volume: bool,
    pub track_liquidity: bool,
}

/// DEX data source implementation
#[derive(Debug)]
pub struct DexDataSource {
    base: Arc<BaseDataSource>,
    config: DexSourceConfig,
    running: Arc<RwLock<bool>>,
}

impl DexDataSource {
    /// Create a new DEX data source
    pub fn new(config_value: serde_json::Value) -> anyhow::Result<Self> {
        let config: DexSourceConfig = serde_json::from_value(config_value)?;
        
        Ok(Self {
            base: Arc::new(BaseDataSource::new(
                "dex".to_string(),
                DataSourceType::DEX,
            )),
            config,
            running: Arc::new(RwLock::new(false)),
        })
    }
}

#[async_trait]
impl DataSource for DexDataSource {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn data_type(&self) -> DataSourceType {
        self.base.data_type.clone()
    }

    async fn start(&self) -> anyhow::Result<()> {
        info!("🚀 Starting DEX data source");
        *self.running.write().await = true;
        
        // Start monitoring task
        let base = Arc::clone(&self.base);
        let config = self.config.clone();
        let running = Arc::clone(&self.running);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(config.poll_interval_seconds)
            );

            while *running.read().await {
                interval.tick().await;
                
                // Simulate DEX data collection
                base.record_success(150, 5).await;
                debug!("📊 DEX data collected");
            }
        });
        
        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        info!("🛑 Stopping DEX data source");
        *self.running.write().await = false;
        Ok(())
    }

    async fn health_check(&self) -> anyhow::Result<SourceHealth> {
        Ok(self.base.health.read().await.clone())
    }

    async fn get_stats(&self) -> anyhow::Result<SourceStats> {
        Ok(self.base.stats.read().await.clone())
    }

    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync> {
        Box::new(Self {
            base: Arc::clone(&self.base),
            config: self.config.clone(),
            running: Arc::clone(&self.running),
        })
    }
}

impl Default for DexSourceConfig {
    fn default() -> Self {
        Self {
            exchanges: vec!["uniswap".to_string(), "sushiswap".to_string()],
            gaming_tokens: vec!["ATLAS".to_string(), "AURY".to_string()],
            poll_interval_seconds: 10,
            track_prices: true,
            track_volume: true,
            track_liquidity: false,
        }
    }
}
