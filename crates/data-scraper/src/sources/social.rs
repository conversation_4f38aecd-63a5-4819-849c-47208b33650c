//! Social Media Data Source
//!
//! Social media monitoring for gaming-related content and sentiment.

use crate::types::*;
use crate::sources::BaseDataSource;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// Social media data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SocialSourceConfig {
    pub platforms: Vec<String>,
    pub keywords: Vec<String>,
    pub poll_interval_seconds: u64,
    pub sentiment_analysis: bool,
    pub influencer_tracking: bool,
}

/// Social media data source implementation
#[derive(Debug)]
pub struct SocialDataSource {
    base: Arc<BaseDataSource>,
    config: SocialSourceConfig,
    running: Arc<RwLock<bool>>,
}

impl SocialDataSource {
    /// Create a new social media data source
    pub fn new(config_value: serde_json::Value) -> anyhow::Result<Self> {
        let config: SocialSourceConfig = serde_json::from_value(config_value)?;
        
        Ok(Self {
            base: Arc::new(BaseDataSource::new(
                "social".to_string(),
                DataSourceType::SocialMedia,
            )),
            config,
            running: Arc::new(RwLock::new(false)),
        })
    }
}

#[async_trait]
impl DataSource for SocialDataSource {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn data_type(&self) -> DataSourceType {
        self.base.data_type.clone()
    }

    async fn start(&self) -> anyhow::Result<()> {
        info!("🚀 Starting social media data source");
        *self.running.write().await = true;
        
        // Start monitoring task
        let base = Arc::clone(&self.base);
        let config = self.config.clone();
        let running = Arc::clone(&self.running);
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(
                std::time::Duration::from_secs(config.poll_interval_seconds)
            );

            while *running.read().await {
                interval.tick().await;
                
                // Simulate social media data collection
                base.record_success(300, 3).await;
                debug!("📊 Social media data collected");
            }
        });
        
        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        info!("🛑 Stopping social media data source");
        *self.running.write().await = false;
        Ok(())
    }

    async fn health_check(&self) -> anyhow::Result<SourceHealth> {
        Ok(self.base.health.read().await.clone())
    }

    async fn get_stats(&self) -> anyhow::Result<SourceStats> {
        Ok(self.base.stats.read().await.clone())
    }

    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync> {
        Box::new(Self {
            base: Arc::clone(&self.base),
            config: self.config.clone(),
            running: Arc::clone(&self.running),
        })
    }
}

impl Default for SocialSourceConfig {
    fn default() -> Self {
        Self {
            platforms: vec!["twitter".to_string(), "discord".to_string()],
            keywords: vec!["web3gaming".to_string(), "nftgaming".to_string()],
            poll_interval_seconds: 30,
            sentiment_analysis: false,
            influencer_tracking: false,
        }
    }
}
