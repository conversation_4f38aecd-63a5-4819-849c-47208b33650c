//! Data Scraping Engine
//!
//! COMPONENT_ESSENCE::HERMES_DATA_HARVESTER_WISDOM
//! DANGER_LEVEL::APOLLO_REAL_TIME_MONITORING_GUARDIAN
//! PERFORMANCE_TARGET::SUB_50MS_TRANSACTION_DETECTION
//! LAST_MODIFIED::PHASE_2_SCRAPING_ENGINE_TRIUMPH
//! SOLUTION_TYPE::REAL_TIME_MULTI_CHAIN_DATA_HARVESTER
//!
//! This crate provides comprehensive data scraping capabilities for Web3 gaming
//! data across multiple blockchains with real-time monitoring and detailed logging.

pub mod aggregator;
pub mod backfill;
pub mod config;
pub mod logger;
pub mod monitor;
pub mod protocols;
pub mod sources;
pub mod types;
pub mod validator;

pub use aggregator::*;
pub use backfill::*;
pub use config::*;
pub use logger::*;
pub use monitor::*;
pub use protocols::*;
pub use sources::*;
pub use types::*;
pub use validator::*;

use anyhow::Result;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, error, warn};

/// Main data scraping engine
#[derive(Debug)]
pub struct DataScrapingEngine {
    config: ScrapingConfig,
    logger: Arc<ScrapingLogger>,
    monitor: Arc<RwLock<TransactionMonitor>>,
    sources: Arc<RwLock<Vec<Box<dyn DataSource + Send + Sync>>>>,
    aggregator: Arc<MultiSourceAggregator>,
    validator: Arc<DataValidationEngine>,
    protocol_manager: Arc<ProtocolScraperManager>,
    backfill_engine: Arc<BackfillEngine>,
}

impl DataScrapingEngine {
    /// Create a new data scraping engine
    pub async fn new(config: ScrapingConfig) -> Result<Self> {
        let logger = Arc::new(ScrapingLogger::new(&config.logging).await?);
        let monitor = Arc::new(RwLock::new(TransactionMonitor::new(config.clone(), Arc::clone(&logger)).await?));
        let sources = Arc::new(RwLock::new(Vec::new()));
        let aggregator = Arc::new(MultiSourceAggregator::new(Arc::clone(&logger)).await?);
        let validator = Arc::new(DataValidationEngine::new(Arc::clone(&logger)).await?);
        let protocol_manager = Arc::new(ProtocolScraperManager::new(Arc::clone(&logger)).await?);

        // Create backfill configuration
        let backfill_config = BackfillConfig {
            max_concurrent_jobs: 3,
            batch_size: 100,
            retry_attempts: 3,
            retry_delay_seconds: 5,
            checkpoint_interval: 1000,
            rate_limit_per_second: 10,
            data_integrity_checks: true,
            resume_on_startup: true,
            storage_path: "data/backfill".to_string(),
        };
        let backfill_engine = Arc::new(BackfillEngine::new(Arc::clone(&logger), backfill_config).await?);

        info!("🚀 Data Scraping Engine initialized with full protocol support and backfill capabilities");

        Ok(Self {
            config,
            logger,
            monitor,
            sources,
            aggregator,
            validator,
            protocol_manager,
            backfill_engine,
        })
    }

    /// Start the data scraping engine
    pub async fn start(&self) -> Result<()> {
        info!("🔄 Starting Data Scraping Engine...");

        // Start transaction monitor
        let monitor = Arc::clone(&self.monitor);
        let monitor_handle = tokio::spawn(async move {
            if let Err(e) = monitor.read().await.start().await {
                error!("Transaction monitor failed: {}", e);
            }
        });

        // Start data sources
        self.start_data_sources().await?;

        // Log startup success
        self.logger.log_engine_event(EngineEvent {
            event_type: EngineEventType::Startup,
            timestamp: chrono::Utc::now(),
            message: "Data Scraping Engine started successfully".to_string(),
            metadata: serde_json::json!({
                "sources_count": self.sources.read().await.len(),
                "config": self.config
            }),
        }).await?;

        info!("✅ Data Scraping Engine started successfully");
        
        // Wait for monitor to complete (it runs indefinitely)
        monitor_handle.await?;
        
        Ok(())
    }

    /// Add a data source to the engine
    pub async fn add_source(&self, source: Box<dyn DataSource + Send + Sync>) -> Result<()> {
        let source_name = source.name().to_string();
        self.sources.write().await.push(source);
        
        self.logger.log_engine_event(EngineEvent {
            event_type: EngineEventType::SourceAdded,
            timestamp: chrono::Utc::now(),
            message: format!("Added data source: {}", source_name),
            metadata: serde_json::json!({
                "source_name": source_name,
                "total_sources": self.sources.read().await.len()
            }),
        }).await?;

        info!("📡 Added data source: {}", source_name);
        Ok(())
    }

    /// Start all configured data sources
    async fn start_data_sources(&self) -> Result<()> {
        let sources = self.sources.read().await;
        
        for source in sources.iter() {
            let source_name = source.name().to_string();
            info!("🔌 Starting data source: {}", source_name);
            
            // Start each source in its own task
            let source_clone = source.clone_box();
            let logger = Arc::clone(&self.logger);
            
            tokio::spawn(async move {
                if let Err(e) = source_clone.start().await {
                    error!("Data source {} failed: {}", source_name, e);
                    
                    // Log the failure
                    if let Err(log_err) = logger.log_engine_event(EngineEvent {
                        event_type: EngineEventType::SourceError,
                        timestamp: chrono::Utc::now(),
                        message: format!("Data source {} failed: {}", source_name, e),
                        metadata: serde_json::json!({
                            "source_name": source_name,
                            "error": e.to_string()
                        }),
                    }).await {
                        error!("Failed to log source error: {}", log_err);
                    }
                }
            });
        }

        Ok(())
    }

    /// Get scraping statistics
    pub async fn get_stats(&self) -> Result<ScrapingStats> {
        let monitor_stats = self.monitor.read().await.get_stats().await;
        let logger_stats = self.logger.get_stats().await?;
        let aggregator_stats = self.aggregator.get_stats().await;
        let validator_stats = self.validator.get_stats().await;
        let protocol_stats = self.protocol_manager.get_stats().await;
        let backfill_stats = self.backfill_engine.get_stats().await;

        Ok(ScrapingStats {
            monitor_stats,
            logger_stats,
            aggregator_stats,
            validator_stats,
            protocol_stats,
            backfill_stats,
            sources_count: self.sources.read().await.len(),
            uptime_seconds: self.logger.get_uptime_seconds(),
        })
    }

    /// Get the logger for external use
    pub fn logger(&self) -> Arc<ScrapingLogger> {
        Arc::clone(&self.logger)
    }

    /// Get the monitor for external use
    pub fn monitor(&self) -> Arc<RwLock<TransactionMonitor>> {
        Arc::clone(&self.monitor)
    }

    /// Get the aggregator for external use
    pub fn aggregator(&self) -> Arc<MultiSourceAggregator> {
        Arc::clone(&self.aggregator)
    }

    /// Get the validator for external use
    pub fn validator(&self) -> Arc<DataValidationEngine> {
        Arc::clone(&self.validator)
    }

    /// Get the protocol manager for external use
    pub fn protocol_manager(&self) -> Arc<ProtocolScraperManager> {
        Arc::clone(&self.protocol_manager)
    }

    /// Get the backfill engine for external use
    pub fn backfill_engine(&self) -> Arc<BackfillEngine> {
        Arc::clone(&self.backfill_engine)
    }

    /// Validate and aggregate data from multiple sources
    pub async fn process_data(
        &self,
        data_type: &str,
        source_data: Vec<(String, serde_json::Value)>,
    ) -> Result<AggregatedDataItem> {
        // First validate each source's data
        let mut validated_data = Vec::new();

        for (source_id, data) in source_data {
            match self.validator.validate_data(data_type, &data, &source_id).await {
                Ok(validation_result) => {
                    if validation_result.is_valid {
                        let quality = DataQuality {
                            score: validation_result.quality_score,
                            completeness: 1.0,
                            accuracy: validation_result.quality_score,
                            consistency: 1.0,
                            timeliness: 1.0,
                            issues: validation_result.errors.iter().map(|e| e.message.clone()).collect(),
                            validation_errors: validation_result.warnings,
                        };
                        validated_data.push((source_id, data, quality));
                    } else {
                        warn!("⚠️ Data validation failed for source {}: {:?}", source_id, validation_result.errors);
                    }
                }
                Err(e) => {
                    error!("❌ Validation error for source {}: {}", source_id, e);
                }
            }
        }

        // Then aggregate the validated data
        if validated_data.is_empty() {
            return Err(anyhow::anyhow!("No valid data to aggregate"));
        }

        self.aggregator.aggregate_data(data_type, validated_data).await
    }

    /// Scrape protocol-specific data
    pub async fn scrape_protocol_data(&self, protocol: &str, data_type: &str) -> Result<Vec<ProtocolData>> {
        self.protocol_manager.scrape_all_protocols(data_type).await
    }

    /// Create a historical data backfill job
    pub async fn create_backfill_job(
        &self,
        name: String,
        chain: shared_utils::Chain,
        protocol: Option<String>,
        data_type: String,
        start_block: u64,
        end_block: u64,
        priority: BackfillPriority,
    ) -> Result<String> {
        self.backfill_engine.create_job(name, chain, protocol, data_type, start_block, end_block, priority).await
    }

    /// Start a backfill job
    pub async fn start_backfill_job(&self, job_id: &str) -> Result<()> {
        self.backfill_engine.start_job(job_id).await
    }
}

/// Overall scraping statistics
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ScrapingStats {
    pub monitor_stats: MonitorStats,
    pub logger_stats: LoggerStats,
    pub aggregator_stats: AggregatorStats,
    pub validator_stats: ValidationStats,
    pub protocol_stats: ProtocolScrapingStats,
    pub backfill_stats: BackfillStats,
    pub sources_count: usize,
    pub uptime_seconds: u64,
}
