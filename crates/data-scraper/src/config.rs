//! Configuration for Data Scraping Engine

use serde::{Deserialize, Serialize};
use shared_utils::Chain;
use std::collections::HashMap;
use std::time::Duration;

/// Main configuration for the data scraping engine
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScrapingConfig {
    pub logging: LoggingConfig,
    pub monitoring: MonitoringConfig,
    pub rate_limiting: RateLimitingConfig,
    pub chains: HashMap<Chain, ChainConfig>,
    pub sources: SourcesConfig,
    pub database: DatabaseConfig,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub format: LogFormat,
    pub output: LogOutput,
    pub structured: bool,
    pub include_metadata: bool,
    pub buffer_size: usize,
    pub flush_interval_ms: u64,
    pub retention_days: u32,
}

/// Log format options
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum LogFormat {
    <PERSON><PERSON>,
    <PERSON>,
    Compact,
}

/// Log output configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LogOutput {
    pub console: bool,
    pub file: Option<String>,
    pub database: bool,
    pub metrics: bool,
}

/// Real-time monitoring configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub enabled: bool,
    pub websocket_url: Option<String>,
    pub poll_interval_ms: u64,
    pub batch_size: usize,
    pub max_connections: usize,
    pub reconnect_delay_ms: u64,
    pub heartbeat_interval_ms: u64,
    pub buffer_size: usize,
    pub gaming_filter: GamingFilterConfig,
}

/// Gaming-specific filtering configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GamingFilterConfig {
    pub enabled: bool,
    pub min_confidence: f64,
    pub gaming_contracts: Vec<String>,
    pub gaming_tokens: Vec<String>,
    pub keywords: Vec<String>,
    pub exclude_patterns: Vec<String>,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitingConfig {
    pub enabled: bool,
    pub default_requests_per_second: u32,
    pub burst_capacity: u32,
    pub adaptive: bool,
    pub backoff_multiplier: f64,
    pub max_backoff_seconds: u64,
    pub per_source_limits: HashMap<String, u32>,
}

/// Chain-specific configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainConfig {
    pub enabled: bool,
    pub rpc_url: String,
    pub websocket_url: Option<String>,
    pub requests_per_second: u32,
    pub block_confirmations: u32,
    pub start_block: Option<u64>,
    pub gaming_contracts: Vec<String>,
    pub priority: u8, // 1-10, higher = more priority
}

/// Data sources configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourcesConfig {
    pub blockchain: BlockchainSourceConfig,
    pub dex: DexSourceConfig,
    pub social: SocialSourceConfig,
    pub news: NewsSourceConfig,
}

/// Blockchain data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockchainSourceConfig {
    pub enabled: bool,
    pub real_time: bool,
    pub historical_backfill: bool,
    pub transaction_monitoring: bool,
    pub event_monitoring: bool,
    pub contract_monitoring: bool,
}

/// DEX data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DexSourceConfig {
    pub enabled: bool,
    pub exchanges: Vec<String>,
    pub gaming_pairs_only: bool,
    pub price_feeds: bool,
    pub volume_tracking: bool,
    pub liquidity_monitoring: bool,
}

/// Social media data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SocialSourceConfig {
    pub enabled: bool,
    pub platforms: Vec<String>,
    pub gaming_keywords: Vec<String>,
    pub sentiment_analysis: bool,
    pub influencer_tracking: bool,
}

/// News data source configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewsSourceConfig {
    pub enabled: bool,
    pub sources: Vec<String>,
    pub gaming_categories: Vec<String>,
    pub real_time_feeds: bool,
    pub sentiment_analysis: bool,
}

/// Database configuration for scraping data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub connection_timeout_seconds: u64,
    pub query_timeout_seconds: u64,
    pub batch_insert_size: usize,
    pub retry_attempts: u32,
}

impl Default for ScrapingConfig {
    fn default() -> Self {
        Self {
            logging: LoggingConfig::default(),
            monitoring: MonitoringConfig::default(),
            rate_limiting: RateLimitingConfig::default(),
            chains: HashMap::new(),
            sources: SourcesConfig::default(),
            database: DatabaseConfig::default(),
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: LogFormat::Json,
            output: LogOutput::default(),
            structured: true,
            include_metadata: true,
            buffer_size: 1000,
            flush_interval_ms: 1000,
            retention_days: 30,
        }
    }
}

impl Default for LogOutput {
    fn default() -> Self {
        Self {
            console: true,
            file: Some("logs/scraping.log".to_string()),
            database: true,
            metrics: true,
        }
    }
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            websocket_url: None,
            poll_interval_ms: 1000,
            batch_size: 100,
            max_connections: 10,
            reconnect_delay_ms: 5000,
            heartbeat_interval_ms: 30000,
            buffer_size: 10000,
            gaming_filter: GamingFilterConfig::default(),
        }
    }
}

impl Default for GamingFilterConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            min_confidence: 0.3,
            gaming_contracts: vec![
                "BAP315i1xoAXqbJcTT1LrUS45N3tAQnNnPuNQkCcvbAr".to_string(), // Star Atlas
                "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM".to_string(), // Aurory
                "GDfnEsia2WLAW5t8yx2X5j2mkfA74i5kwGdDuZHt7XmG".to_string(), // Genopets
            ],
            gaming_tokens: vec![
                "ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx".to_string(), // ATLAS
                "poLisWXnNRwC6oBu1vHiuKQzFjGL4XDSu4g9qjz9qVk".to_string(),  // POLIS
                "AURYydfxJib1ZkTir1Jn1J9ECYUtjb6rKQVmtYaixWPP".to_string(), // AURY
            ],
            keywords: vec![
                "gaming".to_string(),
                "nft".to_string(),
                "play-to-earn".to_string(),
                "p2e".to_string(),
                "metaverse".to_string(),
            ],
            exclude_patterns: vec![
                "test".to_string(),
                "spam".to_string(),
                "bot".to_string(),
            ],
        }
    }
}

impl Default for RateLimitingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            default_requests_per_second: 10,
            burst_capacity: 50,
            adaptive: true,
            backoff_multiplier: 2.0,
            max_backoff_seconds: 300,
            per_source_limits: HashMap::new(),
        }
    }
}

impl Default for SourcesConfig {
    fn default() -> Self {
        Self {
            blockchain: BlockchainSourceConfig::default(),
            dex: DexSourceConfig::default(),
            social: SocialSourceConfig::default(),
            news: NewsSourceConfig::default(),
        }
    }
}

impl Default for BlockchainSourceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            real_time: true,
            historical_backfill: false,
            transaction_monitoring: true,
            event_monitoring: true,
            contract_monitoring: true,
        }
    }
}

impl Default for DexSourceConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            exchanges: vec!["uniswap".to_string(), "sushiswap".to_string()],
            gaming_pairs_only: true,
            price_feeds: true,
            volume_tracking: true,
            liquidity_monitoring: false,
        }
    }
}

impl Default for SocialSourceConfig {
    fn default() -> Self {
        Self {
            enabled: false, // Disabled by default due to API requirements
            platforms: vec!["twitter".to_string(), "discord".to_string()],
            gaming_keywords: vec!["web3gaming".to_string(), "nftgaming".to_string()],
            sentiment_analysis: false,
            influencer_tracking: false,
        }
    }
}

impl Default for NewsSourceConfig {
    fn default() -> Self {
        Self {
            enabled: false, // Disabled by default
            sources: vec!["coindesk".to_string(), "cointelegraph".to_string()],
            gaming_categories: vec!["gaming".to_string(), "nft".to_string()],
            real_time_feeds: false,
            sentiment_analysis: false,
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "postgresql://localhost:5432/gaming_tracker".to_string(),
            max_connections: 10,
            connection_timeout_seconds: 30,
            query_timeout_seconds: 60,
            batch_insert_size: 100,
            retry_attempts: 3,
        }
    }
}
