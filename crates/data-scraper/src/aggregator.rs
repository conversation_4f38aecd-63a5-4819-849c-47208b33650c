//! Multi-Source Data Aggregator
//!
//! COMPONENT_ESSENCE::HERMES_DATA_FUSION_WISDOM
//! DANGER_LEVEL::APOLLO_CONFLICT_RESOLUTION_GUARDIAN
//! PERFORMANCE_TARGET::SUB_10MS_AGGREGATION_LATENCY
//! LAST_MODIFIED::PHASE_2_AGGREGATION_ENGINE_TRIUMPH
//! SOLUTION_TYPE::INTELLIGENT_MULTI_SOURCE_DATA_FUSION
//!
//! This module provides intelligent aggregation of data from multiple sources
//! with source attribution, conflict resolution, and data quality scoring.

use crate::logger::{ScrapingLogger, ScrapingAttempt, ScrapingResult, PerformanceMetrics, DataQuality};
use crate::types::*;
use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use shared_utils::Chain;
use std::collections::{HashMap, BTreeMap};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Multi-source data aggregator with intelligent conflict resolution
#[derive(Debug)]
pub struct MultiSourceAggregator {
    logger: Arc<ScrapingLogger>,
    sources: Arc<RwLock<HashMap<String, SourceMetadata>>>,
    aggregation_rules: Arc<RwLock<HashMap<String, AggregationRule>>>,
    conflict_resolver: Arc<ConflictResolver>,
    stats: Arc<RwLock<AggregatorStats>>,
    cache: Arc<RwLock<AggregationCache>>,
}

/// Metadata about a data source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceMetadata {
    pub source_id: String,
    pub source_type: DataSourceType,
    pub reliability_score: f64, // 0.0 to 1.0
    pub latency_ms: u64,
    pub last_update: DateTime<Utc>,
    pub data_freshness_threshold: Duration,
    pub priority: u8, // 1-10, higher = more priority
    pub trust_level: TrustLevel,
    pub supported_data_types: Vec<String>,
}

/// Trust level for data sources
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrustLevel {
    Primary,    // Authoritative source (e.g., blockchain RPC)
    Secondary,  // Reliable but not authoritative (e.g., established APIs)
    Tertiary,   // Less reliable (e.g., social media)
    Experimental, // New or unproven sources
}

/// Aggregation rule for specific data types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregationRule {
    pub data_type: String,
    pub strategy: AggregationStrategy,
    pub conflict_resolution: ConflictResolutionStrategy,
    pub min_sources: usize,
    pub max_age_seconds: u64,
    pub quality_threshold: f64,
    pub source_weights: HashMap<String, f64>,
}

/// Aggregation strategies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AggregationStrategy {
    Latest,           // Use most recent data
    Weighted,         // Weighted average based on source reliability
    Consensus,        // Require agreement from multiple sources
    Authoritative,    // Use primary source only
    BestQuality,      // Use source with highest quality score
    Merge,            // Merge data from multiple sources
}

/// Conflict resolution strategies
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConflictResolutionStrategy {
    HighestPriority,  // Use data from highest priority source
    MostRecent,       // Use most recent data
    BestQuality,      // Use data with best quality score
    Voting,           // Majority vote among sources
    WeightedAverage,  // Weighted average of conflicting values
    Manual,           // Flag for manual review
}

/// Aggregated data item with source attribution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedDataItem {
    pub id: Uuid,
    pub data_type: String,
    pub aggregated_data: serde_json::Value,
    pub source_attribution: Vec<SourceAttribution>,
    pub confidence_score: f64,
    pub quality_score: f64,
    pub aggregation_timestamp: DateTime<Utc>,
    pub conflicts_detected: Vec<DataConflict>,
    pub resolution_method: String,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Source attribution for aggregated data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceAttribution {
    pub source_id: String,
    pub contribution_weight: f64,
    pub data_timestamp: DateTime<Utc>,
    pub quality_score: f64,
    pub raw_data: serde_json::Value,
}

/// Data conflict information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataConflict {
    pub field: String,
    pub conflicting_values: Vec<ConflictingValue>,
    pub resolution: ConflictResolution,
    pub confidence: f64,
}

/// Conflicting value from a source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictingValue {
    pub source_id: String,
    pub value: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub quality_score: f64,
}

/// How a conflict was resolved
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConflictResolution {
    pub strategy_used: ConflictResolutionStrategy,
    pub chosen_value: serde_json::Value,
    pub reasoning: String,
    pub confidence: f64,
}

/// Conflict resolver for handling data conflicts
#[derive(Debug)]
pub struct ConflictResolver {
    resolution_history: Arc<RwLock<Vec<ConflictResolution>>>,
    learning_enabled: bool,
}

/// Aggregator statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatorStats {
    pub total_aggregations: u64,
    pub successful_aggregations: u64,
    pub conflicts_detected: u64,
    pub conflicts_resolved: u64,
    pub average_confidence_score: f64,
    pub average_quality_score: f64,
    pub source_performance: HashMap<String, SourcePerformance>,
    pub last_updated: DateTime<Utc>,
}

/// Performance metrics for individual sources
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourcePerformance {
    pub source_id: String,
    pub total_contributions: u64,
    pub successful_contributions: u64,
    pub average_quality: f64,
    pub average_latency_ms: f64,
    pub reliability_trend: f64,
    pub last_contribution: Option<DateTime<Utc>>,
}

/// Aggregation cache for performance optimization
#[derive(Debug)]
struct AggregationCache {
    cached_items: BTreeMap<String, CachedAggregation>,
    max_cache_size: usize,
    default_ttl: Duration,
}

/// Cached aggregation result
#[derive(Debug, Clone)]
struct CachedAggregation {
    data: AggregatedDataItem,
    expires_at: DateTime<Utc>,
    access_count: u64,
    last_accessed: DateTime<Utc>,
}

impl MultiSourceAggregator {
    /// Create a new multi-source aggregator
    pub async fn new(logger: Arc<ScrapingLogger>) -> Result<Self> {
        let conflict_resolver = Arc::new(ConflictResolver::new());
        
        Ok(Self {
            logger,
            sources: Arc::new(RwLock::new(HashMap::new())),
            aggregation_rules: Arc::new(RwLock::new(HashMap::new())),
            conflict_resolver,
            stats: Arc::new(RwLock::new(AggregatorStats::default())),
            cache: Arc::new(RwLock::new(AggregationCache::new())),
        })
    }

    /// Register a data source
    pub async fn register_source(&self, metadata: SourceMetadata) -> Result<()> {
        let source_id = metadata.source_id.clone();
        
        // Log the registration attempt
        let attempt = ScrapingAttempt {
            id: Uuid::new_v4(),
            source: "aggregator".to_string(),
            chain: None,
            target: format!("register_source_{}", source_id),
            method: "REGISTER".to_string(),
            parameters: serde_json::to_value(&metadata)?,
            timestamp: Utc::now(),
            expected_data_type: "source_metadata".to_string(),
        };
        self.logger.log_scraping_attempt(attempt).await?;

        let source_type = metadata.source_type.clone();

        // Register the source
        self.sources.write().await.insert(source_id.clone(), metadata);

        // Initialize source performance tracking
        {
            let mut stats = self.stats.write().await;
            stats.source_performance.insert(source_id.clone(), SourcePerformance {
                source_id: source_id.clone(),
                total_contributions: 0,
                successful_contributions: 0,
                average_quality: 0.0,
                average_latency_ms: 0.0,
                reliability_trend: 1.0,
                last_contribution: None,
            });
        }

        info!("📡 Registered data source: {} (type: {:?})", source_id, source_type);
        Ok(())
    }

    /// Add aggregation rule for a data type
    pub async fn add_aggregation_rule(&self, rule: AggregationRule) -> Result<()> {
        let data_type = rule.data_type.clone();
        self.aggregation_rules.write().await.insert(data_type.clone(), rule);
        info!("📋 Added aggregation rule for data type: {}", data_type);
        Ok(())
    }

    /// Aggregate data from multiple sources
    pub async fn aggregate_data(
        &self,
        data_type: &str,
        source_data: Vec<(String, serde_json::Value, DataQuality)>,
    ) -> Result<AggregatedDataItem> {
        let start_time = Instant::now();
        let aggregation_id = Uuid::new_v4();

        // Log aggregation attempt
        let attempt = ScrapingAttempt {
            id: aggregation_id,
            source: "aggregator".to_string(),
            chain: None,
            target: format!("aggregate_{}", data_type),
            method: "AGGREGATE".to_string(),
            parameters: serde_json::json!({
                "data_type": data_type,
                "source_count": source_data.len(),
                "sources": source_data.iter().map(|(s, _, _)| s).collect::<Vec<_>>()
            }),
            timestamp: Utc::now(),
            expected_data_type: data_type.to_string(),
        };
        self.logger.log_scraping_attempt(attempt).await?;

        // Check cache first
        if let Some(cached) = self.check_cache(data_type).await {
            debug!("📦 Using cached aggregation for: {}", data_type);
            return Ok(cached);
        }

        // Get aggregation rule
        let rule = self.get_aggregation_rule(data_type).await?;
        
        // Validate minimum sources requirement
        if source_data.len() < rule.min_sources {
            let error_msg = format!("Insufficient sources: {} < {}", source_data.len(), rule.min_sources);
            warn!("⚠️ {}", error_msg);
            return Err(anyhow::anyhow!(error_msg));
        }

        // Filter sources by quality threshold
        let quality_filtered: Vec<_> = source_data.into_iter()
            .filter(|(_, _, quality)| quality.score >= rule.quality_threshold)
            .collect();

        if quality_filtered.is_empty() {
            let error_msg = "No sources meet quality threshold";
            warn!("⚠️ {}", error_msg);
            return Err(anyhow::anyhow!(error_msg));
        }

        // Detect conflicts
        let conflicts = self.detect_conflicts(&quality_filtered).await?;
        
        // Resolve conflicts and aggregate
        let (aggregated_data, source_attribution, confidence_score) = 
            self.resolve_and_aggregate(&rule, quality_filtered, &conflicts).await?;

        // Calculate overall quality score
        let quality_score = source_attribution.iter()
            .map(|attr| attr.quality_score * attr.contribution_weight)
            .sum::<f64>() / source_attribution.iter().map(|attr| attr.contribution_weight).sum::<f64>();

        // Create aggregated item
        let aggregated_item = AggregatedDataItem {
            id: aggregation_id,
            data_type: data_type.to_string(),
            aggregated_data,
            source_attribution,
            confidence_score,
            quality_score,
            aggregation_timestamp: Utc::now(),
            conflicts_detected: conflicts,
            resolution_method: format!("{:?}", rule.strategy),
            metadata: HashMap::new(),
        };

        // Cache the result
        self.cache_result(data_type, &aggregated_item).await?;

        // Update statistics
        self.update_stats(&aggregated_item).await?;

        // Log successful aggregation
        let duration = start_time.elapsed();
        let result = ScrapingResult {
            attempt_id: aggregation_id,
            success: true,
            timestamp: Utc::now(),
            duration_ms: duration.as_millis() as u64,
            data_size_bytes: Some(serde_json::to_string(&aggregated_item)?.len()),
            items_count: Some(1),
            error_message: None,
            error_type: None,
            retry_count: 0,
            data_quality: Some(DataQuality {
                score: quality_score,
                completeness: 1.0,
                accuracy: confidence_score,
                consistency: 1.0,
                timeliness: 1.0,
                issues: vec![],
                validation_errors: vec![],
            }),
            performance_metrics: Some(PerformanceMetrics {
                duration_ms: duration.as_millis() as u64,
                data_size_bytes: serde_json::to_string(&aggregated_item)?.len(),
                throughput_items_per_second: 1000.0 / duration.as_millis() as f64,
                memory_usage_mb: 0.0, // Would be calculated in real implementation
                cpu_usage_percent: 0.0,
                network_latency_ms: 0,
            }),
        };
        self.logger.log_scraping_result(result).await?;

        info!("✅ Aggregated {} from {} sources (confidence: {:.2}, quality: {:.2})", 
               data_type, aggregated_item.source_attribution.len(), confidence_score, quality_score);

        Ok(aggregated_item)
    }

    /// Get aggregation rule for data type
    async fn get_aggregation_rule(&self, data_type: &str) -> Result<AggregationRule> {
        let rules = self.aggregation_rules.read().await;
        rules.get(data_type)
            .cloned()
            .ok_or_else(|| anyhow::anyhow!("No aggregation rule for data type: {}", data_type))
    }

    /// Detect conflicts between sources
    async fn detect_conflicts(
        &self,
        source_data: &[(String, serde_json::Value, DataQuality)],
    ) -> Result<Vec<DataConflict>> {
        let mut conflicts = Vec::new();

        // Simple conflict detection - compare values from different sources
        // In a real implementation, this would be more sophisticated
        if source_data.len() > 1 {
            // For demonstration, we'll detect conflicts in numeric fields
            for (field, values) in self.extract_comparable_fields(source_data) {
                if values.len() > 1 {
                    let unique_values: std::collections::HashSet<_> = values.iter().map(|v| &v.value).collect();
                    if unique_values.len() > 1 {
                        conflicts.push(DataConflict {
                            field,
                            conflicting_values: values,
                            resolution: ConflictResolution {
                                strategy_used: ConflictResolutionStrategy::HighestPriority,
                                chosen_value: serde_json::Value::Null,
                                reasoning: "Conflict detected, resolution pending".to_string(),
                                confidence: 0.0,
                            },
                            confidence: 0.8,
                        });
                    }
                }
            }
        }

        Ok(conflicts)
    }

    /// Extract comparable fields from source data
    fn extract_comparable_fields(
        &self,
        source_data: &[(String, serde_json::Value, DataQuality)],
    ) -> HashMap<String, Vec<ConflictingValue>> {
        let mut fields = HashMap::new();

        for (source_id, data, quality) in source_data {
            if let serde_json::Value::Object(obj) = data {
                for (field_name, field_value) in obj {
                    fields.entry(field_name.clone()).or_insert_with(Vec::new).push(ConflictingValue {
                        source_id: source_id.clone(),
                        value: field_value.clone(),
                        timestamp: Utc::now(),
                        quality_score: quality.score,
                    });
                }
            }
        }

        fields
    }

    /// Resolve conflicts and aggregate data
    async fn resolve_and_aggregate(
        &self,
        rule: &AggregationRule,
        source_data: Vec<(String, serde_json::Value, DataQuality)>,
        conflicts: &[DataConflict],
    ) -> Result<(serde_json::Value, Vec<SourceAttribution>, f64)> {
        let mut source_attribution = Vec::new();
        let mut confidence_score = 1.0;

        // Create source attribution
        for (source_id, data, quality) in &source_data {
            let weight = rule.source_weights.get(source_id).copied().unwrap_or(1.0);
            source_attribution.push(SourceAttribution {
                source_id: source_id.clone(),
                contribution_weight: weight,
                data_timestamp: Utc::now(),
                quality_score: quality.score,
                raw_data: data.clone(),
            });
        }

        // Apply aggregation strategy
        let aggregated_data = match rule.strategy {
            AggregationStrategy::Latest => {
                // Use most recent data (simplified - would use actual timestamps)
                source_data.last().unwrap().1.clone()
            }
            AggregationStrategy::Authoritative => {
                // Use primary source (highest priority)
                let sources = self.sources.read().await;
                let primary_source = source_data.iter()
                    .max_by_key(|(source_id, _, _)| {
                        sources.get(source_id).map(|s| s.priority).unwrap_or(0)
                    });
                primary_source.unwrap().1.clone()
            }
            AggregationStrategy::BestQuality => {
                // Use source with highest quality score
                let best_quality = source_data.iter()
                    .max_by(|(_, _, q1), (_, _, q2)| q1.score.partial_cmp(&q2.score).unwrap());
                best_quality.unwrap().1.clone()
            }
            _ => {
                // Default to first source for now
                source_data.first().unwrap().1.clone()
            }
        };

        // Adjust confidence based on conflicts
        if !conflicts.is_empty() {
            confidence_score *= 0.8; // Reduce confidence when conflicts exist
        }

        Ok((aggregated_data, source_attribution, confidence_score))
    }

    /// Check cache for existing aggregation
    async fn check_cache(&self, data_type: &str) -> Option<AggregatedDataItem> {
        let cache = self.cache.read().await;
        if let Some(cached) = cache.cached_items.get(data_type) {
            if cached.expires_at > Utc::now() {
                return Some(cached.data.clone());
            }
        }
        None
    }

    /// Cache aggregation result
    async fn cache_result(&self, data_type: &str, item: &AggregatedDataItem) -> Result<()> {
        let mut cache = self.cache.write().await;
        let default_ttl = cache.default_ttl;
        cache.cached_items.insert(data_type.to_string(), CachedAggregation {
            data: item.clone(),
            expires_at: Utc::now() + default_ttl,
            access_count: 1,
            last_accessed: Utc::now(),
        });
        Ok(())
    }

    /// Update aggregator statistics
    async fn update_stats(&self, item: &AggregatedDataItem) -> Result<()> {
        let mut stats = self.stats.write().await;
        stats.total_aggregations += 1;
        stats.successful_aggregations += 1;
        stats.conflicts_detected += item.conflicts_detected.len() as u64;
        stats.conflicts_resolved += item.conflicts_detected.len() as u64;
        
        // Update running averages
        let total = stats.successful_aggregations as f64;
        stats.average_confidence_score = (stats.average_confidence_score * (total - 1.0) + item.confidence_score) / total;
        stats.average_quality_score = (stats.average_quality_score * (total - 1.0) + item.quality_score) / total;
        
        stats.last_updated = Utc::now();
        Ok(())
    }

    /// Get aggregator statistics
    pub async fn get_stats(&self) -> AggregatorStats {
        self.stats.read().await.clone()
    }
}

impl ConflictResolver {
    /// Create a new conflict resolver
    pub fn new() -> Self {
        Self {
            resolution_history: Arc::new(RwLock::new(Vec::new())),
            learning_enabled: true,
        }
    }
}

impl AggregationCache {
    /// Create a new aggregation cache
    pub fn new() -> Self {
        Self {
            cached_items: BTreeMap::new(),
            max_cache_size: 1000,
            default_ttl: Duration::from_secs(300), // 5 minutes
        }
    }
}

impl Default for AggregatorStats {
    fn default() -> Self {
        Self {
            total_aggregations: 0,
            successful_aggregations: 0,
            conflicts_detected: 0,
            conflicts_resolved: 0,
            average_confidence_score: 0.0,
            average_quality_score: 0.0,
            source_performance: HashMap::new(),
            last_updated: Utc::now(),
        }
    }
}
