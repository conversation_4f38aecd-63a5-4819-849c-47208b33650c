//! Data Sources
//!
//! Implementation of various data sources for the scraping engine.

pub mod blockchain;
pub mod dex;
pub mod social;
pub mod news;

pub use blockchain::*;
pub use dex::*;
pub use social::*;
pub use news::*;

use crate::types::*;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// Base data source implementation
#[derive(Debug)]
pub struct BaseDataSource {
    pub name: String,
    pub data_type: DataSourceType,
    pub stats: Arc<RwLock<SourceStats>>,
    pub health: Arc<RwLock<SourceHealth>>,
    pub start_time: Instant,
    pub counters: SourceCounters,
}

/// Atomic counters for data source performance
#[derive(Debug)]
pub struct SourceCounters {
    pub total_requests: AtomicU64,
    pub successful_requests: AtomicU64,
    pub failed_requests: AtomicU64,
    pub data_points_collected: AtomicU64,
    pub rate_limit_hits: AtomicU64,
}

impl BaseDataSource {
    /// Create a new base data source
    pub fn new(name: String, data_type: DataSourceType) -> Self {
        Self {
            name,
            data_type,
            stats: Arc::new(RwLock::new(SourceStats::default())),
            health: Arc::new(RwLock::new(SourceHealth::default())),
            start_time: Instant::now(),
            counters: SourceCounters {
                total_requests: AtomicU64::new(0),
                successful_requests: AtomicU64::new(0),
                failed_requests: AtomicU64::new(0),
                data_points_collected: AtomicU64::new(0),
                rate_limit_hits: AtomicU64::new(0),
            },
        }
    }

    /// Record a successful request
    pub async fn record_success(&self, response_time_ms: u64, data_points: u64) {
        self.counters.total_requests.fetch_add(1, Ordering::Relaxed);
        self.counters.successful_requests.fetch_add(1, Ordering::Relaxed);
        self.counters.data_points_collected.fetch_add(data_points, Ordering::Relaxed);

        // Update health
        {
            let mut health = self.health.write().await;
            health.last_successful_fetch = Some(Utc::now());
            health.response_time_ms = response_time_ms;
            health.is_healthy = true;
            health.issues.clear();
        }

        // Update stats
        self.update_stats().await;
    }

    /// Record a failed request
    pub async fn record_failure(&self, error: &str) {
        self.counters.total_requests.fetch_add(1, Ordering::Relaxed);
        self.counters.failed_requests.fetch_add(1, Ordering::Relaxed);

        // Update health
        {
            let mut health = self.health.write().await;
            health.issues.push(error.to_string());
            
            // Calculate error rate
            let total = self.counters.total_requests.load(Ordering::Relaxed);
            let failed = self.counters.failed_requests.load(Ordering::Relaxed);
            health.error_rate = if total > 0 { failed as f64 / total as f64 } else { 0.0 };
            
            // Mark as unhealthy if error rate is too high
            health.is_healthy = health.error_rate < 0.1; // Less than 10% error rate
        }

        // Update stats
        self.update_stats().await;
    }

    /// Record a rate limit hit
    pub async fn record_rate_limit(&self) {
        self.counters.rate_limit_hits.fetch_add(1, Ordering::Relaxed);
        
        {
            let mut health = self.health.write().await;
            health.issues.push("Rate limit exceeded".to_string());
        }
    }

    /// Update statistics
    async fn update_stats(&self) {
        let mut stats = self.stats.write().await;
        
        stats.total_requests = self.counters.total_requests.load(Ordering::Relaxed);
        stats.successful_requests = self.counters.successful_requests.load(Ordering::Relaxed);
        stats.failed_requests = self.counters.failed_requests.load(Ordering::Relaxed);
        stats.data_points_collected = self.counters.data_points_collected.load(Ordering::Relaxed);
        stats.rate_limit_hits = self.counters.rate_limit_hits.load(Ordering::Relaxed);
        stats.last_activity = Some(Utc::now());
        
        // Calculate uptime percentage
        let uptime_seconds = self.start_time.elapsed().as_secs();
        stats.uptime_percentage = if uptime_seconds > 0 {
            let healthy_time = uptime_seconds; // Simplified calculation
            (healthy_time as f64 / uptime_seconds as f64) * 100.0
        } else {
            100.0
        };

        // Calculate average response time (simplified)
        if stats.successful_requests > 0 {
            let health = self.health.read().await;
            stats.average_response_time_ms = health.response_time_ms as f64;
        }
    }
}

impl Default for SourceStats {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            average_response_time_ms: 0.0,
            data_points_collected: 0,
            last_activity: None,
            rate_limit_hits: 0,
            uptime_percentage: 100.0,
        }
    }
}

impl Default for SourceHealth {
    fn default() -> Self {
        Self {
            is_healthy: true,
            last_successful_fetch: None,
            error_rate: 0.0,
            response_time_ms: 0,
            issues: Vec::new(),
        }
    }
}

/// Mock blockchain data source for testing
#[derive(Debug, Clone)]
pub struct MockBlockchainSource {
    base: Arc<BaseDataSource>,
}

impl MockBlockchainSource {
    pub fn new() -> Self {
        Self {
            base: Arc::new(BaseDataSource::new(
                "mock_blockchain".to_string(),
                DataSourceType::Blockchain,
            )),
        }
    }
}

#[async_trait]
impl DataSource for MockBlockchainSource {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn data_type(&self) -> DataSourceType {
        self.base.data_type.clone()
    }

    async fn start(&self) -> anyhow::Result<()> {
        info!("🚀 Starting mock blockchain data source");
        
        // Simulate data collection
        let base = Arc::clone(&self.base);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(5));
            
            loop {
                interval.tick().await;
                
                // Simulate successful data collection
                base.record_success(100, 10).await;
                debug!("📊 Mock blockchain data collected");
            }
        });

        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        info!("🛑 Stopping mock blockchain data source");
        Ok(())
    }

    async fn health_check(&self) -> anyhow::Result<SourceHealth> {
        Ok(self.base.health.read().await.clone())
    }

    async fn get_stats(&self) -> anyhow::Result<SourceStats> {
        Ok(self.base.stats.read().await.clone())
    }

    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync> {
        Box::new(self.clone())
    }
}

/// Mock DEX data source for testing
#[derive(Debug, Clone)]
pub struct MockDexSource {
    base: Arc<BaseDataSource>,
}

impl MockDexSource {
    pub fn new() -> Self {
        Self {
            base: Arc::new(BaseDataSource::new(
                "mock_dex".to_string(),
                DataSourceType::DEX,
            )),
        }
    }
}

#[async_trait]
impl DataSource for MockDexSource {
    fn name(&self) -> &str {
        &self.base.name
    }

    fn data_type(&self) -> DataSourceType {
        self.base.data_type.clone()
    }

    async fn start(&self) -> anyhow::Result<()> {
        info!("🚀 Starting mock DEX data source");
        
        // Simulate data collection
        let base = Arc::clone(&self.base);
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(std::time::Duration::from_secs(10));
            
            loop {
                interval.tick().await;
                
                // Simulate successful data collection
                base.record_success(200, 5).await;
                debug!("📊 Mock DEX data collected");
            }
        });

        Ok(())
    }

    async fn stop(&self) -> anyhow::Result<()> {
        info!("🛑 Stopping mock DEX data source");
        Ok(())
    }

    async fn health_check(&self) -> anyhow::Result<SourceHealth> {
        Ok(self.base.health.read().await.clone())
    }

    async fn get_stats(&self) -> anyhow::Result<SourceStats> {
        Ok(self.base.stats.read().await.clone())
    }

    fn clone_box(&self) -> Box<dyn DataSource + Send + Sync> {
        Box::new(self.clone())
    }
}

/// Data source factory
pub struct DataSourceFactory;

impl DataSourceFactory {
    /// Create a data source by name
    pub fn create(source_type: &str, config: serde_json::Value) -> anyhow::Result<Box<dyn DataSource + Send + Sync>> {
        match source_type {
            "mock_blockchain" => Ok(Box::new(MockBlockchainSource::new())),
            "mock_dex" => Ok(Box::new(MockDexSource::new())),
            "blockchain" => Ok(Box::new(BlockchainDataSource::new(config)?)),
            "dex" => Ok(Box::new(DexDataSource::new(config)?)),
            "social" => Ok(Box::new(SocialDataSource::new(config)?)),
            "news" => Ok(Box::new(NewsDataSource::new(config)?)),
            _ => Err(anyhow::anyhow!("Unknown data source type: {}", source_type)),
        }
    }

    /// Get available data source types
    pub fn available_types() -> Vec<&'static str> {
        vec![
            "mock_blockchain",
            "mock_dex", 
            "blockchain",
            "dex",
            "social",
            "news",
        ]
    }
}
