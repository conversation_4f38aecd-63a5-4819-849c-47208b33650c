//! API route definitions for the Web3 Gaming Intelligence Platform
//!
//! COMPONENT_ESSENCE::HERMES_API_ROUTE_NETWORK
//! DANGER_LEVEL::APOLLO_ENDPOINT_COORDINATION
//! PERFORMANCE_TARGET::SUB_10MS_ROUTE_RESOLUTION
//! LAST_MODIFIED::PHASE_4_API_LAYER_MIGRATION
//! DEPENDENCIES::AXUM_ROUTING_ECOSYSTEM
//!
//! This module provides comprehensive API routes for:
//! - Health and system monitoring
//! - Gaming analytics and insights
//! - Blockchain data access
//! - Contract analysis results
//! - Real-time WebSocket connections

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Json},
    routing::{get, post, put, delete},
    Router,
};
use serde::{Deserialize, Serialize};
use shared_utils::{Address, Chain, semantic_protection::*};
use std::collections::HashMap;
use tracing::{debug, info};

use crate::{
    auth::JwtClaims,
    server::{AppError, AppState},
};

/// Health check routes
///
/// FUNCTION_ESSENCE::APOLLO_SYSTEM_HEALTH_ORACLE
/// DANGER_LEVEL::HERMES_MONITORING_ESSENTIAL
pub fn health_routes() -> Router<AppState> {
    Router::new()
        .route("/", get(health_check))
        .route("/ready", get(readiness_check))
        .route("/live", get(liveness_check))
        .route("/detailed", get(detailed_health_check))
}

/// Gaming analytics routes
///
/// FUNCTION_ESSENCE::ATHENA_GAMING_INTELLIGENCE_ROUTES
/// DANGER_LEVEL::APOLLO_ANALYTICS_PRECISION
pub fn gaming_routes() -> Router<AppState> {
    Router::new()
        .route("/overview", get(gaming_overview))
        .route("/trends", get(gaming_trends))
        .route("/top-games", get(top_games))
        .route("/player-analytics", get(player_analytics))
        .route("/token-analytics", get(token_analytics))
        .route("/nft-analytics", get(nft_analytics))
        .route("/whale-activity", get(whale_activity))
        .route("/market-sentiment", get(market_sentiment))
}

/// Analytics engine routes
///
/// FUNCTION_ESSENCE::PROMETHEUS_ANALYTICS_FIRE
/// DANGER_LEVEL::TITAN_DATA_PROCESSING
pub fn analytics_routes() -> Router<AppState> {
    Router::new()
        .route("/summary", get(analytics_summary))
        .route("/performance", get(analytics_performance))
        .route("/insights", get(analytics_insights))
        .route("/reports", get(analytics_reports))
        .route("/reports", post(create_analytics_report))
        .route("/reports/:id", get(get_analytics_report))
        .route("/custom-query", post(custom_analytics_query))
}

/// Blockchain data routes
///
/// FUNCTION_ESSENCE::HERMES_BLOCKCHAIN_DATA_MESSENGER
/// DANGER_LEVEL::CYCLOPS_RPC_COORDINATION
pub fn blockchain_routes() -> Router<AppState> {
    Router::new()
        .route("/chains", get(supported_chains))
        .route("/chain/:chain/status", get(chain_status))
        .route("/chain/:chain/latest-block", get(latest_block))
        .route("/chain/:chain/transaction/:hash", get(get_transaction))
        .route("/chain/:chain/address/:address", get(get_address_info))
        .route("/chain/:chain/contract/:address", get(get_contract_info))
        .route("/multi-chain/summary", get(multi_chain_summary))
}

/// Contract analysis routes
///
/// FUNCTION_ESSENCE::APOLLO_CONTRACT_ANALYSIS_TEMPLE
/// DANGER_LEVEL::ATHENA_PATTERN_RECOGNITION
pub fn contract_routes() -> Router<AppState> {
    Router::new()
        .route("/analyze", post(analyze_contract))
        .route("/gaming-contracts", get(gaming_contracts))
        .route("/contract/:chain/:address", get(contract_details))
        .route("/contract/:chain/:address/gaming-score", get(contract_gaming_score))
        .route("/contract/:chain/:address/activity", get(contract_activity))
        .route("/trending", get(trending_contracts))
        .route("/categories", get(contract_categories))
}

/// WebSocket routes for real-time data
///
/// FUNCTION_ESSENCE::ARES_REAL_TIME_BATTLEFIELD_ROUTES
/// DANGER_LEVEL::KRAKEN_WEBSOCKET_COORDINATION
pub fn websocket_routes() -> Router<AppState> {
    Router::new()
        .route("/gaming-events", get(websocket_gaming_events))
        .route("/contract-analysis", get(websocket_contract_analysis))
        .route("/market-data", get(websocket_market_data))
        .route("/whale-alerts", get(websocket_whale_alerts))
}

/// Health check handlers
///
/// FUNCTION_ESSENCE::APOLLO_HEALTH_DIAGNOSTICS

async fn health_check() -> impl IntoResponse {
    Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "service": "web3-gaming-intelligence-platform",
        "version": env!("CARGO_PKG_VERSION")
    }))
}

async fn readiness_check(State(state): State<AppState>) -> Result<impl IntoResponse, AppError> {
    // Check database connectivity
    let db_healthy = sqlx::query("SELECT 1").fetch_one(&state.database_pool).await.is_ok();

    // Check Redis connectivity
    let redis_healthy = true; // Simplified check

    // Check blockchain engine
    let blockchain_healthy = true; // Would check RPC connectivity

    let ready = db_healthy && redis_healthy && blockchain_healthy;

    let status = if ready { "ready" } else { "not ready" };
    let http_status = if ready { StatusCode::OK } else { StatusCode::SERVICE_UNAVAILABLE };

    Ok((http_status, Json(serde_json::json!({
        "status": status,
        "checks": {
            "database": db_healthy,
            "redis": redis_healthy,
            "blockchain": blockchain_healthy
        },
        "timestamp": chrono::Utc::now()
    }))))
}

async fn liveness_check() -> impl IntoResponse {
    // Simple liveness check - if we can respond, we're alive
    Json(serde_json::json!({
        "status": "alive",
        "timestamp": chrono::Utc::now()
    }))
}

async fn detailed_health_check(State(state): State<AppState>) -> Result<impl IntoResponse, AppError> {
    // Get comprehensive system health information
    let server_stats = state.blockchain_engine.get_stats().await;

    Ok(Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now(),
        "service": "web3-gaming-intelligence-platform",
        "version": env!("CARGO_PKG_VERSION"),
        "uptime_seconds": 0, // Would calculate from start time
        "system": {
            "memory_usage_mb": 0, // Would get from system
            "cpu_usage_percent": 0.0,
            "active_connections": 0
        },
        "blockchain": {
            "supported_chains": 8,
            "rpc_pool_status": "healthy",
            "last_block_sync": chrono::Utc::now()
        },
        "database": {
            "status": "connected",
            "pool_size": state.database_pool.size(),
            "active_connections": 0
        }
    })))
}

/// Gaming analytics handlers
///
/// FUNCTION_ESSENCE::ATHENA_GAMING_INTELLIGENCE_ORACLES

async fn gaming_overview(
    State(_state): State<AppState>,
) -> Result<impl IntoResponse, AppError> {
    debug!("Gaming overview requested");

    // Get gaming analytics overview from analytics engine (placeholder)
    let overview = serde_json::json!({
        "total_contracts": 1500,
        "active_games": 250,
        "total_players": 50000,
        "volume_24h": 1000000.0,
        "top_chains": ["ethereum", "polygon", "solana"],
        "trending_games": ["game1", "game2", "game3"]
    });

    Ok(Json(serde_json::json!({
        "total_gaming_contracts": overview["total_contracts"],
        "active_games": overview["active_games"],
        "total_players": overview["total_players"],
        "total_volume_24h": overview["volume_24h"],
        "top_chains": overview["top_chains"],
        "trending_games": overview["trending_games"],
        "timestamp": chrono::Utc::now()
    })))
}

async fn gaming_trends(
    State(state): State<AppState>,
    Query(params): Query<TrendsQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    debug!("Gaming trends requested: {:?}", params);

    // Placeholder implementation
    let trends = serde_json::json!({
        "timeframe": params.timeframe.unwrap_or_else(|| "24h".to_string()),
        "chain": params.chain,
        "trends": ["increasing_players", "rising_volume", "new_games"]
    });

    Ok(Json(trends))
}

async fn top_games(
    State(_state): State<AppState>,
    Query(params): Query<TopGamesQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let limit = params.limit.unwrap_or(10);
    let metric = params.metric.unwrap_or_else(|| "volume".to_string());

    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "top_games": [],
        "limit": limit,
        "metric": metric,
        "timestamp": chrono::Utc::now()
    })))
}

async fn player_analytics(
    State(state): State<AppState>,
    Query(params): Query<PlayerAnalyticsQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Create semantic protection for API endpoint
    let protection = CommonProtections::api_endpoint();

    // Semantic validation of input parameters
    protection.validate_semantic_integrity(&params.address, "player_address")
        .map_err(|e| AppError::BadRequest(format!("Address semantic validation failed: {}", e)))?;

    protection.validate_semantic_integrity(&params.chain, "blockchain_chain")
        .map_err(|e| AppError::BadRequest(format!("Chain semantic validation failed: {}", e)))?;

    // Placeholder implementation with semantic protection
    let response_data = serde_json::json!({
        "address": params.address,
        "chain": params.chain,
        "analytics": "placeholder",
        "timestamp": chrono::Utc::now()
    });

    // Semantic validation of output
    protection.validate_semantic_integrity(&response_data, "player_analytics_response")
        .map_err(|e| AppError::InternalServerError(format!("Response semantic validation failed: {}", e)))?;

    Ok(Json(response_data))
}

async fn token_analytics(
    State(_state): State<AppState>,
    Query(params): Query<TokenAnalyticsQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "token_address": params.token_address,
        "chain": params.chain,
        "analytics": "placeholder",
        "timestamp": chrono::Utc::now()
    })))
}

async fn nft_analytics(
    State(_state): State<AppState>,
    Query(params): Query<NftAnalyticsQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "collection_address": params.collection_address,
        "chain": params.chain,
        "analytics": "placeholder",
        "timestamp": chrono::Utc::now()
    })))
}

async fn whale_activity(
    State(_state): State<AppState>,
    Query(params): Query<WhaleActivityQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let min_value = params.min_value_usd.unwrap_or(10000.0);
    let timeframe = params.timeframe.unwrap_or_else(|| "24h".to_string());

    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "min_value_usd": min_value,
        "timeframe": timeframe,
        "whale_activity": [],
        "timestamp": chrono::Utc::now()
    })))
}

async fn market_sentiment(
    State(_state): State<AppState>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "sentiment": "neutral",
        "confidence": 0.75,
        "timestamp": chrono::Utc::now()
    })))
}

/// Analytics engine handlers
///
/// FUNCTION_ESSENCE::PROMETHEUS_ANALYTICS_FIRE_HANDLERS

async fn analytics_summary(
    State(_state): State<AppState>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "summary": "placeholder",
        "timestamp": chrono::Utc::now()
    })))
}

async fn analytics_performance(
    State(_state): State<AppState>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "performance": "placeholder",
        "timestamp": chrono::Utc::now()
    })))
}

async fn analytics_insights(
    State(_state): State<AppState>,
    Query(params): Query<InsightsQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "category": params.category,
        "timeframe": params.timeframe,
        "insights": [],
        "timestamp": chrono::Utc::now()
    })))
}

async fn analytics_reports(
    State(_state): State<AppState>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "reports": [],
        "timestamp": chrono::Utc::now()
    })))
}

async fn create_analytics_report(
    State(_state): State<AppState>,
    _claims: JwtClaims,
    Json(request): Json<CreateReportRequest>,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "report_id": "placeholder-id",
        "name": request.name,
        "status": "created",
        "timestamp": chrono::Utc::now()
    })))
}

async fn get_analytics_report(
    State(_state): State<AppState>,
    Path(report_id): Path<String>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "report_id": report_id,
        "status": "placeholder",
        "timestamp": chrono::Utc::now()
    })))
}

async fn custom_analytics_query(
    State(_state): State<AppState>,
    _claims: JwtClaims,
    Json(query): Json<CustomQueryRequest>,
) -> Result<impl IntoResponse, AppError> {
    // Placeholder implementation
    Ok(Json(serde_json::json!({
        "query_type": query.query_type,
        "results": [],
        "timestamp": chrono::Utc::now()
    })))
}

/// Blockchain data handlers
///
/// FUNCTION_ESSENCE::HERMES_BLOCKCHAIN_DATA_ORACLES

async fn supported_chains(
    State(_state): State<AppState>,
) -> Result<impl IntoResponse, AppError> {
    Ok(Json(serde_json::json!({
        "chains": [
            {"name": "Ethereum", "id": "ethereum", "chain_id": 1},
            {"name": "Polygon", "id": "polygon", "chain_id": 137},
            {"name": "BSC", "id": "bsc", "chain_id": 56},
            {"name": "Arbitrum", "id": "arbitrum", "chain_id": 42161},
            {"name": "Optimism", "id": "optimism", "chain_id": 10},
            {"name": "Base", "id": "base", "chain_id": 8453},
            {"name": "Avalanche", "id": "avalanche", "chain_id": 43114},
            {"name": "Solana", "id": "solana", "chain_id": null}
        ],
        "total": 8
    })))
}

async fn chain_status(
    State(state): State<AppState>,
    Path(chain): Path<String>,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let status = state.blockchain_engine.get_chain_status(chain_enum).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(status))
}

async fn latest_block(
    State(state): State<AppState>,
    Path(chain): Path<String>,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let block = state.blockchain_engine.get_latest_block(chain_enum).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(block))
}

async fn get_transaction(
    State(state): State<AppState>,
    Path((chain, hash)): Path<(String, String)>,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let transaction = state.blockchain_engine.get_transaction(chain_enum, &hash).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(transaction))
}

async fn get_address_info(
    State(state): State<AppState>,
    Path((chain, address)): Path<(String, String)>,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let address_info = state.blockchain_engine.get_address_info(chain_enum, &address).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(address_info))
}

async fn get_contract_info(
    State(state): State<AppState>,
    Path((chain, address)): Path<(String, String)>,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let contract_info = state.blockchain_engine.get_contract_info(chain_enum, &address).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(contract_info))
}

async fn multi_chain_summary(
    State(state): State<AppState>,
) -> Result<impl IntoResponse, AppError> {
    let summary = state.blockchain_engine.get_multi_chain_summary().await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(summary))
}

/// Contract analysis handlers
///
/// FUNCTION_ESSENCE::APOLLO_CONTRACT_ANALYSIS_ORACLES

async fn analyze_contract(
    State(state): State<AppState>,
    _claims: JwtClaims,
    Json(request): Json<AnalyzeContractRequest>,
) -> Result<impl IntoResponse, AppError> {
    let analysis = state.blockchain_engine.analyze_contract(
        request.chain,
        &request.address
    ).await.map_err(|e| AppError::Internal(e))?;

    Ok(Json(analysis))
}

async fn gaming_contracts(
    State(state): State<AppState>,
    Query(params): Query<GamingContractsQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let contracts = state.blockchain_engine.get_gaming_contracts(
        params.chain,
        params.min_confidence.unwrap_or(0.7),
        params.limit.unwrap_or(50)
    ).await.map_err(|e| AppError::Internal(e))?;

    Ok(Json(contracts))
}

async fn contract_details(
    State(state): State<AppState>,
    Path((chain, address)): Path<(String, String)>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let details = state.blockchain_engine.get_contract_details(chain_enum, &address).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(details))
}

async fn contract_gaming_score(
    State(state): State<AppState>,
    Path((chain, address)): Path<(String, String)>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let score = state.blockchain_engine.get_gaming_score(chain_enum, &address).await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(serde_json::json!({
        "chain": chain,
        "address": address,
        "gaming_score": score,
        "timestamp": chrono::Utc::now()
    })))
}

async fn contract_activity(
    State(state): State<AppState>,
    Path((chain, address)): Path<(String, String)>,
    Query(params): Query<ActivityQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let chain_enum = parse_chain(&chain)?;
    let timeframe = params.timeframe.unwrap_or_else(|| "24h".to_string());

    let activity = state.blockchain_engine.get_contract_activity(
        chain_enum,
        &address,
        &timeframe
    ).await.map_err(|e| AppError::Internal(e))?;

    Ok(Json(activity))
}

async fn trending_contracts(
    State(state): State<AppState>,
    Query(params): Query<TrendingQuery>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let trending = state.blockchain_engine.get_trending_contracts(
        params.timeframe.unwrap_or_else(|| "24h".to_string()),
        params.limit.unwrap_or(20)
    ).await.map_err(|e| AppError::Internal(e))?;

    Ok(Json(trending))
}

async fn contract_categories(
    State(state): State<AppState>,
    _claims: JwtClaims,
) -> Result<impl IntoResponse, AppError> {
    let categories = state.blockchain_engine.get_contract_categories().await
        .map_err(|e| AppError::Internal(e))?;

    Ok(Json(categories))
}

/// WebSocket handlers (placeholder - actual WebSocket handling would be different)
///
/// FUNCTION_ESSENCE::ARES_WEBSOCKET_BATTLEFIELD_HANDLERS

async fn websocket_gaming_events() -> Result<impl IntoResponse, AppError> {
    // In a real implementation, this would upgrade to WebSocket
    Ok(Json(serde_json::json!({
        "message": "WebSocket endpoint for gaming events",
        "upgrade_required": true
    })))
}

async fn websocket_contract_analysis() -> Result<impl IntoResponse, AppError> {
    Ok(Json(serde_json::json!({
        "message": "WebSocket endpoint for contract analysis",
        "upgrade_required": true
    })))
}

async fn websocket_market_data() -> Result<impl IntoResponse, AppError> {
    Ok(Json(serde_json::json!({
        "message": "WebSocket endpoint for market data",
        "upgrade_required": true
    })))
}

async fn websocket_whale_alerts() -> Result<impl IntoResponse, AppError> {
    Ok(Json(serde_json::json!({
        "message": "WebSocket endpoint for whale alerts",
        "upgrade_required": true
    })))
}

/// Query parameter structures for API endpoints
///
/// FUNCTION_ESSENCE::HERMES_PARAMETER_STRUCTURES

#[derive(Debug, Deserialize)]
pub struct TrendsQuery {
    pub timeframe: Option<String>,
    pub chain: Option<Chain>,
}

#[derive(Debug, Deserialize)]
pub struct TopGamesQuery {
    pub limit: Option<usize>,
    pub metric: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct PlayerAnalyticsQuery {
    pub address: Option<String>,
    pub chain: Option<Chain>,
}

#[derive(Debug, Deserialize)]
pub struct TokenAnalyticsQuery {
    pub token_address: String,
    pub chain: Chain,
}

#[derive(Debug, Deserialize)]
pub struct NftAnalyticsQuery {
    pub collection_address: String,
    pub chain: Chain,
}

#[derive(Debug, Deserialize)]
pub struct WhaleActivityQuery {
    pub min_value_usd: Option<f64>,
    pub timeframe: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct InsightsQuery {
    pub category: Option<String>,
    pub timeframe: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct GamingContractsQuery {
    pub chain: Option<Chain>,
    pub min_confidence: Option<f64>,
    pub limit: Option<usize>,
}

#[derive(Debug, Deserialize)]
pub struct ActivityQuery {
    pub timeframe: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct TrendingQuery {
    pub timeframe: Option<String>,
    pub limit: Option<usize>,
}

/// Request structures for POST endpoints
///
/// FUNCTION_ESSENCE::APOLLO_REQUEST_STRUCTURES

#[derive(Debug, Deserialize)]
pub struct CreateReportRequest {
    pub name: String,
    pub description: Option<String>,
    pub parameters: HashMap<String, serde_json::Value>,
    pub schedule: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CustomQueryRequest {
    pub query_type: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub filters: Option<HashMap<String, serde_json::Value>>,
}

#[derive(Debug, Deserialize)]
pub struct AnalyzeContractRequest {
    pub chain: Chain,
    pub address: String,
    pub force_refresh: Option<bool>,
}

/// Utility functions
///
/// FUNCTION_ESSENCE::HERMES_UTILITY_HELPERS

fn parse_chain(chain_str: &str) -> Result<Chain, AppError> {
    match chain_str.to_lowercase().as_str() {
        "ethereum" | "eth" => Ok(Chain::Ethereum),
        "polygon" | "matic" => Ok(Chain::Polygon),
        "bsc" | "binance" => Ok(Chain::BSC),
        "arbitrum" | "arb" => Ok(Chain::Arbitrum),
        "optimism" | "op" => Ok(Chain::Optimism),
        "base" => Ok(Chain::Base),
        "avalanche" | "avax" => Ok(Chain::Avalanche),
        "solana" | "sol" => Ok(Chain::Solana),
        _ => Err(AppError::BadRequest(format!("Unsupported chain: {}", chain_str))),
    }
}
