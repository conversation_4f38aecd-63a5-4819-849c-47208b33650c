//! Authentication and authorization service
//!
//! COMPONENT_ESSENCE::CERBERUS_SECURITY_GUARDIAN
//! DANGER_LEVEL::MEDUSA_SECURITY_CRITICAL
//! PERFORMANCE_TARGET::SUB_10MS_TOKEN_VALIDATION
//! LAST_MODIFIED::PHASE_4_API_LAYER_MIGRATION
//! DEPENDENCIES::JSONWEBTOKEN_BCRYPT_SECURITY_STACK
//!
//! This module provides comprehensive authentication and authorization:
//! - JWT token generation and validation
//! - Password hashing and verification
//! - User session management
//! - Role-based access control
//! - API key management

use anyhow::Result;
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Valida<PERSON>};
use serde::{Deserialize, Serialize};
use shared_utils::{Web3GamingError, semantic_protection::*};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// JWT claims structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JwtClaims {
    pub sub: String,        // Subject (user ID)
    pub email: Option<String>,
    pub role: UserRole,
    pub exp: i64,           // Expiration time
    pub iat: i64,           // Issued at
    pub jti: String,        // JWT ID
}

/// User roles for authorization
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum UserRole {
    Admin,
    Developer,
    Analyst,
    User,
    ReadOnly,
}

impl UserRole {
    /// Check if this role has permission for a given action
    pub fn has_permission(&self, permission: &Permission) -> bool {
        match (self, permission) {
            (UserRole::Admin, _) => true,
            (UserRole::Developer, Permission::ReadAnalytics | Permission::WriteAnalytics | Permission::ReadContracts) => true,
            (UserRole::Analyst, Permission::ReadAnalytics | Permission::ReadContracts) => true,
            (UserRole::User, Permission::ReadAnalytics) => true,
            (UserRole::ReadOnly, Permission::ReadAnalytics) => true,
            _ => false,
        }
    }
}

/// Permissions for fine-grained access control
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Permission {
    ReadAnalytics,
    WriteAnalytics,
    ReadContracts,
    WriteContracts,
    ManageUsers,
    SystemAdmin,
}

/// User information stored in the system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub email: String,
    pub password_hash: String,
    pub role: UserRole,
    pub created_at: chrono::DateTime<Utc>,
    pub last_login: Option<chrono::DateTime<Utc>>,
    pub is_active: bool,
    pub api_key: Option<String>,
}

/// Session information for active users
#[derive(Debug, Clone)]
pub struct UserSession {
    pub user_id: String,
    pub token_id: String,
    pub created_at: chrono::DateTime<Utc>,
    pub expires_at: chrono::DateTime<Utc>,
    pub last_activity: chrono::DateTime<Utc>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

/// Authentication service for managing users and sessions
///
/// COMPONENT_ESSENCE::CERBERUS_AUTHENTICATION_ENGINE
/// DANGER_LEVEL::TITAN_SECURITY_FOUNDATION
pub struct AuthService {
    jwt_secret: String,
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    users: Arc<RwLock<HashMap<String, User>>>,
    sessions: Arc<RwLock<HashMap<String, UserSession>>>,
    token_expiry_hours: i64,
    semantic_protection: SemanticProtection,
}

impl AuthService {
    /// Create a new authentication service
    ///
    /// FUNCTION_ESSENCE::CERBERUS_FORGE_GUARDIAN
    /// DANGER_LEVEL::MEDUSA_SECRET_KEY_CRITICAL
    pub fn new(jwt_secret: String) -> Result<Self> {
        let encoding_key = EncodingKey::from_secret(jwt_secret.as_ref());
        let decoding_key = DecodingKey::from_secret(jwt_secret.as_ref());

        // Create semantic protection for authentication operations
        let semantic_protection = SemanticProtection::new_critical(vec![
            SemanticBase::Artemis, // Boundary protection for security
            SemanticBase::Athena,  // Wisdom for authentication decisions
            SemanticBase::Apollo,  // Precision in token validation
        ]);

        Ok(Self {
            jwt_secret,
            encoding_key,
            decoding_key,
            users: Arc::new(RwLock::new(HashMap::new())),
            sessions: Arc::new(RwLock::new(HashMap::new())),
            token_expiry_hours: 24,
            semantic_protection,
        })
    }

    /// Register a new user
    ///
    /// FUNCTION_ESSENCE::APOLLO_USER_CREATION
    /// DANGER_LEVEL::SIREN_PASSWORD_SECURITY
    pub async fn register(&self, email: &str, password: &str) -> Result<String> {
        debug!("Registering new user: {}", email);

        // Check if user already exists
        {
            let users = self.users.read().await;
            if users.values().any(|u| u.email == email) {
                return Err(Web3GamingError::authentication("User already exists").into());
            }
        }

        // Hash the password
        let password_hash = hash(password, DEFAULT_COST)
            .map_err(|e| Web3GamingError::authentication(format!("Password hashing failed: {}", e)))?;

        // Create new user
        let user_id = Uuid::new_v4().to_string();
        let user = User {
            id: user_id.clone(),
            email: email.to_string(),
            password_hash,
            role: UserRole::User,
            created_at: Utc::now(),
            last_login: None,
            is_active: true,
            api_key: None,
        };

        // Store user
        {
            let mut users = self.users.write().await;
            users.insert(user_id.clone(), user);
        }

        info!("User registered successfully: {}", email);
        Ok(user_id)
    }

    /// Authenticate a user and return a JWT token
    ///
    /// FUNCTION_ESSENCE::CERBERUS_AUTHENTICATION_GATE
    /// DANGER_LEVEL::MEDUSA_CREDENTIAL_VALIDATION
    pub async fn authenticate(&self, email: &str, password: &str) -> Result<String> {
        debug!("Authentication attempt for user: {}", email);

        // Semantic validation of authentication inputs
        self.semantic_protection.validate_semantic_integrity(&email, "user_email")
            .map_err(|e| Web3GamingError::authentication(format!("Email semantic validation failed: {}", e)))?;

        self.semantic_protection.validate_semantic_integrity(&password, "user_password")
            .map_err(|e| Web3GamingError::authentication(format!("Password semantic validation failed: {}", e)))?;

        // Find user by email
        let user = {
            let users = self.users.read().await;
            users.values()
                .find(|u| u.email == email && u.is_active)
                .cloned()
                .ok_or_else(|| Web3GamingError::authentication("Invalid credentials"))?
        };

        // Verify password
        if !verify(password, &user.password_hash)
            .map_err(|e| Web3GamingError::authentication(format!("Password verification failed: {}", e)))? {
            warn!("Failed login attempt for user: {}", email);
            return Err(Web3GamingError::authentication("Invalid credentials").into());
        }

        // Update last login
        {
            let mut users = self.users.write().await;
            if let Some(user) = users.get_mut(&user.id) {
                user.last_login = Some(Utc::now());
            }
        }

        // Generate JWT token
        let token = self.generate_token(&user).await?;

        info!("User authenticated successfully: {}", email);
        Ok(token)
    }

    /// Generate a JWT token for a user
    ///
    /// FUNCTION_ESSENCE::HERMES_TOKEN_MESSENGER
    /// DANGER_LEVEL::APOLLO_CRYPTOGRAPHIC_PRECISION
    async fn generate_token(&self, user: &User) -> Result<String> {
        let now = Utc::now();
        let expiration = now + Duration::hours(self.token_expiry_hours);
        let token_id = Uuid::new_v4().to_string();

        let claims = JwtClaims {
            sub: user.id.clone(),
            email: Some(user.email.clone()),
            role: user.role.clone(),
            exp: expiration.timestamp(),
            iat: now.timestamp(),
            jti: token_id.clone(),
        };

        // Create session
        let session = UserSession {
            user_id: user.id.clone(),
            token_id: token_id.clone(),
            created_at: now,
            expires_at: expiration,
            last_activity: now,
            ip_address: None,
            user_agent: None,
        };

        // Store session
        {
            let mut sessions = self.sessions.write().await;
            sessions.insert(token_id, session);
        }

        // Generate token
        let token = encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(|e| Web3GamingError::authentication(format!("Token generation failed: {}", e)))?;

        Ok(token)
    }

    /// Validate a JWT token and return claims
    ///
    /// FUNCTION_ESSENCE::CERBERUS_TOKEN_VALIDATION
    /// DANGER_LEVEL::MEDUSA_SECURITY_VERIFICATION
    pub async fn validate_token(&self, token: &str) -> Result<JwtClaims> {
        // Semantic validation of token input
        self.semantic_protection.validate_semantic_integrity(&token, "jwt_token")
            .map_err(|e| Web3GamingError::authentication(format!("Token semantic validation failed: {}", e)))?;

        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<JwtClaims>(token, &self.decoding_key, &validation)
            .map_err(|e| Web3GamingError::authentication(format!("Token validation failed: {}", e)))?;

        let claims = token_data.claims;

        // Check if session exists and is valid
        {
            let sessions = self.sessions.read().await;
            if let Some(session) = sessions.get(&claims.jti) {
                if session.expires_at < Utc::now() {
                    return Err(Web3GamingError::authentication("Token expired").into());
                }
            } else {
                return Err(Web3GamingError::authentication("Session not found").into());
            }
        }

        // Update session activity
        {
            let mut sessions = self.sessions.write().await;
            if let Some(session) = sessions.get_mut(&claims.jti) {
                session.last_activity = Utc::now();
            }
        }

        Ok(claims)
    }

    /// Refresh a JWT token
    ///
    /// FUNCTION_ESSENCE::APOLLO_TOKEN_RENEWAL
    /// DANGER_LEVEL::HERMES_SESSION_CONTINUITY
    pub async fn refresh_token(&self, old_token: &str) -> Result<String> {
        let claims = self.validate_token(old_token).await?;

        // Get user information
        let user = {
            let users = self.users.read().await;
            users.get(&claims.sub)
                .cloned()
                .ok_or_else(|| Web3GamingError::authentication("User not found"))?
        };

        // Invalidate old session
        {
            let mut sessions = self.sessions.write().await;
            sessions.remove(&claims.jti);
        }

        // Generate new token
        self.generate_token(&user).await
    }

    /// Logout a user by invalidating their session
    ///
    /// FUNCTION_ESSENCE::HERMES_SESSION_TERMINATION
    /// DANGER_LEVEL::ATHENA_SECURITY_CLEANUP
    pub async fn logout(&self, token: &str) -> Result<()> {
        let claims = self.validate_token(token).await?;

        // Remove session
        {
            let mut sessions = self.sessions.write().await;
            sessions.remove(&claims.jti);
        }

        info!("User logged out: {}", claims.sub);
        Ok(())
    }

    /// Generate an API key for a user
    ///
    /// FUNCTION_ESSENCE::APOLLO_API_KEY_FORGE
    /// DANGER_LEVEL::TITAN_PERMANENT_ACCESS_TOKEN
    pub async fn generate_api_key(&self, user_id: &str) -> Result<String> {
        let api_key = format!("wg3_{}", Uuid::new_v4().to_string().replace('-', ""));

        // Update user with API key
        {
            let mut users = self.users.write().await;
            if let Some(user) = users.get_mut(user_id) {
                user.api_key = Some(api_key.clone());
            } else {
                return Err(Web3GamingError::authentication("User not found").into());
            }
        }

        info!("API key generated for user: {}", user_id);
        Ok(api_key)
    }

    /// Validate an API key
    ///
    /// FUNCTION_ESSENCE::CERBERUS_API_KEY_GUARDIAN
    /// DANGER_LEVEL::MEDUSA_PERMANENT_TOKEN_VALIDATION
    pub async fn validate_api_key(&self, api_key: &str) -> Result<User> {
        let users = self.users.read().await;

        let user = users.values()
            .find(|u| u.api_key.as_ref() == Some(&api_key.to_string()) && u.is_active)
            .cloned()
            .ok_or_else(|| Web3GamingError::authentication("Invalid API key"))?;

        Ok(user)
    }

    /// Get user by ID
    ///
    /// FUNCTION_ESSENCE::APOLLO_USER_RETRIEVAL
    pub async fn get_user(&self, user_id: &str) -> Result<Option<User>> {
        let users = self.users.read().await;
        Ok(users.get(user_id).cloned())
    }

    /// Update user role (admin only)
    ///
    /// FUNCTION_ESSENCE::ZEUS_ROLE_ASSIGNMENT
    /// DANGER_LEVEL::TITAN_PRIVILEGE_ESCALATION
    pub async fn update_user_role(&self, user_id: &str, new_role: UserRole) -> Result<()> {
        let mut users = self.users.write().await;

        if let Some(user) = users.get_mut(user_id) {
            user.role = new_role;
            info!("User role updated: {} -> {:?}", user_id, user.role);
            Ok(())
        } else {
            Err(Web3GamingError::authentication("User not found").into())
        }
    }

    /// Clean up expired sessions
    ///
    /// FUNCTION_ESSENCE::ATHENA_SESSION_CLEANUP
    /// DANGER_LEVEL::HERMES_MEMORY_MANAGEMENT
    pub async fn cleanup_expired_sessions(&self) -> usize {
        let now = Utc::now();
        let mut sessions = self.sessions.write().await;

        let initial_count = sessions.len();
        sessions.retain(|_, session| session.expires_at > now);
        let removed_count = initial_count - sessions.len();

        if removed_count > 0 {
            debug!("Cleaned up {} expired sessions", removed_count);
        }

        removed_count
    }

    /// Get active session count
    pub async fn get_active_session_count(&self) -> usize {
        let sessions = self.sessions.read().await;
        sessions.len()
    }

    /// Get user count
    pub async fn get_user_count(&self) -> usize {
        let users = self.users.read().await;
        users.len()
    }
}
