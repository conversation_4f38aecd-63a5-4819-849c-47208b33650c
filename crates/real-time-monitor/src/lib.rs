//! Real-time monitoring system for Web3 Gaming Intelligence Platform
//!
//! COMPONENT_ESSENCE::HERMES_REAL_TIME_MESSENGER
//! DANGER_LEVEL::ARES_COMMUNICATION_URGENCY
//! PERFORMANCE_TARGET::SUB_100MS_EVENT_DELIVERY
//! LAST_MODIFIED::SEMANTIC_PROTECTION_INTEGRATION
//!
//! This crate provides:
//! - WebSocket server infrastructure with semantic protection
//! - Real-time event streaming with communication guards
//! - Connection management with boundary protection
//! - Memory-efficient broadcasting with integrity validation

pub mod websocket;
pub mod streaming;
pub mod connections;
pub mod broadcast;
pub mod integration;
pub mod memory;
pub mod metrics;

pub use websocket::*;
pub use streaming::*;
pub use connections::*;
pub use broadcast::*;
pub use integration::*;

/// Placeholder metrics registry for API server
#[derive(Debug, Clone)]
pub struct MetricsRegistry {
    // Placeholder implementation
}

impl MetricsRegistry {
    pub fn new() -> Self {
        Self {}
    }

    pub fn render(&self) -> String {
        "# Placeholder metrics\n".to_string()
    }
}
pub use memory::*;
pub use metrics::*;
