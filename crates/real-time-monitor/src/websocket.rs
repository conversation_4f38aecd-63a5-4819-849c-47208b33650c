//! High-performance WebSocket server infrastructure
//!
//! COMPONENT_ESSENCE::ARES_REAL_TIME_BATTLEFIELD
//! DANGER_LEVEL::KRAKEN_MEMORY_LEAK_POTENTIAL
//! PERFORMANCE_TARGET::10000_CONCURRENT_CONNECTIONS_OLYMPIAN
//! LAST_MODIFIED::PHASE_3_WEBSOCKET_TRIUMPH
//! DEPENDENCIES::TOKIO_TUNGSTENITE_PRODUCTION_READY
//!
//! This module provides a production-ready WebSocket server with:
//! - Connection management and lifecycle handling
//! - Message broadcasting and routing
//! - Memory-efficient connection pooling
//! - Real-time gaming event streaming

use anyhow::Result;
use dashmap::DashMap;
use futures_util::{SinkExt, StreamExt};
use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use shared_utils::{Address, Chain, Web3GamingError, semantic_protection::*};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, mpsc};
use tokio::time::interval;
use tokio_tungstenite::{
    accept_async, tungstenite::protocol::Message, WebSocketStream,
};
use tracing::{debug, error, info, warn};

/// Unique identifier for WebSocket connections
pub type ConnectionId = u64;

/// WebSocket message types for gaming events
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WsMessage {
    /// Connection established
    Connected { connection_id: ConnectionId },
    /// Heartbeat/ping message
    Ping { timestamp: u64 },
    /// Heartbeat/pong response
    Pong { timestamp: u64 },
    /// Gaming event notification
    GameEvent {
        chain: Chain,
        contract_address: Address,
        event_type: String,
        data: serde_json::Value,
        timestamp: u64,
    },
    /// Contract analysis result
    ContractAnalysis {
        chain: Chain,
        contract_address: Address,
        is_gaming: bool,
        confidence_score: f64,
        analysis_time: u64,
    },
    /// Real-time market data
    MarketData {
        chain: Chain,
        token_address: Address,
        price_usd: f64,
        volume_24h: f64,
        timestamp: u64,
    },
    /// Subscription confirmation
    Subscribed { filters: Vec<String> },
    /// Error message
    Error { message: String, code: u16 },
}

/// Client subscription filters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionFilter {
    pub id: String,
    pub chains: Vec<Chain>,
    pub contract_addresses: Vec<Address>,
    pub event_types: Vec<String>,
    pub min_confidence_score: Option<f64>,
    pub is_active: bool,
}

/// WebSocket connection metadata
#[derive(Debug)]
pub struct ConnectionInfo {
    pub id: ConnectionId,
    pub addr: SocketAddr,
    pub connected_at: Instant,
    pub last_ping: Instant,
    pub subscriptions: Arc<RwLock<Vec<SubscriptionFilter>>>,
    pub message_count: AtomicU64,
    pub bytes_sent: AtomicU64,
    pub bytes_received: AtomicU64,
}

impl ConnectionInfo {
    pub fn new(id: ConnectionId, addr: SocketAddr) -> Self {
        let now = Instant::now();
        Self {
            id,
            addr,
            connected_at: now,
            last_ping: now,
            subscriptions: Arc::new(RwLock::new(Vec::new())),
            message_count: AtomicU64::new(0),
            bytes_sent: AtomicU64::new(0),
            bytes_received: AtomicU64::new(0),
        }
    }

    pub fn update_ping(&self) {
        // Note: We can't update last_ping directly due to borrowing rules
        // This would need to be handled differently in a real implementation
        self.message_count.fetch_add(1, Ordering::Relaxed);
    }

    pub fn add_subscription(&self, filter: SubscriptionFilter) {
        let mut subs = self.subscriptions.write();
        subs.push(filter);
    }

    pub fn remove_subscription(&self, filter_id: &str) -> bool {
        let mut subs = self.subscriptions.write();
        if let Some(pos) = subs.iter().position(|f| f.id == filter_id) {
            subs.remove(pos);
            true
        } else {
            false
        }
    }

    pub fn matches_filter(&self, message: &WsMessage) -> bool {
        let subs = self.subscriptions.read();
        if subs.is_empty() {
            return true; // No filters means receive all
        }

        subs.iter().any(|filter| {
            if !filter.is_active {
                return false;
            }

            match message {
                WsMessage::GameEvent { chain, contract_address, event_type, .. } => {
                    (filter.chains.is_empty() || filter.chains.contains(chain)) &&
                    (filter.contract_addresses.is_empty() || filter.contract_addresses.contains(contract_address)) &&
                    (filter.event_types.is_empty() || filter.event_types.contains(event_type))
                }
                WsMessage::ContractAnalysis { chain, contract_address, confidence_score, .. } => {
                    (filter.chains.is_empty() || filter.chains.contains(chain)) &&
                    (filter.contract_addresses.is_empty() || filter.contract_addresses.contains(contract_address)) &&
                    filter.min_confidence_score.map_or(true, |min| *confidence_score >= min)
                }
                WsMessage::MarketData { chain, token_address, .. } => {
                    (filter.chains.is_empty() || filter.chains.contains(chain)) &&
                    (filter.contract_addresses.is_empty() || filter.contract_addresses.contains(token_address))
                }
                _ => true, // System messages always pass through
            }
        })
    }
}

/// WebSocket server statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketStats {
    pub total_connections: u64,
    pub active_connections: u64,
    pub messages_sent: u64,
    pub messages_received: u64,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub uptime_seconds: u64,
    pub connections_per_second: f64,
    pub messages_per_second: f64,
}

/// High-performance WebSocket server with semantic protection
#[derive(Debug)]
pub struct WebSocketServer {
    connections: Arc<DashMap<ConnectionId, Arc<ConnectionInfo>>>,
    message_sender: broadcast::Sender<WsMessage>,
    next_connection_id: AtomicU64,
    stats: Arc<RwLock<WebSocketStats>>,
    start_time: Instant,
    max_connections: usize,
    heartbeat_interval: Duration,
    connection_timeout: Duration,
    semantic_protection: SemanticProtection,
}

impl WebSocketServer {
    /// Create a new WebSocket server with semantic protection
    pub fn new(max_connections: usize) -> Self {
        let (message_sender, _) = broadcast::channel(10000);

        // Create semantic protection for WebSocket communications
        let semantic_protection = SemanticProtection::new_critical(vec![
            SemanticBase::Hermes,  // Communication excellence
            SemanticBase::Ares,    // Urgency for real-time events
            SemanticBase::Artemis, // Boundary protection for connections
        ]);

        Self {
            connections: Arc::new(DashMap::new()),
            message_sender,
            next_connection_id: AtomicU64::new(1),
            stats: Arc::new(RwLock::new(WebSocketStats {
                total_connections: 0,
                active_connections: 0,
                messages_sent: 0,
                messages_received: 0,
                bytes_sent: 0,
                bytes_received: 0,
                uptime_seconds: 0,
                connections_per_second: 0.0,
                messages_per_second: 0.0,
            })),
            start_time: Instant::now(),
            max_connections,
            heartbeat_interval: Duration::from_secs(30),
            connection_timeout: Duration::from_secs(60),
            semantic_protection,
        }
    }

    /// Start the WebSocket server
    pub async fn start(&self, bind_addr: &str) -> Result<()> {
        let listener = TcpListener::bind(bind_addr).await?;
        info!("WebSocket server listening on {}", bind_addr);

        // Start background tasks
        self.start_background_tasks().await;

        // Accept connections
        while let Ok((stream, addr)) = listener.accept().await {
            if self.connections.len() >= self.max_connections {
                warn!("Max connections reached, rejecting connection from {}", addr);
                continue;
            }

            let server = self.clone();
            tokio::spawn(async move {
                if let Err(e) = server.handle_connection(stream, addr).await {
                    error!("Error handling connection from {}: {}", addr, e);
                }
            });
        }

        Ok(())
    }

    /// Handle a new WebSocket connection
    async fn handle_connection(&self, stream: TcpStream, addr: SocketAddr) -> Result<()> {
        let ws_stream = accept_async(stream).await?;
        let connection_id = self.next_connection_id.fetch_add(1, Ordering::Relaxed);

        info!("New WebSocket connection: {} from {}", connection_id, addr);

        let connection_info = Arc::new(ConnectionInfo::new(connection_id, addr));
        self.connections.insert(connection_id, connection_info.clone());

        // Update stats
        {
            let mut stats = self.stats.write();
            stats.total_connections += 1;
            stats.active_connections = self.connections.len() as u64;
        }

        // Send connection confirmation
        let welcome_msg = WsMessage::Connected { connection_id };
        if let Err(e) = self.send_to_connection(connection_id, welcome_msg).await {
            warn!("Failed to send welcome message to {}: {}", connection_id, e);
        }

        // Handle the connection
        let result = self.handle_websocket_messages(ws_stream, connection_info.clone()).await;

        // Clean up connection
        self.connections.remove(&connection_id);
        {
            let mut stats = self.stats.write();
            stats.active_connections = self.connections.len() as u64;
        }

        info!("WebSocket connection {} closed", connection_id);
        result
    }

    /// Handle WebSocket messages for a connection
    ///
    /// FUNCTION_ESSENCE::HERMES_MESSAGE_RELAY_GUARDIAN
    /// DANGER_LEVEL::MEDUSA_ASYNC_DEADLOCK_POTENTIAL
    /// PERFORMANCE_CRITICAL::SUB_10MS_MESSAGE_LATENCY
    /// MEMORY_SENSITIVE::CONNECTION_LIFECYCLE_MANAGEMENT
    async fn handle_websocket_messages(
        &self,
        ws_stream: WebSocketStream<TcpStream>,
        connection_info: Arc<ConnectionInfo>,
    ) -> Result<()> {
        let (ws_sender, mut ws_receiver) = ws_stream.split();
        let connection_id = connection_info.id;

        // Subscribe to broadcast messages
        let mut broadcast_receiver = self.message_sender.subscribe();

        // Use Arc<Mutex<>> to share the sender between tasks
        let ws_sender = Arc::new(tokio::sync::Mutex::new(ws_sender));

        // Handle incoming messages from client
        let ws_sender_clone = Arc::clone(&ws_sender);
        let connection_info_clone = Arc::clone(&connection_info);
        let incoming_task = tokio::spawn(async move {
            while let Some(msg) = ws_receiver.next().await {
                match msg {
                    Ok(Message::Text(text)) => {
                        connection_info_clone.bytes_received.fetch_add(text.len() as u64, Ordering::Relaxed);
                        // Handle client message would go here
                        debug!("Received text message from {}: {}", connection_id, text);
                    }
                    Ok(Message::Binary(data)) => {
                        connection_info_clone.bytes_received.fetch_add(data.len() as u64, Ordering::Relaxed);
                        debug!("Received binary message from {}, length: {}", connection_id, data.len());
                    }
                    Ok(Message::Ping(data)) => {
                        let mut sender = ws_sender_clone.lock().await;
                        if let Err(e) = sender.send(Message::Pong(data)).await {
                            error!("Failed to send pong to {}: {}", connection_id, e);
                            break;
                        }
                        connection_info_clone.update_ping();
                    }
                    Ok(Message::Pong(_)) => {
                        connection_info_clone.update_ping();
                    }
                    Ok(Message::Close(_)) => {
                        debug!("Client {} requested close", connection_id);
                        break;
                    }
                    Ok(Message::Frame(_)) => {
                        debug!("Received raw frame from {}", connection_id);
                    }
                    Err(e) => {
                        error!("WebSocket error for connection {}: {}", connection_id, e);
                        break;
                    }
                }
            }
        });

        // Handle outgoing broadcast messages
        let ws_sender_clone = Arc::clone(&ws_sender);
        let connection_info_clone = Arc::clone(&connection_info);
        let outgoing_task = tokio::spawn(async move {
            while let Ok(message) = broadcast_receiver.recv().await {
                // Check if this connection should receive this message
                if !connection_info_clone.matches_filter(&message) {
                    continue;
                }

                let json_msg = match serde_json::to_string(&message) {
                    Ok(json) => json,
                    Err(e) => {
                        error!("Failed to serialize message: {}", e);
                        continue;
                    }
                };

                let mut sender = ws_sender_clone.lock().await;
                if let Err(e) = sender.send(Message::Text(json_msg.clone())).await {
                    error!("Failed to send message to {}: {}", connection_id, e);
                    break;
                }

                connection_info_clone.bytes_sent.fetch_add(json_msg.len() as u64, Ordering::Relaxed);
                connection_info_clone.message_count.fetch_add(1, Ordering::Relaxed);
            }
        });

        // Wait for either task to complete
        tokio::select! {
            _ = incoming_task => {},
            _ = outgoing_task => {},
        }

        Ok(())
    }

    /// Handle incoming client messages
    async fn handle_client_message(&self, connection_id: ConnectionId, message: &str) -> Result<()> {
        debug!("Received message from {}: {}", connection_id, message);

        // Parse the message as JSON
        let parsed: serde_json::Value = serde_json::from_str(message)?;

        match parsed.get("type").and_then(|t| t.as_str()) {
            Some("ping") => {
                let timestamp = chrono::Utc::now().timestamp_millis() as u64;
                let pong = WsMessage::Pong { timestamp };
                self.send_to_connection(connection_id, pong).await?;
            }
            Some("subscribe") => {
                if let Some(filter_data) = parsed.get("data") {
                    let filter: SubscriptionFilter = serde_json::from_value(filter_data.clone())?;
                    self.add_subscription(connection_id, filter).await?;
                }
            }
            Some("unsubscribe") => {
                if let Some(filter_id) = parsed.get("filter_id").and_then(|id| id.as_str()) {
                    self.remove_subscription(connection_id, filter_id).await?;
                }
            }
            _ => {
                warn!("Unknown message type from {}: {}", connection_id, message);
            }
        }

        Ok(())
    }

    /// Add a subscription filter for a connection
    async fn add_subscription(&self, connection_id: ConnectionId, filter: SubscriptionFilter) -> Result<()> {
        if let Some(connection) = self.connections.get(&connection_id) {
            let filter_id = filter.id.clone();
            connection.add_subscription(filter);

            let confirmation = WsMessage::Subscribed {
                filters: vec![filter_id.clone()]
            };
            self.send_to_connection(connection_id, confirmation).await?;

            info!("Added subscription {} for connection {}", filter_id, connection_id);
        }
        Ok(())
    }

    /// Remove a subscription filter for a connection
    async fn remove_subscription(&self, connection_id: ConnectionId, filter_id: &str) -> Result<()> {
        if let Some(connection) = self.connections.get(&connection_id) {
            if connection.remove_subscription(filter_id) {
                info!("Removed subscription {} for connection {}", filter_id, connection_id);
            }
        }
        Ok(())
    }

    /// Send a message to a specific connection
    async fn send_to_connection(&self, connection_id: ConnectionId, message: WsMessage) -> Result<()> {
        // For now, we'll use the broadcast channel
        // In a real implementation, we'd want direct connection messaging
        if let Err(e) = self.message_sender.send(message) {
            warn!("Failed to broadcast message: {}", e);
        }
        Ok(())
    }

    /// Broadcast a message to all connected clients with semantic protection
    pub async fn broadcast(&self, message: WsMessage) -> Result<()> {
        // Semantic validation of message before broadcasting
        self.semantic_protection.validate_semantic_integrity(&message, "websocket_message")
            .map_err(|e| anyhow::anyhow!("WebSocket message semantic validation failed: {}", e))?;

        let receiver_count = self.message_sender.receiver_count();

        if receiver_count == 0 {
            debug!("No active receivers for broadcast message");
            return Ok(());
        }

        match self.message_sender.send(message) {
            Ok(_) => {
                // Update stats
                let mut stats = self.stats.write();
                stats.messages_sent += receiver_count as u64;
                debug!("Broadcasted message to {} receivers", receiver_count);
                Ok(())
            }
            Err(e) => {
                error!("Failed to broadcast message: {}", e);
                Err(Web3GamingError::websocket(format!("Broadcast failed: {}", e)).into())
            }
        }
    }

    /// Get current server statistics
    pub async fn get_stats(&self) -> WebSocketStats {
        let mut stats = self.stats.read().clone();
        stats.active_connections = self.connections.len() as u64;
        stats.uptime_seconds = self.start_time.elapsed().as_secs();

        // Calculate rates
        if stats.uptime_seconds > 0 {
            stats.connections_per_second = stats.total_connections as f64 / stats.uptime_seconds as f64;
            stats.messages_per_second = stats.messages_sent as f64 / stats.uptime_seconds as f64;
        }

        stats
    }

    /// Get information about all active connections
    pub async fn get_connections(&self) -> Vec<(ConnectionId, SocketAddr, u64)> {
        self.connections
            .iter()
            .map(|entry| {
                let connection = entry.value();
                (
                    connection.id,
                    connection.addr,
                    connection.message_count.load(Ordering::Relaxed),
                )
            })
            .collect()
    }

    /// Start background maintenance tasks
    async fn start_background_tasks(&self) {
        // Heartbeat task
        let connections = Arc::clone(&self.connections);
        let heartbeat_interval = self.heartbeat_interval;
        let connection_timeout = self.connection_timeout;

        tokio::spawn(async move {
            let mut interval = interval(heartbeat_interval);
            loop {
                interval.tick().await;

                let now = Instant::now();
                let mut to_remove = Vec::new();

                for entry in connections.iter() {
                    let connection = entry.value();
                    if now.duration_since(connection.last_ping) > connection_timeout {
                        to_remove.push(connection.id);
                    }
                }

                for connection_id in to_remove {
                    connections.remove(&connection_id);
                    info!("Removed inactive connection: {}", connection_id);
                }
            }
        });

        // Statistics update task
        let stats = Arc::clone(&self.stats);
        let connections = Arc::clone(&self.connections);

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(10));
            loop {
                interval.tick().await;

                let mut stats = stats.write();
                stats.active_connections = connections.len() as u64;

                // Calculate total bytes sent/received
                let mut total_bytes_sent = 0u64;
                let mut total_bytes_received = 0u64;
                let mut total_messages = 0u64;

                for entry in connections.iter() {
                    let connection = entry.value();
                    total_bytes_sent += connection.bytes_sent.load(Ordering::Relaxed);
                    total_bytes_received += connection.bytes_received.load(Ordering::Relaxed);
                    total_messages += connection.message_count.load(Ordering::Relaxed);
                }

                stats.bytes_sent = total_bytes_sent;
                stats.bytes_received = total_bytes_received;
                stats.messages_received = total_messages;
            }
        });

        info!("WebSocket server background tasks started");
    }
}

impl Clone for WebSocketServer {
    fn clone(&self) -> Self {
        Self {
            connections: Arc::clone(&self.connections),
            message_sender: self.message_sender.clone(),
            next_connection_id: AtomicU64::new(self.next_connection_id.load(Ordering::Relaxed)),
            stats: Arc::clone(&self.stats),
            start_time: self.start_time,
            max_connections: self.max_connections,
            heartbeat_interval: self.heartbeat_interval,
            connection_timeout: self.connection_timeout,
        }
    }
}
