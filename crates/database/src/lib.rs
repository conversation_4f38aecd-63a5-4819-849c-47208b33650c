//! High-Performance Database Layer for Web3 Gaming Intelligence Platform
//! 
//! COMPONENT_ESSENCE::APOLLO_DATA_PRECISION_TEMPLE
//! DANGER_LEVEL::TITAN_DATA_INTEGRITY_CRITICAL
//! PERFORMANCE_TARGET::SUB_1MS_QUERY_EXECUTION
//! LAST_MODIFIED::HYBRID_ARCHITECTURE_IMPLEMENTATION
//! DEPENDENCIES::SQLX_ASYNC_POSTGRES_REDIS
//! 
//! This module provides a high-performance database abstraction layer
//! optimized for Web3 gaming analytics with connection pooling,
//! prepared statements, and intelligent caching strategies.

pub mod connection;
pub mod models;
pub mod queries;
pub mod migrations;
pub mod cache;
pub mod analytics;

pub use connection::*;
pub use models::*;
pub use queries::*;
pub use cache::*;
pub use analytics::*;

use shared_utils::{Web3GamingError, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorArchetype, semantic_protection::*};
use sqlx::{PgPool, Row};
use std::sync::Arc;
use tokio::sync::RwLock;

/// High-performance database manager with intelligent caching
/// 
/// COMPONENT_ESSENCE::APOLLO_DATABASE_ORCHESTRATOR
/// DANGER_LEVEL::TITAN_DATA_CONSISTENCY_GUARDIAN
/// PERFORMANCE_TARGET::10000_CONCURRENT_CONNECTIONS
#[derive(Clone)]
pub struct DatabaseManager {
    /// Primary PostgreSQL connection pool
    pub pg_pool: PgPool,

    /// Redis connection pool for caching
    pub redis_pool: Arc<redis::aio::ConnectionManager>,

    /// Query cache for prepared statements
    query_cache: Arc<RwLock<std::collections::HashMap<String, String>>>,

    /// Performance metrics collector
    metrics: Arc<DatabaseMetrics>,

    /// Semantic protection for database operations
    semantic_protection: SemanticProtection,
}

/// Database performance metrics for monitoring
/// 
/// COMPONENT_ESSENCE::PROMETHEUS_DATABASE_FIRE
/// DANGER_LEVEL::APOLLO_METRICS_PRECISION
#[derive(Debug, Default)]
pub struct DatabaseMetrics {
    /// Total queries executed
    pub total_queries: std::sync::atomic::AtomicU64,
    
    /// Cache hit ratio
    pub cache_hits: std::sync::atomic::AtomicU64,
    
    /// Cache misses
    pub cache_misses: std::sync::atomic::AtomicU64,
    
    /// Average query execution time
    pub avg_query_time_ms: std::sync::atomic::AtomicU64,
    
    /// Active connections
    pub active_connections: std::sync::atomic::AtomicU32,
}

impl DatabaseManager {
    /// Create a new database manager with optimized configuration
    /// 
    /// FUNCTION_ESSENCE::APOLLO_DATABASE_TEMPLE_CREATION
    /// DANGER_LEVEL::TITAN_INITIALIZATION_CRITICAL
    pub async fn new(database_url: &str, redis_url: &str) -> Result<Self, Web3GamingError> {
        // Configure PostgreSQL connection pool with optimization
        let pg_pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(100) // High concurrency support
            .min_connections(10)  // Always-ready connections
            .acquire_timeout(std::time::Duration::from_secs(5))
            .idle_timeout(std::time::Duration::from_secs(600))
            .max_lifetime(std::time::Duration::from_secs(1800))
            .test_before_acquire(true)
            .connect(database_url)
            .await
            .map_err(|e| Web3GamingError::database_custom(format!("Failed to connect to PostgreSQL: {}", e)))?;

        // Configure Redis connection with optimization
        let redis_client = redis::Client::open(redis_url)
            .map_err(|e| Web3GamingError::database_custom(format!("Failed to create Redis client: {}", e)))?;
        
        let redis_pool = Arc::new(
            redis::aio::ConnectionManager::new(redis_client)
                .await
                .map_err(|e| Web3GamingError::database_custom(format!("Failed to connect to Redis: {}", e)))?
        );

        // Create semantic protection for database operations
        let semantic_protection = SemanticProtection::new_critical(vec![
            SemanticBase::Apollo,  // Precision for data integrity
            SemanticBase::Artemis, // Boundary protection for queries
            SemanticBase::Athena,  // Wisdom for data validation
        ]);

        Ok(Self {
            pg_pool,
            redis_pool,
            query_cache: Arc::new(RwLock::new(std::collections::HashMap::new())),
            metrics: Arc::new(DatabaseMetrics::default()),
            semantic_protection,
        })
    }

    /// Execute a cached query with automatic performance tracking
    /// 
    /// FUNCTION_ESSENCE::APOLLO_QUERY_PRECISION_EXECUTION
    /// DANGER_LEVEL::HERMES_QUERY_SAFETY_VALIDATION
    pub async fn execute_cached_query<T>(
        &self,
        query_key: &str,
        query: &str,
    ) -> Result<Vec<T>, Web3GamingError>
    where
        T: for<'r> sqlx::FromRow<'r, sqlx::postgres::PgRow> + Send + Unpin + serde::de::DeserializeOwned + serde::Serialize,
    {
        let start_time = std::time::Instant::now();
        
        // Increment query counter
        self.metrics.total_queries.fetch_add(1, std::sync::atomic::Ordering::Relaxed);

        // Try cache first
        if let Some(cached_result) = self.get_from_cache::<Vec<T>>(query_key).await? {
            self.metrics.cache_hits.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            return Ok(cached_result);
        }

        // Cache miss - execute query
        self.metrics.cache_misses.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        
        let results = sqlx::query_as::<_, T>(query)
            .fetch_all(&self.pg_pool)
            .await
            .map_err(|e| {
                Web3GamingError::database_custom(format!("Database query failed: {}", e))
            })?;

        // Cache the results
        self.set_cache(query_key, &results, 300).await?; // 5 minute cache

        // Update metrics
        let execution_time = start_time.elapsed().as_millis() as u64;
        self.metrics.avg_query_time_ms.store(execution_time, std::sync::atomic::Ordering::Relaxed);

        Ok(results)
    }

    /// Get data from Redis cache
    /// 
    /// FUNCTION_ESSENCE::HERMES_CACHE_RETRIEVAL_SPEED
    /// DANGER_LEVEL::APOLLO_CACHE_PRECISION
    async fn get_from_cache<T>(&self, key: &str) -> Result<Option<T>, Web3GamingError>
    where
        T: serde::de::DeserializeOwned,
    {
        use redis::AsyncCommands;
        
        let mut conn = self.redis_pool.as_ref().clone();
        
        match conn.get::<_, Option<String>>(key).await {
            Ok(Some(cached_data)) => {
                match serde_json::from_str::<T>(&cached_data) {
                    Ok(data) => Ok(Some(data)),
                    Err(_) => Ok(None), // Invalid cache data, ignore
                }
            }
            Ok(None) => Ok(None),
            Err(_) => Ok(None), // Redis error, continue without cache
        }
    }

    /// Set data in Redis cache with TTL
    /// 
    /// FUNCTION_ESSENCE::HERMES_CACHE_STORAGE_OPTIMIZATION
    /// DANGER_LEVEL::APOLLO_CACHE_CONSISTENCY
    async fn set_cache<T>(&self, key: &str, data: &T, ttl_seconds: u64) -> Result<(), Web3GamingError>
    where
        T: serde::Serialize,
    {
        use redis::AsyncCommands;
        
        let serialized = serde_json::to_string(data)
            .map_err(|e| Web3GamingError::database_custom(format!("Cache serialization failed: {}", e)))?;
        
        let mut conn = self.redis_pool.as_ref().clone();
        
        // Set with expiration - ignore errors to not break main functionality
        let _ = conn.set_ex::<_, _, ()>(key, serialized, ttl_seconds).await;
        
        Ok(())
    }

    /// Get database health status
    /// 
    /// FUNCTION_ESSENCE::APOLLO_HEALTH_ORACLE_WISDOM
    /// DANGER_LEVEL::HERMES_STATUS_REPORTING
    pub async fn health_check(&self) -> DatabaseHealth {
        let pg_healthy = sqlx::query("SELECT 1")
            .fetch_one(&self.pg_pool)
            .await
            .is_ok();

        let redis_healthy = {
            use redis::AsyncCommands;
            let mut conn = self.redis_pool.as_ref().clone();
            conn.get::<&str, Option<String>>("__health_check__").await.is_ok()
        };

        let active_connections = self.pg_pool.size() as u32;
        let cache_hit_ratio = {
            let hits = self.metrics.cache_hits.load(std::sync::atomic::Ordering::Relaxed);
            let misses = self.metrics.cache_misses.load(std::sync::atomic::Ordering::Relaxed);
            if hits + misses > 0 {
                (hits as f64 / (hits + misses) as f64) * 100.0
            } else {
                0.0
            }
        };

        DatabaseHealth {
            postgres_healthy: pg_healthy,
            redis_healthy,
            active_connections,
            cache_hit_ratio,
            total_queries: self.metrics.total_queries.load(std::sync::atomic::Ordering::Relaxed),
            avg_query_time_ms: self.metrics.avg_query_time_ms.load(std::sync::atomic::Ordering::Relaxed),
        }
    }

    /// Execute a transaction with automatic rollback on error
    /// 
    /// FUNCTION_ESSENCE::ATHENA_TRANSACTION_WISDOM_GUARDIAN
    /// DANGER_LEVEL::TITAN_DATA_CONSISTENCY_CRITICAL
    pub async fn execute_transaction<F, R>(&self, transaction_fn: F) -> Result<R, Web3GamingError>
    where
        F: for<'c> FnOnce(&mut sqlx::Transaction<'c, sqlx::Postgres>) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<R, Web3GamingError>> + Send + 'c>>,
        R: Send,
    {
        let mut tx = self.pg_pool
            .begin()
            .await
            .map_err(|e| Web3GamingError::database_custom(format!("Failed to begin transaction: {}", e)))?;

        match transaction_fn(&mut tx).await {
            Ok(result) => {
                tx.commit()
                    .await
                    .map_err(|e| Web3GamingError::database_custom(format!("Failed to commit transaction: {}", e)))?;
                Ok(result)
            }
            Err(e) => {
                let _ = tx.rollback().await; // Ignore rollback errors
                Err(e)
            }
        }
    }

    /// Get performance metrics
    /// 
    /// FUNCTION_ESSENCE::PROMETHEUS_METRICS_FIRE_COLLECTION
    /// DANGER_LEVEL::APOLLO_METRICS_PRECISION
    pub fn get_metrics(&self) -> DatabaseMetrics {
        DatabaseMetrics {
            total_queries: std::sync::atomic::AtomicU64::new(
                self.metrics.total_queries.load(std::sync::atomic::Ordering::Relaxed)
            ),
            cache_hits: std::sync::atomic::AtomicU64::new(
                self.metrics.cache_hits.load(std::sync::atomic::Ordering::Relaxed)
            ),
            cache_misses: std::sync::atomic::AtomicU64::new(
                self.metrics.cache_misses.load(std::sync::atomic::Ordering::Relaxed)
            ),
            avg_query_time_ms: std::sync::atomic::AtomicU64::new(
                self.metrics.avg_query_time_ms.load(std::sync::atomic::Ordering::Relaxed)
            ),
            active_connections: std::sync::atomic::AtomicU32::new(
                self.pg_pool.size() as u32
            ),
        }
    }
}

/// Database health status for monitoring
/// 
/// COMPONENT_ESSENCE::APOLLO_HEALTH_ORACLE
/// DANGER_LEVEL::HERMES_STATUS_TRANSPARENCY
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DatabaseHealth {
    pub postgres_healthy: bool,
    pub redis_healthy: bool,
    pub active_connections: u32,
    pub cache_hit_ratio: f64,
    pub total_queries: u64,
    pub avg_query_time_ms: u64,
}

/// Database configuration for different environments
/// 
/// COMPONENT_ESSENCE::ATHENA_CONFIGURATION_WISDOM
/// DANGER_LEVEL::APOLLO_SETTINGS_PRECISION
#[derive(Debug, Clone, serde::Deserialize)]
pub struct DatabaseConfig {
    pub postgres_url: String,
    pub redis_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout_seconds: u64,
    pub query_timeout_seconds: u64,
    pub cache_ttl_seconds: u64,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            postgres_url: "postgresql://localhost/web3gaming".to_string(),
            redis_url: "redis://localhost:6379".to_string(),
            max_connections: 100,
            min_connections: 10,
            connection_timeout_seconds: 5,
            query_timeout_seconds: 30,
            cache_ttl_seconds: 300,
        }
    }
}
