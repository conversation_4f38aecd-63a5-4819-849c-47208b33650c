"""
Moralis Web3 Data API client for comprehensive blockchain data
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass

from .base import BaseBlockchainDataClient
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class MoralisNetworkConfig:
    """Moralis network configuration"""
    name: str
    chain_id: str
    native_currency: str
    supports_nft: bool = True
    supports_defi: bool = True
    supports_gaming: bool = True


class MoralisClient(BaseBlockchainDataClient):
    """Moralis Web3 Data API client for gaming analytics"""
    
    # Network mappings for Moralis API
    NETWORK_CONFIGS = {
        "ethereum": MoralisNetworkConfig("Ethereum", "0x1", "ETH", True, True, True),
        "polygon": MoralisNetworkConfig("Polygon", "0x89", "MATIC", True, True, True),
        "bsc": MoralisNetworkConfig("BSC", "0x38", "BNB", True, True, True),
        "arbitrum": MoralisNetworkConfig("Arbitrum", "0xa4b1", "ETH", True, True, True),
        "optimism": MoralisNetworkConfig("Optimism", "0xa", "ETH", True, True, True),
        "base": MoralisNetworkConfig("Base", "0x2105", "ETH", True, True, True),
        "avalanche": MoralisNetworkConfig("Avalanche", "0xa86a", "AVAX", True, True, True),
    }
    
    def __init__(self):
        super().__init__()
        self.api_key = settings.blockchain_data.moralis_api_key
        self.base_url = settings.blockchain_data.moralis_base_url
        self.session = None
        
        if not self.api_key:
            logger.warning("Moralis API key not configured")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if not self.session:
            headers = {
                "X-API-Key": self.api_key,
                "Content-Type": "application/json",
                "User-Agent": "Web3GamingTracker/1.0"
            }
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make API request to Moralis"""
        if not self.api_key:
            logger.error("Moralis API key not configured")
            return None
        
        session = await self._get_session()
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    logger.warning("Moralis rate limit exceeded")
                    await asyncio.sleep(1)
                    return None
                else:
                    logger.error(f"Moralis API error: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Moralis request failed: {e}")
            return None
    
    # NFT Data Methods
    
    async def get_nft_metadata(self, contract_address: str, token_id: str, chain: str = "eth") -> Optional[Dict]:
        """Get NFT metadata"""
        endpoint = f"nft/{contract_address}/{token_id}"
        params = {"chain": chain, "format": "decimal"}
        return await self._make_request(endpoint, params)
    
    async def get_nft_owners(self, contract_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get NFT owners for a collection"""
        endpoint = f"nft/{contract_address}/owners"
        params = {"chain": chain, "format": "decimal"}
        return await self._make_request(endpoint, params)
    
    async def get_nft_transfers(self, contract_address: str, chain: str = "eth", 
                               from_block: int = None, to_block: int = None) -> Optional[Dict]:
        """Get NFT transfers for a collection"""
        endpoint = f"nft/{contract_address}/transfers"
        params = {"chain": chain, "format": "decimal"}
        
        if from_block:
            params["from_block"] = from_block
        if to_block:
            params["to_block"] = to_block
            
        return await self._make_request(endpoint, params)
    
    async def get_wallet_nfts(self, wallet_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get all NFTs owned by a wallet"""
        endpoint = f"{wallet_address}/nft"
        params = {"chain": chain, "format": "decimal"}
        return await self._make_request(endpoint, params)
    
    async def get_nft_collection_stats(self, contract_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get NFT collection statistics"""
        endpoint = f"nft/{contract_address}/stats"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    # Token Data Methods
    
    async def get_token_metadata(self, contract_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get ERC20 token metadata"""
        endpoint = f"erc20/metadata"
        params = {"chain": chain, "addresses": [contract_address]}
        return await self._make_request(endpoint, params)
    
    async def get_token_price(self, contract_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get token price"""
        endpoint = f"erc20/{contract_address}/price"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    async def get_wallet_token_balances(self, wallet_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get all token balances for a wallet"""
        endpoint = f"{wallet_address}/erc20"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    async def get_token_transfers(self, contract_address: str, chain: str = "eth",
                                 from_block: int = None, to_block: int = None) -> Optional[Dict]:
        """Get token transfers"""
        endpoint = f"erc20/{contract_address}/transfers"
        params = {"chain": chain}
        
        if from_block:
            params["from_block"] = from_block
        if to_block:
            params["to_block"] = to_block
            
        return await self._make_request(endpoint, params)
    
    # Transaction Data Methods
    
    async def get_wallet_transactions(self, wallet_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get wallet transaction history"""
        endpoint = f"{wallet_address}"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    async def get_transaction_details(self, transaction_hash: str, chain: str = "eth") -> Optional[Dict]:
        """Get transaction details"""
        endpoint = f"transaction/{transaction_hash}"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    async def get_block_data(self, block_number: Union[int, str], chain: str = "eth") -> Optional[Dict]:
        """Get block data"""
        endpoint = f"block/{block_number}"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    # DeFi Data Methods
    
    async def get_wallet_defi_positions(self, wallet_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get DeFi positions for a wallet"""
        endpoint = f"{wallet_address}/defi/positions"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    async def get_wallet_portfolio(self, wallet_address: str, chain: str = "eth") -> Optional[Dict]:
        """Get complete wallet portfolio"""
        endpoint = f"wallets/{wallet_address}/portfolio"
        params = {"chain": chain}
        return await self._make_request(endpoint, params)
    
    # Gaming-Specific Methods
    
    async def get_gaming_nft_activity(self, contract_addresses: List[str], 
                                     chain: str = "eth") -> Dict[str, Any]:
        """Get gaming NFT activity across multiple collections"""
        gaming_data = {
            "collections": {},
            "total_transfers": 0,
            "total_volume": 0,
            "active_players": set()
        }
        
        for contract_address in contract_addresses:
            try:
                # Get transfers
                transfers = await self.get_nft_transfers(contract_address, chain)
                if transfers and "result" in transfers:
                    collection_transfers = transfers["result"]
                    gaming_data["collections"][contract_address] = {
                        "transfers": collection_transfers,
                        "transfer_count": len(collection_transfers)
                    }
                    gaming_data["total_transfers"] += len(collection_transfers)
                    
                    # Track active players
                    for transfer in collection_transfers:
                        gaming_data["active_players"].add(transfer.get("from_address"))
                        gaming_data["active_players"].add(transfer.get("to_address"))
                
                # Get collection stats
                stats = await self.get_nft_collection_stats(contract_address, chain)
                if stats:
                    gaming_data["collections"][contract_address]["stats"] = stats
                
                await asyncio.sleep(0.1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error getting gaming data for {contract_address}: {e}")
        
        gaming_data["unique_players"] = len(gaming_data["active_players"])
        gaming_data["active_players"] = list(gaming_data["active_players"])
        
        return gaming_data
    
    async def get_gaming_token_metrics(self, token_addresses: List[str], 
                                      chain: str = "eth") -> Dict[str, Any]:
        """Get gaming token metrics"""
        token_data = {}
        
        for token_address in token_addresses:
            try:
                # Get token metadata
                metadata = await self.get_token_metadata(token_address, chain)
                
                # Get token price
                price = await self.get_token_price(token_address, chain)
                
                # Get recent transfers
                transfers = await self.get_token_transfers(token_address, chain)
                
                token_data[token_address] = {
                    "metadata": metadata,
                    "price": price,
                    "transfers": transfers
                }
                
                await asyncio.sleep(0.1)  # Rate limiting
                
            except Exception as e:
                logger.error(f"Error getting token data for {token_address}: {e}")
        
        return token_data
    
    async def analyze_gaming_wallet(self, wallet_address: str, chain: str = "eth") -> Dict[str, Any]:
        """Comprehensive gaming wallet analysis"""
        try:
            # Get all NFTs
            nfts = await self.get_wallet_nfts(wallet_address, chain)
            
            # Get all tokens
            tokens = await self.get_token_balances(wallet_address, chain)
            
            # Get transaction history
            transactions = await self.get_wallet_transactions(wallet_address, chain)
            
            # Get DeFi positions
            defi_positions = await self.get_wallet_defi_positions(wallet_address, chain)
            
            # Get portfolio overview
            portfolio = await self.get_wallet_portfolio(wallet_address, chain)
            
            return {
                "wallet_address": wallet_address,
                "chain": chain,
                "nfts": nfts,
                "tokens": tokens,
                "transactions": transactions,
                "defi_positions": defi_positions,
                "portfolio": portfolio,
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing gaming wallet {wallet_address}: {e}")
            return {}
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()
            self.session = None
