"""
Multi-chain blockchain client supporting EVM and non-EVM chains
"""
import asyncio
import logging
import aiohttp
import json
from typing import Dict, Optional, Any, List, Union
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime

from web3 import Web3
from web3.middleware import ExtraDataToPOAMiddleware
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class BlockchainInfo:
    """Blockchain network information"""
    name: str
    chain_id: Optional[int]
    native_token: str
    block_time: float  # Average block time in seconds
    is_evm: bool
    gaming_focus: bool = False


class BaseChainClient(ABC):
    """Base class for blockchain clients"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        self.rpc_url = rpc_url
        self.chain_info = chain_info
        self.is_connected = False
    
    @abstractmethod
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number"""
        pass
    
    @abstractmethod
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        pass
    
    @abstractmethod
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        pass


class EVMChainClient(BaseChainClient):
    """Client for EVM-compatible chains"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        super().__init__(rpc_url, chain_info)
        self.web3 = None
        self._initialize_web3()
    
    def _initialize_web3(self):
        """Initialize Web3 connection"""
        try:
            self.web3 = Web3(Web3.HTTPProvider(
                self.rpc_url,
                request_kwargs={'timeout': settings.blockchain.request_timeout}
            ))
            
            # Add PoA middleware for chains that need it
            if self.chain_info.name in ['bsc', 'polygon', 'ronin']:
                self.web3.middleware_onion.inject(ExtraDataToPOAMiddleware, layer=0)
            
            self.is_connected = True
            logger.info(f"✅ Initialized {self.chain_info.name} Web3 client")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize {self.chain_info.name} Web3: {e}")
            self.is_connected = False
    
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number"""
        if not self.web3:
            return None
        
        try:
            return self.web3.eth.block_number
        except Exception as e:
            logger.error(f"Error getting latest block for {self.chain_info.name}: {e}")
            return None
    
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        if not self.web3:
            return None
        
        try:
            block = self.web3.eth.get_block(block_number, full_transactions=True)
            return dict(block)
        except Exception as e:
            logger.error(f"Error getting block {block_number} for {self.chain_info.name}: {e}")
            return None
    
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        if not self.web3:
            return None
        
        try:
            tx = self.web3.eth.get_transaction(tx_hash)
            return dict(tx)
        except Exception as e:
            logger.error(f"Error getting transaction {tx_hash} for {self.chain_info.name}: {e}")
            return None
    
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        try:
            block_number = await self.get_latest_block_number()
            return block_number is not None
        except Exception:
            return False

    async def get_contract_code(self, contract_address: str) -> Optional[str]:
        """Get contract bytecode"""
        if not self.web3:
            return None

        try:
            code = self.web3.eth.get_code(Web3.to_checksum_address(contract_address))
            return code.hex() if code else None
        except Exception as e:
            logger.error(f"Error getting contract code for {contract_address} on {self.chain_info.name}: {e}")
            return None
    
    async def call_contract_function(
        self,
        contract_address: str,
        abi: list,
        function_name: str,
        *args
    ) -> Any:
        """Call a contract function"""
        if not self.web3:
            return None
        
        try:
            contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(contract_address),
                abi=abi
            )
            
            function = getattr(contract.functions, function_name)
            result = function(*args).call()
            return result
        except Exception as e:
            logger.error(f"Error calling {function_name} on {contract_address} ({self.chain_info.name}): {e}")
            return None

    async def get_contract_creations(self, from_block: int, to_block: int) -> List[Dict]:
        """Get contract creation transactions in block range"""
        if not self.web3:
            return []

        contracts = []
        try:
            for block_num in range(from_block, min(to_block + 1, from_block + 100)):  # Limit to 100 blocks
                try:
                    block = self.web3.eth.get_block(block_num, full_transactions=True)
                    for tx in block.transactions:
                        # Contract creation has 'to' field as None
                        if tx.to is None and tx.input and len(tx.input) > 2:
                            # Get transaction receipt to get contract address
                            receipt = self.web3.eth.get_transaction_receipt(tx.hash)
                            if receipt.contractAddress:
                                contracts.append({
                                    'contract_address': receipt.contractAddress,
                                    'creator_address': tx['from'],
                                    'transaction_hash': tx.hash.hex(),
                                    'block_number': block_num,
                                    'gas_used': receipt.gasUsed,
                                    'bytecode': tx.input
                                })
                except Exception as e:
                    logger.debug(f"Error processing block {block_num}: {e}")
                    continue
        except Exception as e:
            logger.error(f"Error getting contract creations for {self.chain_info.name}: {e}")

        return contracts

    async def get_events_in_range(self, from_block: int, to_block: int, contract_addresses: List[str] = None) -> List[Dict]:
        """Get events in block range for specified contracts"""
        if not self.web3:
            return []

        events = []
        try:
            # Common gaming-related event signatures
            gaming_event_signatures = [
                '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',  # Transfer
                '0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925',  # Approval
                '0x17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31',  # ApprovalForAll
                '0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb',  # Transfer (ERC-721)
            ]

            # Build filter for events
            filter_params = {
                'fromBlock': from_block,
                'toBlock': min(to_block, from_block + 99),  # Limit range
                'topics': [gaming_event_signatures]
            }

            if contract_addresses:
                filter_params['address'] = [Web3.to_checksum_address(addr) for addr in contract_addresses[:10]]  # Limit addresses

            # Get logs
            logs = self.web3.eth.get_logs(filter_params)

            for log in logs:
                events.append({
                    'contract_address': log.address,
                    'transaction_hash': log.transactionHash.hex(),
                    'block_number': log.blockNumber,
                    'log_index': log.logIndex,
                    'topics': [topic.hex() for topic in log.topics],
                    'data': log.data.hex(),
                    'event_signature': log.topics[0].hex() if log.topics else None
                })

        except Exception as e:
            logger.error(f"Error getting events for {self.chain_info.name}: {e}")

        return events


class SolanaChainClient(BaseChainClient):
    """Client for Solana blockchain"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        super().__init__(rpc_url, chain_info)
        self.session = None
    
    async def _make_rpc_request(self, method: str, params: List = None) -> Optional[Dict]:
        """Make RPC request to Solana"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or []
        }
        
        try:
            async with self.session.post(
                self.rpc_url,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=settings.blockchain.request_timeout)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' in data:
                        logger.error(f"Solana RPC error: {data['error']}")
                        return None
                    return data.get('result')
                else:
                    logger.error(f"Solana RPC HTTP error: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Solana RPC request failed: {e}")
            return None
    
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest slot (block) number"""
        result = await self._make_rpc_request("getSlot")
        return result if result is not None else None
    
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        result = await self._make_rpc_request("getBlock", [block_number])
        return result
    
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        result = await self._make_rpc_request("getTransaction", [tx_hash])
        return result
    
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        try:
            slot = await self.get_latest_block_number()
            return slot is not None
        except Exception:
            return False
    
    async def get_account_info(self, account_address: str) -> Optional[Dict]:
        """Get Solana account information"""
        result = await self._make_rpc_request("getAccountInfo", [account_address])
        return result

    async def get_contract_creations(self, from_block: int, to_block: int) -> List[Dict]:
        """Get program deployments in slot range (Solana equivalent of contract creation)"""
        contracts = []
        try:
            # In Solana, we look for program deployments by checking account creations
            # This is a simplified implementation - in practice, you'd need to track
            # specific program deployment transactions
            for slot in range(from_block, min(to_block + 1, from_block + 50)):  # Limit to 50 slots
                try:
                    block = await self._make_rpc_request("getBlock", [slot, {"encoding": "json", "transactionDetails": "full"}])
                    if block and 'transactions' in block:
                        for tx in block['transactions']:
                            # Look for program deployment patterns
                            if tx.get('meta', {}).get('err') is None:  # Successful transaction
                                # Check if this looks like a program deployment
                                if self._is_program_deployment(tx):
                                    contracts.append({
                                        'program_address': self._extract_program_address(tx),
                                        'deployer_address': tx.get('transaction', {}).get('message', {}).get('accountKeys', [None])[0],
                                        'transaction_hash': tx.get('transaction', {}).get('signatures', [None])[0],
                                        'slot_number': slot,
                                        'compute_units_consumed': tx.get('meta', {}).get('computeUnitsConsumed', 0)
                                    })
                except Exception as e:
                    logger.debug(f"Error processing Solana slot {slot}: {e}")
                    continue
        except Exception as e:
            logger.error(f"Error getting Solana program deployments: {e}")

        return contracts

    async def get_events_in_range(self, from_block: int, to_block: int, program_addresses: List[str] = None) -> List[Dict]:
        """Get program logs/events in slot range"""
        events = []
        try:
            for slot in range(from_block, min(to_block + 1, from_block + 50)):  # Limit to 50 slots
                try:
                    block = await self._make_rpc_request("getBlock", [slot, {"encoding": "json", "transactionDetails": "full"}])
                    if block and 'transactions' in block:
                        for tx in block['transactions']:
                            if tx.get('meta', {}).get('err') is None:  # Successful transaction
                                # Extract logs/events from transaction
                                logs = tx.get('meta', {}).get('logMessages', [])
                                if logs:
                                    events.append({
                                        'program_address': self._extract_program_from_logs(logs),
                                        'transaction_hash': tx.get('transaction', {}).get('signatures', [None])[0],
                                        'slot_number': slot,
                                        'logs': logs,
                                        'compute_units_consumed': tx.get('meta', {}).get('computeUnitsConsumed', 0)
                                    })
                except Exception as e:
                    logger.debug(f"Error processing Solana slot {slot}: {e}")
                    continue
        except Exception as e:
            logger.error(f"Error getting Solana events: {e}")

        return events

    def _is_program_deployment(self, tx: Dict) -> bool:
        """Check if transaction looks like a program deployment"""
        # Simplified check - look for specific instruction patterns
        try:
            instructions = tx.get('transaction', {}).get('message', {}).get('instructions', [])
            for instruction in instructions:
                # Check for BPF Loader program calls (program deployment)
                if instruction.get('programIdIndex') == 0:  # System program
                    return True
        except Exception:
            pass
        return False

    def _extract_program_address(self, tx: Dict) -> Optional[str]:
        """Extract program address from deployment transaction"""
        try:
            account_keys = tx.get('transaction', {}).get('message', {}).get('accountKeys', [])
            if len(account_keys) > 1:
                return account_keys[1]  # Usually the second account is the program
        except Exception:
            pass
        return None

    def _extract_program_from_logs(self, logs: List[str]) -> Optional[str]:
        """Extract program address from transaction logs"""
        try:
            for log in logs:
                if 'Program' in log and 'invoke' in log:
                    # Extract program ID from log message
                    parts = log.split()
                    if len(parts) > 1:
                        return parts[1]
        except Exception:
            pass
        return None

    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()


class TONChainClient(BaseChainClient):
    """Client for TON blockchain"""
    
    def __init__(self, rpc_url: str, chain_info: BlockchainInfo):
        super().__init__(rpc_url, chain_info)
        self.session = None
    
    async def _make_rpc_request(self, method: str, params: Dict = None) -> Optional[Dict]:
        """Make RPC request to TON"""
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        try:
            async with self.session.post(
                self.rpc_url,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=settings.blockchain.request_timeout)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' in data:
                        logger.error(f"TON RPC error: {data['error']}")
                        return None
                    return data.get('result')
                else:
                    logger.error(f"TON RPC HTTP error: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"TON RPC request failed: {e}")
            return None
    
    async def get_latest_block_number(self) -> Optional[int]:
        """Get the latest block number"""
        result = await self._make_rpc_request("getMasterchainInfo")
        if result and 'last' in result:
            return result['last']['seqno']
        return None
    
    async def get_block(self, block_number: int) -> Optional[Dict]:
        """Get block data"""
        # TON uses different block identification
        result = await self._make_rpc_request("getBlockHeader", {"seqno": block_number})
        return result
    
    async def get_transaction(self, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        result = await self._make_rpc_request("getTransactions", {"hash": tx_hash})
        return result
    
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        try:
            block_number = await self.get_latest_block_number()
            return block_number is not None
        except Exception:
            return False
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()


class MultiChainManager:
    """Manager for multiple blockchain clients"""
    
    def __init__(self):
        self.clients: Dict[str, BaseChainClient] = {}
        self.blockchain_info = self._get_blockchain_info()
        self._initialize_clients()
    
    def _get_blockchain_info(self) -> Dict[str, BlockchainInfo]:
        """Get blockchain information for supported chains"""
        return {
            'ethereum': BlockchainInfo('ethereum', 1, 'ETH', 12.0, True, True),
            'polygon': BlockchainInfo('polygon', 137, 'MATIC', 2.0, True, True),
            'bsc': BlockchainInfo('bsc', 56, 'BNB', 3.0, True, True),
            'arbitrum': BlockchainInfo('arbitrum', 42161, 'ETH', 0.25, True, True),
            'optimism': BlockchainInfo('optimism', 10, 'ETH', 2.0, True, True),
            'base': BlockchainInfo('base', 8453, 'ETH', 2.0, True, True),
            'avalanche': BlockchainInfo('avalanche', 43114, 'AVAX', 2.0, True, True),
            'immutable': BlockchainInfo('immutable', 13371, 'IMX', 1.0, True, True),
            'ronin': BlockchainInfo('ronin', 2020, 'RON', 3.0, True, True),
            'solana': BlockchainInfo('solana', None, 'SOL', 0.4, False, True),
            'ton': BlockchainInfo('ton', None, 'TON', 5.0, False, True),
        }
    
    def _initialize_clients(self):
        """Initialize blockchain clients"""
        # EVM chains
        evm_chains = {
            'ethereum': settings.blockchain.ethereum_rpc_url,
            'polygon': settings.blockchain.polygon_rpc_url,
            'bsc': settings.blockchain.bsc_rpc_url,
            'arbitrum': settings.blockchain.arbitrum_rpc_url,
            'optimism': settings.blockchain.optimism_rpc_url,
            'base': settings.blockchain.base_rpc_url,
            'avalanche': settings.blockchain.avalanche_rpc_url,
            'immutable': settings.blockchain.immutable_rpc_url,
            'ronin': settings.blockchain.ronin_rpc_url,
        }
        
        for chain, rpc_url in evm_chains.items():
            if rpc_url:
                chain_info = self.blockchain_info[chain]
                self.clients[chain] = EVMChainClient(rpc_url, chain_info)
        
        # Non-EVM chains
        if settings.blockchain.solana_rpc_url:
            solana_info = self.blockchain_info['solana']
            self.clients['solana'] = SolanaChainClient(settings.blockchain.solana_rpc_url, solana_info)
        
        if settings.blockchain.ton_rpc_url:
            ton_info = self.blockchain_info['ton']
            self.clients['ton'] = TONChainClient(settings.blockchain.ton_rpc_url, ton_info)
    
    def get_client(self, chain: str) -> Optional[BaseChainClient]:
        """Get client for a specific chain"""
        return self.clients.get(chain)
    
    def get_gaming_chains(self) -> List[str]:
        """Get list of gaming-focused chains"""
        return [
            chain for chain, info in self.blockchain_info.items()
            if info.gaming_focus and chain in self.clients
        ]
    
    async def test_all_connections(self) -> Dict[str, bool]:
        """Test all blockchain connections"""
        results = {}
        
        for chain, client in self.clients.items():
            try:
                results[chain] = await client.test_connection()
                if results[chain]:
                    logger.info(f"✅ {chain}: Connection successful")
                else:
                    logger.error(f"❌ {chain}: Connection failed")
            except Exception as e:
                results[chain] = False
                logger.error(f"❌ {chain}: Connection test error - {e}")
        
        return results
    
    async def get_all_latest_blocks(self) -> Dict[str, Optional[int]]:
        """Get latest block numbers from all chains"""
        results = {}
        
        for chain, client in self.clients.items():
            try:
                block_number = await client.get_latest_block_number()
                results[chain] = block_number
                if block_number:
                    logger.info(f"{chain}: Latest block {block_number}")
            except Exception as e:
                results[chain] = None
                logger.error(f"Error getting latest block for {chain}: {e}")
        
        return results
    
    async def close_all(self):
        """Close all client connections"""
        for client in self.clients.values():
            if hasattr(client, 'close'):
                await client.close()


# Global multi-chain manager instance
multi_chain_manager = MultiChainManager()
