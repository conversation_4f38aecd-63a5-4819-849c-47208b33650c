#!/usr/bin/env python3
"""
Minimal API startup script for live system observation.
"""

import os
import sys
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Add project root to path
sys.path.insert(0, '.')

# Set environment variables
os.environ['DATABASE_URL'] = 'postgresql://postgres:postgres@localhost:5432/gaming_tracker'
os.environ['REDIS_URL'] = 'redis://localhost:6379/0'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create minimal FastAPI app
app = FastAPI(
    title="Web3 Gaming Scraper API",
    description="Minimal API for live system observation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Basic health check
@app.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "service": "web3-gaming-scraper",
        "version": "1.0.0"
    }

# Database status check
@app.get("/status/database")
async def database_status():
    """Check database connectivity"""
    try:
        from models.base import get_db_sync
        from sqlalchemy import text
        
        db = get_db_sync()
        try:
            # Test database connection
            result = db.execute(text("SELECT 1"))
            row = result.fetchone()
            
            # Get table counts
            tables_result = db.execute(text("""
                SELECT 
                    'webhook_subscriptions' as table_name, COUNT(*) as count FROM webhook_subscriptions
                UNION ALL
                SELECT 'webhook_deliveries', COUNT(*) FROM webhook_deliveries
                UNION ALL
                SELECT 'gaming_projects', COUNT(*) FROM gaming_projects
                UNION ALL
                SELECT 'gaming_contracts', COUNT(*) FROM gaming_contracts
                UNION ALL
                SELECT 'blockchain_data', COUNT(*) FROM blockchain_data
            """))
            
            table_counts = {}
            for row in tables_result:
                table_counts[row[0]] = row[1]
            
            return {
                "status": "connected",
                "database": "gaming_tracker",
                "table_counts": table_counts
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

# Redis status check
@app.get("/status/redis")
async def redis_status():
    """Check Redis connectivity"""
    try:
        import redis
        
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        
        # Get Redis info
        info = r.info()
        
        return {
            "status": "connected",
            "redis_version": info.get('redis_version'),
            "used_memory": info.get('used_memory_human'),
            "connected_clients": info.get('connected_clients')
        }
        
    except Exception as e:
        logger.error(f"Redis connection failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

# System status overview
@app.get("/status")
async def system_status():
    """Get overall system status"""
    try:
        # Check database
        db_status = await database_status()
        
        # Check Redis
        redis_status_result = await redis_status()
        
        # Overall status
        overall_status = "healthy"
        if db_status.get("status") != "connected" or redis_status_result.get("status") != "connected":
            overall_status = "degraded"
        
        return {
            "status": overall_status,
            "timestamp": "2025-07-15T00:00:00Z",
            "components": {
                "database": db_status,
                "redis": redis_status_result
            }
        }
        
    except Exception as e:
        logger.error(f"System status check failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

# Basic webhook endpoints
@app.get("/api/v1/webhooks/subscriptions")
async def list_webhook_subscriptions():
    """List webhook subscriptions"""
    try:
        from models.base import get_db_sync
        from sqlalchemy import text
        
        db = get_db_sync()
        try:
            result = db.execute(text("""
                SELECT 
                    id, subscriber_name, subscriber_url, is_active, is_verified,
                    total_deliveries, successful_deliveries, failed_deliveries,
                    created_at
                FROM webhook_subscriptions 
                ORDER BY created_at DESC
                LIMIT 10
            """))
            
            subscriptions = []
            for row in result:
                subscriptions.append({
                    "id": row[0],
                    "subscriber_name": row[1],
                    "subscriber_url": row[2],
                    "is_active": row[3],
                    "is_verified": row[4],
                    "total_deliveries": row[5],
                    "successful_deliveries": row[6],
                    "failed_deliveries": row[7],
                    "created_at": row[8].isoformat() if row[8] else None
                })
            
            return {
                "subscriptions": subscriptions,
                "total": len(subscriptions)
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to list subscriptions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Basic gaming data endpoints
@app.get("/api/v1/gaming/projects")
async def list_gaming_projects():
    """List gaming projects"""
    try:
        from models.base import get_db_sync
        from sqlalchemy import text
        
        db = get_db_sync()
        try:
            result = db.execute(text("""
                SELECT 
                    id, project_name, blockchain, primary_genre, is_active,
                    created_at
                FROM gaming_projects 
                WHERE is_active = true
                ORDER BY created_at DESC
                LIMIT 10
            """))
            
            projects = []
            for row in result:
                projects.append({
                    "id": row[0],
                    "project_name": row[1],
                    "blockchain": row[2],
                    "primary_genre": row[3],
                    "is_active": row[4],
                    "created_at": row[5].isoformat() if row[5] else None
                })
            
            return {
                "projects": projects,
                "total": len(projects)
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to list gaming projects: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Recent blockchain activity
@app.get("/api/v1/blockchain/recent")
async def recent_blockchain_activity():
    """Get recent blockchain activity"""
    try:
        from models.base import get_db_sync
        from sqlalchemy import text
        
        db = get_db_sync()
        try:
            result = db.execute(text("""
                SELECT 
                    blockchain, event_type, contract_address, 
                    from_address, to_address, token_price_usd,
                    block_timestamp, gaming_project_id
                FROM blockchain_data 
                ORDER BY block_timestamp DESC
                LIMIT 20
            """))
            
            activities = []
            for row in result:
                activities.append({
                    "blockchain": row[0],
                    "event_type": row[1],
                    "contract_address": row[2],
                    "from_address": row[3],
                    "to_address": row[4],
                    "token_price_usd": float(row[5]) if row[5] else None,
                    "block_timestamp": row[6].isoformat() if row[6] else None,
                    "gaming_project_id": row[7]
                })
            
            return {
                "activities": activities,
                "total": len(activities)
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to get blockchain activity: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    print("🚀 Starting Minimal Web3 Gaming Scraper API")
    print("=" * 50)
    print("📊 Health Check: http://localhost:8001/health")
    print("🔍 System Status: http://localhost:8001/status")
    print("🎮 Gaming Projects: http://localhost:8001/api/v1/gaming/projects")
    print("⛓️ Recent Activity: http://localhost:8001/api/v1/blockchain/recent")
    print("🔗 Webhook Subscriptions: http://localhost:8001/api/v1/webhooks/subscriptions")
    print("=" * 50)
    
    uvicorn.run(
        "start_minimal_api:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
