# Web3 Gaming News Tracker

An automated system that continuously monitors and aggregates web3 crypto blockchain gaming news from multiple sources, providing comprehensive coverage of the rapidly evolving GameFi and NFT gaming landscape.

## Features

- **Multi-source Integration**: Gaming news sites, Twitter, Reddit, Discord, blockchain events
- **Blockchain Integration**: Direct RPC endpoints for on-chain gaming data
- **Real-time Processing**: Continuous monitoring of gaming protocols and news
- **Gaming Classification**: Automatic tagging for P2E, NFT gaming, DeFi gaming, metaverse
- **On-chain Analytics**: Token prices, NFT floor prices, gaming protocol metrics
- **Advanced Search**: Full-text search with blockchain data filtering
- **REST API**: Programmatic access to gaming news and on-chain data
- **Scalable Architecture**: Built for high-volume data processing

## Technology Stack

- **Backend**: Python 3.11, FastAPI, SQLAlchemy
- **Database**: PostgreSQL with full-text search
- **Blockchain**: Web3.py, ethers-py for RPC integration
- **Task Queue**: Celery with Redis
- **Web Scraping**: BeautifulSoup4, Scrapy, Selenium
- **APIs**: Twitter API, Reddit API, Gaming News APIs
- **Infrastructure**: Docker, Docker Compose

## Gaming Focus Areas

- **Play-to-Earn (P2E)**: Axie Infinity, Splinterlands, Gods Unchained
- **NFT Gaming**: Gaming NFT collections, in-game assets
- **DeFi Gaming**: Yield farming games, gaming tokens
- **Metaverse**: Virtual worlds, land sales, avatar projects
- **Gaming Infrastructure**: Gaming chains, scaling solutions
- **GameFi Protocols**: Gaming-focused DeFi protocols

## Blockchain Networks

- **Ethereum**: Primary gaming NFTs and DeFi
- **Polygon**: Gaming scaling solution
- **BSC**: Gaming tokens and protocols
- **Immutable X**: NFT gaming focus
- **Ronin**: Axie Infinity ecosystem
- **WAX**: Gaming-focused blockchain

## Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Git
- RPC endpoint access (Infura, Alchemy, or self-hosted)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd webThreeGameScraper
   ```

2. **Set up virtual environment**
   ```bash
   python3.11 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration including RPC endpoints
   ```

5. **Start database services**
   ```bash
   docker-compose up -d postgres redis
   ```

6. **Initialize the database**
   ```bash
   python manage.py init-db
   ```

7. **Run the API server**
   ```bash
   python manage.py run-api
   ```

The API will be available at `http://localhost:8000` with documentation at `http://localhost:8000/docs`.

## Development

### Project Structure

```
├── api/                 # FastAPI application and endpoints
├── models/              # Database models and schemas
├── scrapers/            # Data collection modules
│   ├── news/           # Gaming news scrapers
│   ├── blockchain/     # On-chain data scrapers
│   └── social/         # Social media scrapers
├── blockchain/          # Blockchain integration utilities
├── utils/               # Shared utilities
├── config/              # Configuration management
├── tests/               # Test suite
├── alembic/             # Database migrations
├── docs/                # Documentation
├── docker-compose.yml   # Docker services
├── requirements.txt     # Python dependencies
└── manage.py           # Management CLI
```

### Management Commands

```bash
# Database management
python manage.py db init              # Initialize database ✅
python manage.py db reset             # Reset database (with confirmation) ✅
python manage.py db test              # Test database connection ✅
python manage.py db migrate           # Create new migration ✅
python manage.py db upgrade           # Apply migrations ✅
python manage.py db backup            # Create database backup ✅
python manage.py db current           # Show current migration ✅

# Blockchain management
python manage.py blockchain test-rpc  # Test RPC connections ✅
python manage.py blockchain start-scraper  # Start blockchain scraper ✅
python manage.py blockchain test-apis # Test blockchain API connections ✅

# Gaming data management
python manage.py gaming sync-projects # Sync gaming projects ⚠️ (partial)
python manage.py gaming sync-nfts     # Sync NFT collections ⚠️ (partial)

# Scraper management
python manage.py scraper start        # Start all scrapers ⚠️ (partial)
python manage.py scraper stop         # Stop all scrapers ⚠️ (partial)
python manage.py scraper test         # Test scraper connections ✅

# Development server
python manage.py run-api              # Run FastAPI server ✅
python manage.py run-worker           # Run Celery worker ✅
python manage.py run-beat             # Run Celery beat scheduler ✅

# System status
python manage.py status               # Show system status ✅

# Alternative script commands (direct execution)
python scripts/system_status.py      # Comprehensive system check ✅
python scripts/test_blockchain_api_keys.py  # Test API keys ✅
python scripts/init_gaming_database.py      # Initialize gaming DB ✅
python scripts/database_backup.py    # Database backup utility ✅
```

## Configuration

Key environment variables for web3 gaming tracker:

- **Database**: PostgreSQL connection settings
- **Redis**: Cache and task queue settings
- **RPC Endpoints**: Ethereum, Polygon, BSC, etc.
- **API Keys**: Twitter, Reddit, News API credentials
- **Gaming APIs**: OpenSea, CoinGecko, DeFiPulse
- **Scraping**: Rate limiting and retry settings

See `.env.example` for all available configuration options.

## API Documentation

Once the server is running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## Development Roadmap

This project follows an 8-phase development plan focused on web3 gaming:

- **Phase 1**: ✅ Foundation & Core Infrastructure (Current)
- **Phase 2**: Database & Data Models for Gaming
- **Phase 3**: Blockchain Integration Framework
- **Phase 4**: Web3 Gaming News Sources
- **Phase 5**: On-chain Data Collection
- **Phase 6**: Content Classification & Analytics
- **Phase 7**: API & Search Capabilities
- **Phase 8**: Real-time Monitoring & Alerts

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

[Add your license here]

## Support

[Add support information here]
