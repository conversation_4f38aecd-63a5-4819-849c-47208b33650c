"""
Webhook receiver endpoints for external blockchain services.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request, Header, BackgroundTasks
from pydantic import BaseModel, Field

from webhooks.event_bus import event_bus, emit_blockchain_event, emit_gaming_event, emit_nft_event, emit_token_event
from webhooks.events import WebhookEventType
from config.settings import get_settings


logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/receive", tags=["webhook-receivers"])


# Pydantic Models for External Webhooks

class AlchemyWebhookEvent(BaseModel):
    """Alchemy webhook event structure."""
    webhookId: str
    id: str
    createdAt: datetime
    type: str
    event: Dict[str, Any]


class MoralisWebhookEvent(BaseModel):
    """Moralis webhook event structure."""
    confirmed: bool
    chainId: str
    abi: Optional[Dict[str, Any]] = None
    streamId: str
    tag: str
    retries: int
    block: Dict[str, Any]
    logs: list
    txs: list
    txsInternal: list
    erc20Transfers: list
    erc20Approvals: list
    nftTransfers: list


class QuickNodeWebhookEvent(BaseModel):
    """QuickNode webhook event structure."""
    id: str
    network: str
    type: str
    data: Dict[str, Any]


class GenericBlockchainEvent(BaseModel):
    """Generic blockchain event for custom integrations."""
    event_type: str
    blockchain: str
    block_number: Optional[int] = None
    transaction_hash: Optional[str] = None
    contract_address: Optional[str] = None
    from_address: Optional[str] = None
    to_address: Optional[str] = None
    token_address: Optional[str] = None
    token_id: Optional[str] = None
    amount: Optional[str] = None
    data: Dict[str, Any] = Field(default_factory=dict)


# Security Functions

def verify_alchemy_signature(request: Request, signature: Optional[str] = Header(None)) -> bool:
    """Verify Alchemy webhook signature."""
    if not signature:
        return False
    
    settings = get_settings()
    webhook_secret = getattr(settings, 'ALCHEMY_WEBHOOK_SECRET', None)
    
    if not webhook_secret:
        logger.warning("Alchemy webhook secret not configured")
        return False
    
    # Implement Alchemy signature verification
    # This would use their specific signature algorithm
    return True  # Simplified for now


def verify_moralis_signature(request: Request, signature: Optional[str] = Header(None)) -> bool:
    """Verify Moralis webhook signature."""
    if not signature:
        return False
    
    settings = get_settings()
    webhook_secret = getattr(settings, 'MORALIS_WEBHOOK_SECRET', None)
    
    if not webhook_secret:
        logger.warning("Moralis webhook secret not configured")
        return False
    
    # Implement Moralis signature verification
    return True  # Simplified for now


def verify_quicknode_signature(request: Request, signature: Optional[str] = Header(None)) -> bool:
    """Verify QuickNode webhook signature."""
    if not signature:
        return False
    
    settings = get_settings()
    webhook_secret = getattr(settings, 'QUICKNODE_WEBHOOK_SECRET', None)
    
    if not webhook_secret:
        logger.warning("QuickNode webhook secret not configured")
        return False
    
    # Implement QuickNode signature verification
    return True  # Simplified for now


# Event Processing Functions

async def process_alchemy_event(event: AlchemyWebhookEvent, background_tasks: BackgroundTasks):
    """Process Alchemy webhook event."""
    try:
        event_data = event.event
        
        # Determine event type and blockchain
        blockchain = "ethereum"  # Default, could be extracted from event
        
        if event.type == "ADDRESS_ACTIVITY":
            # Process address activity
            for activity in event_data.get("activity", []):
                if activity.get("category") == "erc721":
                    # NFT transfer
                    await emit_nft_event(
                        event_type=WebhookEventType.NFT_TRANSFERRED,
                        blockchain=blockchain,
                        contract_address=activity.get("rawContract", {}).get("address"),
                        token_id=activity.get("tokenId"),
                        from_address=activity.get("fromAddress"),
                        to_address=activity.get("toAddress"),
                        data=activity
                    )
                elif activity.get("category") == "erc20":
                    # Token transfer
                    await emit_token_event(
                        event_type=WebhookEventType.TOKEN_TRANSFER,
                        blockchain=blockchain,
                        token_address=activity.get("rawContract", {}).get("address"),
                        amount=float(activity.get("value", 0)),
                        from_address=activity.get("fromAddress"),
                        to_address=activity.get("toAddress"),
                        data=activity
                    )
        
        elif event.type == "MINED_TRANSACTION":
            # Process mined transaction
            tx_data = event_data.get("transaction", {})
            await emit_blockchain_event(
                event_type=WebhookEventType.TRANSACTION_CONFIRMED,
                blockchain=blockchain,
                transaction_hash=tx_data.get("hash"),
                block_number=int(tx_data.get("blockNum", 0), 16),
                data=tx_data
            )
        
        logger.info(f"Processed Alchemy webhook event: {event.type}")
        
    except Exception as e:
        logger.error(f"Error processing Alchemy event: {e}")
        raise


async def process_moralis_event(event: MoralisWebhookEvent, background_tasks: BackgroundTasks):
    """Process Moralis webhook event."""
    try:
        # Map chainId to blockchain name
        chain_mapping = {
            "0x1": "ethereum",
            "0x89": "polygon",
            "0x38": "bsc",
            "0xa4b1": "arbitrum",
            "0xa": "optimism"
        }
        
        blockchain = chain_mapping.get(event.chainId, "ethereum")
        
        # Process NFT transfers
        for nft_transfer in event.nftTransfers:
            await emit_nft_event(
                event_type=WebhookEventType.NFT_TRANSFERRED,
                blockchain=blockchain,
                contract_address=nft_transfer.get("contract"),
                token_id=nft_transfer.get("tokenId"),
                from_address=nft_transfer.get("from"),
                to_address=nft_transfer.get("to"),
                data=nft_transfer
            )
        
        # Process ERC20 transfers
        for erc20_transfer in event.erc20Transfers:
            await emit_token_event(
                event_type=WebhookEventType.TOKEN_TRANSFER,
                blockchain=blockchain,
                token_address=erc20_transfer.get("contract"),
                amount=float(erc20_transfer.get("value", 0)),
                from_address=erc20_transfer.get("from"),
                to_address=erc20_transfer.get("to"),
                data=erc20_transfer
            )
        
        # Process transactions
        for tx in event.txs:
            await emit_blockchain_event(
                event_type=WebhookEventType.TRANSACTION_CONFIRMED,
                blockchain=blockchain,
                transaction_hash=tx.get("hash"),
                block_number=event.block.get("number"),
                data=tx
            )
        
        logger.info(f"Processed Moralis webhook event for chain {event.chainId}")
        
    except Exception as e:
        logger.error(f"Error processing Moralis event: {e}")
        raise


async def process_quicknode_event(event: QuickNodeWebhookEvent, background_tasks: BackgroundTasks):
    """Process QuickNode webhook event."""
    try:
        blockchain = event.network.lower()
        
        if event.type == "block":
            # New block
            await emit_blockchain_event(
                event_type=WebhookEventType.BLOCK_MINED,
                blockchain=blockchain,
                block_number=event.data.get("number"),
                data=event.data
            )
        
        elif event.type == "tx":
            # New transaction
            await emit_blockchain_event(
                event_type=WebhookEventType.TRANSACTION_CONFIRMED,
                blockchain=blockchain,
                transaction_hash=event.data.get("hash"),
                block_number=event.data.get("blockNumber"),
                data=event.data
            )
        
        logger.info(f"Processed QuickNode webhook event: {event.type}")
        
    except Exception as e:
        logger.error(f"Error processing QuickNode event: {e}")
        raise


async def process_generic_event(event: GenericBlockchainEvent, background_tasks: BackgroundTasks):
    """Process generic blockchain event."""
    try:
        if event.event_type == "nft_transfer":
            await emit_nft_event(
                event_type=WebhookEventType.NFT_TRANSFERRED,
                blockchain=event.blockchain,
                contract_address=event.contract_address,
                token_id=event.token_id,
                from_address=event.from_address,
                to_address=event.to_address,
                data=event.data
            )
        
        elif event.event_type == "token_transfer":
            await emit_token_event(
                event_type=WebhookEventType.TOKEN_TRANSFER,
                blockchain=event.blockchain,
                token_address=event.token_address,
                amount=float(event.amount) if event.amount else None,
                from_address=event.from_address,
                to_address=event.to_address,
                data=event.data
            )
        
        elif event.event_type == "transaction":
            await emit_blockchain_event(
                event_type=WebhookEventType.TRANSACTION_CONFIRMED,
                blockchain=event.blockchain,
                transaction_hash=event.transaction_hash,
                block_number=event.block_number,
                contract_address=event.contract_address,
                data=event.data
            )
        
        elif event.event_type == "block":
            await emit_blockchain_event(
                event_type=WebhookEventType.BLOCK_MINED,
                blockchain=event.blockchain,
                block_number=event.block_number,
                data=event.data
            )
        
        logger.info(f"Processed generic webhook event: {event.event_type}")
        
    except Exception as e:
        logger.error(f"Error processing generic event: {e}")
        raise


# Webhook Receiver Endpoints

@router.post("/alchemy")
async def receive_alchemy_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    x_alchemy_signature: Optional[str] = Header(None)
):
    """Receive webhook from Alchemy."""
    # Verify signature
    if not verify_alchemy_signature(request, x_alchemy_signature):
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    try:
        body = await request.json()
        event = AlchemyWebhookEvent(**body)
        
        # Process event in background
        background_tasks.add_task(process_alchemy_event, event, background_tasks)
        
        return {"status": "received", "event_id": event.id}
        
    except Exception as e:
        logger.error(f"Error receiving Alchemy webhook: {e}")
        raise HTTPException(status_code=400, detail="Invalid webhook data")


@router.post("/moralis")
async def receive_moralis_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    x_signature: Optional[str] = Header(None)
):
    """Receive webhook from Moralis."""
    # Verify signature
    if not verify_moralis_signature(request, x_signature):
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    try:
        body = await request.json()
        event = MoralisWebhookEvent(**body)
        
        # Process event in background
        background_tasks.add_task(process_moralis_event, event, background_tasks)
        
        return {"status": "received", "stream_id": event.streamId}
        
    except Exception as e:
        logger.error(f"Error receiving Moralis webhook: {e}")
        raise HTTPException(status_code=400, detail="Invalid webhook data")


@router.post("/quicknode")
async def receive_quicknode_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    x_qn_signature: Optional[str] = Header(None)
):
    """Receive webhook from QuickNode."""
    # Verify signature
    if not verify_quicknode_signature(request, x_qn_signature):
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    try:
        body = await request.json()
        event = QuickNodeWebhookEvent(**body)
        
        # Process event in background
        background_tasks.add_task(process_quicknode_event, event, background_tasks)
        
        return {"status": "received", "event_id": event.id}
        
    except Exception as e:
        logger.error(f"Error receiving QuickNode webhook: {e}")
        raise HTTPException(status_code=400, detail="Invalid webhook data")


@router.post("/generic")
async def receive_generic_webhook(
    event: GenericBlockchainEvent,
    background_tasks: BackgroundTasks
):
    """Receive generic blockchain webhook."""
    try:
        # Process event in background
        background_tasks.add_task(process_generic_event, event, background_tasks)
        
        return {"status": "received", "event_type": event.event_type}
        
    except Exception as e:
        logger.error(f"Error receiving generic webhook: {e}")
        raise HTTPException(status_code=400, detail="Invalid webhook data")
