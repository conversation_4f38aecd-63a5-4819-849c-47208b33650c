"""
External webhook API endpoints with authentication and security.
"""

import logging
import hashlib
import hmac
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Header, Request, BackgroundTasks
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from sqlalchemy.orm import Session

from models.base import get_db_sync
from models.gaming import WebhookSubscription, WebhookDelivery
from webhooks.manager import webhook_manager
from webhooks.events import WebhookEventType
from config.settings import get_settings


logger = logging.getLogger(__name__)
security = HTTPBearer()
router = APIRouter(prefix="/api/v1/webhooks", tags=["webhooks"])


# Pydantic Models for API

class WebhookSubscriptionCreate(BaseModel):
    """Create webhook subscription request."""
    subscriber_name: str = Field(..., min_length=1, max_length=200)
    subscriber_url: str = Field(..., pattern=r'^https?://.+')
    subscriber_email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    
    # Event filtering
    event_types: List[str] = Field(default_factory=list)
    blockchain_filters: List[str] = Field(default_factory=list)
    gaming_categories: List[str] = Field(default_factory=list)
    gaming_projects_filter: List[str] = Field(default_factory=list)
    
    # Thresholds
    min_significance_score: float = Field(default=0.0, ge=0.0, le=1.0)
    min_token_value_usd: float = Field(default=0.0, ge=0.0)
    min_nft_value_usd: float = Field(default=0.0, ge=0.0)
    
    # Rate limiting
    rate_limit_per_minute: int = Field(default=100, ge=1, le=1000)
    
    @validator('event_types')
    def validate_event_types(cls, v):
        valid_types = [e.value for e in WebhookEventType]
        for event_type in v:
            if event_type not in valid_types:
                raise ValueError(f"Invalid event type: {event_type}")
        return v
    
    @validator('blockchain_filters')
    def validate_blockchains(cls, v):
        valid_chains = ['ethereum', 'polygon', 'bsc', 'solana', 'arbitrum', 'optimism']
        for chain in v:
            if chain not in valid_chains:
                raise ValueError(f"Invalid blockchain: {chain}")
        return v
    
    @validator('gaming_categories')
    def validate_gaming_categories(cls, v):
        valid_categories = ['P2E', 'NFT_Gaming', 'DeFi_Gaming', 'Metaverse', 'Social_Gaming', 'Strategy', 'RPG', 'Collectibles']
        for category in v:
            if category not in valid_categories:
                raise ValueError(f"Invalid gaming category: {category}")
        return v


class WebhookSubscriptionUpdate(BaseModel):
    """Update webhook subscription request."""
    subscriber_name: Optional[str] = Field(None, min_length=1, max_length=200)
    subscriber_url: Optional[str] = Field(None, pattern=r'^https?://.+')
    subscriber_email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    
    event_types: Optional[List[str]] = None
    blockchain_filters: Optional[List[str]] = None
    gaming_categories: Optional[List[str]] = None
    gaming_projects_filter: Optional[List[str]] = None
    
    min_significance_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    min_token_value_usd: Optional[float] = Field(None, ge=0.0)
    min_nft_value_usd: Optional[float] = Field(None, ge=0.0)
    
    rate_limit_per_minute: Optional[int] = Field(None, ge=1, le=1000)
    is_active: Optional[bool] = None


class WebhookSubscriptionResponse(BaseModel):
    """Webhook subscription response."""
    id: int
    subscriber_name: str
    subscriber_url: str
    subscriber_email: Optional[str]
    api_key: str
    webhook_secret: str
    
    event_types: List[str]
    blockchain_filters: List[str]
    gaming_categories: List[str]
    min_significance_score: float
    min_token_value_usd: float
    min_nft_value_usd: float
    
    is_active: bool
    is_verified: bool
    rate_limit_per_minute: int
    
    total_deliveries: int
    successful_deliveries: int
    failed_deliveries: int
    last_delivery_at: Optional[datetime]
    
    created_at: datetime
    updated_at: datetime


class WebhookDeliveryResponse(BaseModel):
    """Webhook delivery response."""
    id: int
    webhook_event_id: str
    event_type: str
    gaming_category: Optional[str]
    gaming_action: Optional[str]
    significance_score: Optional[float]
    
    delivery_status: str
    delivery_attempts: int
    last_attempt_at: Optional[datetime]
    delivered_at: Optional[datetime]
    
    http_status_code: Optional[int]
    response_time_ms: Optional[int]
    error_message: Optional[str]
    
    created_at: datetime


class WebhookTestRequest(BaseModel):
    """Test webhook request."""
    event_type: str = Field(..., description="Type of test event to send")
    test_data: Dict[str, Any] = Field(default_factory=dict, description="Custom test data")


# Authentication and Security

def generate_api_key() -> str:
    """Generate a secure API key."""
    return f"wh_{secrets.token_urlsafe(32)}"


def generate_webhook_secret() -> str:
    """Generate a secure webhook secret."""
    return secrets.token_urlsafe(32)


def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)) -> WebhookSubscription:
    """Verify API key and return subscription."""
    if not credentials.credentials.startswith('wh_'):
        raise HTTPException(status_code=401, detail="Invalid API key format")
    
    db = get_db_sync()
    try:
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.api_key == credentials.credentials,
            WebhookSubscription.is_active == True
        ).first()
        
        if not subscription:
            raise HTTPException(status_code=401, detail="Invalid or inactive API key")
        
        return subscription
    finally:
        db.close()


def verify_webhook_signature(
    request: Request,
    subscription: WebhookSubscription,
    x_webhook_signature: Optional[str] = Header(None)
) -> bool:
    """Verify webhook signature for incoming webhooks."""
    if not x_webhook_signature or not subscription.webhook_secret:
        return False
    
    try:
        # Get request body
        body = request.body()
        
        # Calculate expected signature
        expected_signature = hmac.new(
            subscription.webhook_secret.encode(),
            body,
            hashlib.sha256
        ).hexdigest()
        
        # Compare signatures
        return hmac.compare_digest(f"sha256={expected_signature}", x_webhook_signature)
    except Exception as e:
        logger.error(f"Error verifying webhook signature: {e}")
        return False


# API Endpoints

@router.post("/subscriptions", response_model=WebhookSubscriptionResponse)
async def create_webhook_subscription(
    subscription_data: WebhookSubscriptionCreate,
    background_tasks: BackgroundTasks
):
    """Create a new webhook subscription."""
    db = get_db_sync()
    try:
        # Check if URL already exists
        existing = db.query(WebhookSubscription).filter(
            WebhookSubscription.subscriber_url == subscription_data.subscriber_url
        ).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="Subscription URL already exists")
        
        # Generate credentials
        api_key = generate_api_key()
        webhook_secret = generate_webhook_secret()
        
        # Create subscription
        subscription = WebhookSubscription(
            subscriber_name=subscription_data.subscriber_name,
            subscriber_url=subscription_data.subscriber_url,
            subscriber_email=subscription_data.subscriber_email,
            api_key=api_key,
            webhook_secret=webhook_secret,
            event_types=subscription_data.event_types,
            blockchain_filters=subscription_data.blockchain_filters,
            gaming_categories=subscription_data.gaming_categories,
            gaming_projects_filter=subscription_data.gaming_projects_filter,
            min_significance_score=subscription_data.min_significance_score,
            min_token_value_usd=subscription_data.min_token_value_usd,
            min_nft_value_usd=subscription_data.min_nft_value_usd,
            rate_limit_per_minute=subscription_data.rate_limit_per_minute,
            is_active=True,
            is_verified=False
        )
        
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        
        # Schedule verification webhook
        background_tasks.add_task(send_verification_webhook, subscription.id)
        
        return WebhookSubscriptionResponse(
            id=subscription.id,
            subscriber_name=subscription.subscriber_name,
            subscriber_url=subscription.subscriber_url,
            subscriber_email=subscription.subscriber_email,
            api_key=subscription.api_key,
            webhook_secret=subscription.webhook_secret,
            event_types=subscription.event_types or [],
            blockchain_filters=subscription.blockchain_filters or [],
            gaming_categories=subscription.gaming_categories or [],
            min_significance_score=subscription.min_significance_score,
            min_token_value_usd=subscription.min_token_value_usd,
            min_nft_value_usd=subscription.min_nft_value_usd,
            is_active=subscription.is_active,
            is_verified=subscription.is_verified,
            rate_limit_per_minute=subscription.rate_limit_per_minute,
            total_deliveries=subscription.total_deliveries,
            successful_deliveries=subscription.successful_deliveries,
            failed_deliveries=subscription.failed_deliveries,
            last_delivery_at=subscription.last_delivery_at,
            created_at=subscription.created_at,
            updated_at=subscription.updated_at
        )
        
    finally:
        db.close()


@router.get("/subscriptions/me", response_model=WebhookSubscriptionResponse)
async def get_my_subscription(subscription: WebhookSubscription = Depends(verify_api_key)):
    """Get current subscription details."""
    return WebhookSubscriptionResponse(
        id=subscription.id,
        subscriber_name=subscription.subscriber_name,
        subscriber_url=subscription.subscriber_url,
        subscriber_email=subscription.subscriber_email,
        api_key=subscription.api_key,
        webhook_secret=subscription.webhook_secret,
        event_types=subscription.event_types or [],
        blockchain_filters=subscription.blockchain_filters or [],
        gaming_categories=subscription.gaming_categories or [],
        min_significance_score=subscription.min_significance_score,
        min_token_value_usd=subscription.min_token_value_usd,
        min_nft_value_usd=subscription.min_nft_value_usd,
        is_active=subscription.is_active,
        is_verified=subscription.is_verified,
        rate_limit_per_minute=subscription.rate_limit_per_minute,
        total_deliveries=subscription.total_deliveries,
        successful_deliveries=subscription.successful_deliveries,
        failed_deliveries=subscription.failed_deliveries,
        last_delivery_at=subscription.last_delivery_at,
        created_at=subscription.created_at,
        updated_at=subscription.updated_at
    )


@router.put("/subscriptions/me", response_model=WebhookSubscriptionResponse)
async def update_my_subscription(
    update_data: WebhookSubscriptionUpdate,
    subscription: WebhookSubscription = Depends(verify_api_key)
):
    """Update current subscription."""
    db = get_db_sync()
    try:
        # Update fields
        if update_data.subscriber_name is not None:
            subscription.subscriber_name = update_data.subscriber_name
        if update_data.subscriber_url is not None:
            subscription.subscriber_url = update_data.subscriber_url
        if update_data.subscriber_email is not None:
            subscription.subscriber_email = update_data.subscriber_email
        if update_data.event_types is not None:
            subscription.event_types = update_data.event_types
        if update_data.blockchain_filters is not None:
            subscription.blockchain_filters = update_data.blockchain_filters
        if update_data.gaming_categories is not None:
            subscription.gaming_categories = update_data.gaming_categories
        if update_data.gaming_projects_filter is not None:
            subscription.gaming_projects_filter = update_data.gaming_projects_filter
        if update_data.min_significance_score is not None:
            subscription.min_significance_score = update_data.min_significance_score
        if update_data.min_token_value_usd is not None:
            subscription.min_token_value_usd = update_data.min_token_value_usd
        if update_data.min_nft_value_usd is not None:
            subscription.min_nft_value_usd = update_data.min_nft_value_usd
        if update_data.rate_limit_per_minute is not None:
            subscription.rate_limit_per_minute = update_data.rate_limit_per_minute
        if update_data.is_active is not None:
            subscription.is_active = update_data.is_active

        subscription.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(subscription)

        return WebhookSubscriptionResponse(
            id=subscription.id,
            subscriber_name=subscription.subscriber_name,
            subscriber_url=subscription.subscriber_url,
            subscriber_email=subscription.subscriber_email,
            api_key=subscription.api_key,
            webhook_secret=subscription.webhook_secret,
            event_types=subscription.event_types or [],
            blockchain_filters=subscription.blockchain_filters or [],
            gaming_categories=subscription.gaming_categories or [],
            min_significance_score=subscription.min_significance_score,
            min_token_value_usd=subscription.min_token_value_usd,
            min_nft_value_usd=subscription.min_nft_value_usd,
            is_active=subscription.is_active,
            is_verified=subscription.is_verified,
            rate_limit_per_minute=subscription.rate_limit_per_minute,
            total_deliveries=subscription.total_deliveries,
            successful_deliveries=subscription.successful_deliveries,
            failed_deliveries=subscription.failed_deliveries,
            last_delivery_at=subscription.last_delivery_at,
            created_at=subscription.created_at,
            updated_at=subscription.updated_at
        )

    finally:
        db.close()


@router.delete("/subscriptions/me")
async def delete_my_subscription(subscription: WebhookSubscription = Depends(verify_api_key)):
    """Delete current subscription."""
    db = get_db_sync()
    try:
        subscription.is_active = False
        subscription.updated_at = datetime.utcnow()
        db.commit()

        return {"message": "Subscription deactivated successfully"}

    finally:
        db.close()


@router.get("/subscriptions/me/deliveries", response_model=List[WebhookDeliveryResponse])
async def get_my_deliveries(
    subscription: WebhookSubscription = Depends(verify_api_key),
    limit: int = 100,
    offset: int = 0
):
    """Get webhook delivery history for current subscription."""
    db = get_db_sync()
    try:
        deliveries = db.query(WebhookDelivery).filter(
            WebhookDelivery.subscriber_url == subscription.subscriber_url
        ).order_by(
            WebhookDelivery.created_at.desc()
        ).offset(offset).limit(limit).all()

        return [
            WebhookDeliveryResponse(
                id=delivery.id,
                webhook_event_id=delivery.webhook_event_id,
                event_type=delivery.event_type,
                gaming_category=delivery.gaming_category,
                gaming_action=delivery.gaming_action,
                significance_score=delivery.significance_score,
                delivery_status=delivery.delivery_status,
                delivery_attempts=delivery.delivery_attempts,
                last_attempt_at=delivery.last_attempt_at,
                delivered_at=delivery.delivered_at,
                http_status_code=delivery.http_status_code,
                response_time_ms=delivery.response_time_ms,
                error_message=delivery.error_message,
                created_at=delivery.created_at
            )
            for delivery in deliveries
        ]

    finally:
        db.close()


@router.post("/subscriptions/me/test")
async def test_webhook(
    test_request: WebhookTestRequest,
    background_tasks: BackgroundTasks,
    subscription: WebhookSubscription = Depends(verify_api_key)
):
    """Send a test webhook to verify endpoint."""
    from webhooks.events import GamingWebhookEvent, WebhookEventType
    from webhooks.event_bus import event_bus

    # Create test event
    test_event = GamingWebhookEvent(
        event_type=WebhookEventType(test_request.event_type),
        blockchain="ethereum",
        gaming_project="Test Project",
        gaming_action="test",
        data=test_request.test_data
    )

    # Emit test event
    background_tasks.add_task(event_bus.emit, test_event)

    return {"message": "Test webhook queued for delivery"}


@router.post("/subscriptions/me/regenerate-secret")
async def regenerate_webhook_secret(subscription: WebhookSubscription = Depends(verify_api_key)):
    """Regenerate webhook secret for current subscription."""
    db = get_db_sync()
    try:
        subscription.webhook_secret = generate_webhook_secret()
        subscription.updated_at = datetime.utcnow()
        db.commit()

        return {"webhook_secret": subscription.webhook_secret}

    finally:
        db.close()


async def send_verification_webhook(subscription_id: int):
    """Send verification webhook to new subscriber."""
    from webhooks.events import WebhookEvent, WebhookEventType
    from webhooks.event_bus import event_bus

    # Create verification event
    verification_event = WebhookEvent(
        event_type=WebhookEventType.CONTRACT_DEPLOYED,
        blockchain="ethereum",
        data={
            "verification": True,
            "message": "Welcome to Web3 Gaming Webhooks!",
            "subscription_id": subscription_id,
            "timestamp": datetime.utcnow().isoformat()
        }
    )

    # Emit verification event
    await event_bus.emit(verification_event)
