"""
Webhook administration and monitoring endpoints.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session
from sqlalchemy import func, text

from models.base import get_db_sync
from models.gaming import WebhookSubscription, WebhookDelivery
from webhooks.manager import webhook_manager
from webhooks.event_bus import event_bus


logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/admin/webhooks", tags=["webhook-admin"])


# Pydantic Models

class WebhookStats(BaseModel):
    """Webhook system statistics."""
    total_subscriptions: int
    active_subscriptions: int
    verified_subscriptions: int
    total_deliveries: int
    successful_deliveries: int
    failed_deliveries: int
    average_response_time_ms: Optional[float]
    events_in_queue: int
    active_workers: int
    last_24h_deliveries: int
    success_rate_percent: float


class SubscriptionStats(BaseModel):
    """Individual subscription statistics."""
    id: int
    subscriber_name: str
    subscriber_url: str
    is_active: bool
    is_verified: bool
    total_deliveries: int
    successful_deliveries: int
    failed_deliveries: int
    success_rate_percent: float
    last_delivery_at: Optional[datetime]
    avg_response_time_ms: Optional[float]
    created_at: datetime


class EventTypeStats(BaseModel):
    """Statistics by event type."""
    event_type: str
    total_events: int
    successful_deliveries: int
    failed_deliveries: int
    avg_significance_score: Optional[float]
    avg_response_time_ms: Optional[float]


class GamingCategoryStats(BaseModel):
    """Statistics by gaming category."""
    gaming_category: str
    total_events: int
    successful_deliveries: int
    failed_deliveries: int
    avg_significance_score: Optional[float]
    unique_subscribers: int


class DeliveryTrend(BaseModel):
    """Delivery trend data."""
    date: str
    total_deliveries: int
    successful_deliveries: int
    failed_deliveries: int
    avg_response_time_ms: Optional[float]


# Admin Authentication (simplified - in production use proper admin auth)
def verify_admin_access():
    """Verify admin access - simplified for demo."""
    # In production, implement proper admin authentication
    return True


# Statistics Endpoints

@router.get("/stats", response_model=WebhookStats)
async def get_webhook_stats():
    """Get overall webhook system statistics."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        # Get subscription counts
        total_subs = db.query(WebhookSubscription).count()
        active_subs = db.query(WebhookSubscription).filter(WebhookSubscription.is_active == True).count()
        verified_subs = db.query(WebhookSubscription).filter(
            WebhookSubscription.is_active == True,
            WebhookSubscription.is_verified == True
        ).count()
        
        # Get delivery counts
        total_deliveries = db.query(WebhookDelivery).count()
        successful_deliveries = db.query(WebhookDelivery).filter(
            WebhookDelivery.delivery_status == "DELIVERED"
        ).count()
        failed_deliveries = db.query(WebhookDelivery).filter(
            WebhookDelivery.delivery_status == "FAILED"
        ).count()
        
        # Get average response time
        avg_response_time = db.query(func.avg(WebhookDelivery.response_time_ms)).filter(
            WebhookDelivery.delivery_status == "DELIVERED"
        ).scalar()
        
        # Get last 24h deliveries
        yesterday = datetime.utcnow() - timedelta(days=1)
        last_24h_deliveries = db.query(WebhookDelivery).filter(
            WebhookDelivery.created_at >= yesterday
        ).count()
        
        # Calculate success rate
        success_rate = (successful_deliveries / total_deliveries * 100) if total_deliveries > 0 else 0
        
        # Get system stats
        system_stats = webhook_manager.get_stats()
        event_bus_stats = event_bus.get_stats()
        
        return WebhookStats(
            total_subscriptions=total_subs,
            active_subscriptions=active_subs,
            verified_subscriptions=verified_subs,
            total_deliveries=total_deliveries,
            successful_deliveries=successful_deliveries,
            failed_deliveries=failed_deliveries,
            average_response_time_ms=avg_response_time,
            events_in_queue=system_stats.get("queue_size", 0),
            active_workers=system_stats.get("active_workers", 0),
            last_24h_deliveries=last_24h_deliveries,
            success_rate_percent=round(success_rate, 2)
        )
        
    finally:
        db.close()


@router.get("/subscriptions/stats", response_model=List[SubscriptionStats])
async def get_subscription_stats(
    limit: int = Query(50, ge=1, le=500),
    offset: int = Query(0, ge=0)
):
    """Get statistics for all subscriptions."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        # Use the subscription_performance view
        result = db.execute(text("""
            SELECT 
                ws.id,
                ws.subscriber_name,
                ws.subscriber_url,
                ws.is_active,
                ws.is_verified,
                ws.total_deliveries,
                ws.successful_deliveries,
                ws.failed_deliveries,
                CASE 
                    WHEN ws.total_deliveries > 0 
                    THEN ROUND(CAST(ws.successful_deliveries AS NUMERIC) / ws.total_deliveries * 100, 2)
                    ELSE 0 
                END as success_rate_percent,
                ws.last_delivery_at,
                AVG(wd.response_time_ms) as avg_response_time_ms,
                ws.created_at
            FROM webhook_subscriptions ws
            LEFT JOIN webhook_deliveries wd ON ws.subscriber_url = wd.subscriber_url 
                AND wd.delivery_status = 'DELIVERED'
            GROUP BY 
                ws.id, ws.subscriber_name, ws.subscriber_url, ws.is_active,
                ws.is_verified, ws.total_deliveries, ws.successful_deliveries, 
                ws.failed_deliveries, ws.last_delivery_at, ws.created_at
            ORDER BY ws.total_deliveries DESC
            LIMIT :limit OFFSET :offset
        """), {"limit": limit, "offset": offset})
        
        subscriptions = []
        for row in result:
            subscriptions.append(SubscriptionStats(
                id=row[0],
                subscriber_name=row[1],
                subscriber_url=row[2],
                is_active=row[3],
                is_verified=row[4],
                total_deliveries=row[5],
                successful_deliveries=row[6],
                failed_deliveries=row[7],
                success_rate_percent=float(row[8]) if row[8] else 0.0,
                last_delivery_at=row[9],
                avg_response_time_ms=float(row[10]) if row[10] else None,
                created_at=row[11]
            ))
        
        return subscriptions
        
    finally:
        db.close()


@router.get("/events/stats", response_model=List[EventTypeStats])
async def get_event_type_stats():
    """Get statistics by event type."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        result = db.execute(text("""
            SELECT 
                event_type,
                COUNT(*) as total_events,
                SUM(CASE WHEN delivery_status = 'DELIVERED' THEN 1 ELSE 0 END) as successful_deliveries,
                SUM(CASE WHEN delivery_status = 'FAILED' THEN 1 ELSE 0 END) as failed_deliveries,
                AVG(significance_score) as avg_significance_score,
                AVG(CASE WHEN delivery_status = 'DELIVERED' THEN response_time_ms END) as avg_response_time_ms
            FROM webhook_deliveries 
            WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY event_type
            ORDER BY total_events DESC
        """))
        
        stats = []
        for row in result:
            stats.append(EventTypeStats(
                event_type=row[0],
                total_events=row[1],
                successful_deliveries=row[2],
                failed_deliveries=row[3],
                avg_significance_score=float(row[4]) if row[4] else None,
                avg_response_time_ms=float(row[5]) if row[5] else None
            ))
        
        return stats
        
    finally:
        db.close()


@router.get("/gaming/stats", response_model=List[GamingCategoryStats])
async def get_gaming_category_stats():
    """Get statistics by gaming category."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        result = db.execute(text("""
            SELECT 
                gaming_category,
                COUNT(*) as total_events,
                SUM(CASE WHEN delivery_status = 'DELIVERED' THEN 1 ELSE 0 END) as successful_deliveries,
                SUM(CASE WHEN delivery_status = 'FAILED' THEN 1 ELSE 0 END) as failed_deliveries,
                AVG(significance_score) as avg_significance_score,
                COUNT(DISTINCT subscriber_url) as unique_subscribers
            FROM webhook_deliveries 
            WHERE gaming_category IS NOT NULL 
                AND created_at >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY gaming_category
            ORDER BY total_events DESC
        """))
        
        stats = []
        for row in result:
            stats.append(GamingCategoryStats(
                gaming_category=row[0],
                total_events=row[1],
                successful_deliveries=row[2],
                failed_deliveries=row[3],
                avg_significance_score=float(row[4]) if row[4] else None,
                unique_subscribers=row[5]
            ))
        
        return stats
        
    finally:
        db.close()


@router.get("/trends", response_model=List[DeliveryTrend])
async def get_delivery_trends(days: int = Query(30, ge=1, le=90)):
    """Get delivery trends over time."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        result = db.execute(text("""
            SELECT 
                DATE(created_at) as delivery_date,
                COUNT(*) as total_deliveries,
                SUM(CASE WHEN delivery_status = 'DELIVERED' THEN 1 ELSE 0 END) as successful_deliveries,
                SUM(CASE WHEN delivery_status = 'FAILED' THEN 1 ELSE 0 END) as failed_deliveries,
                AVG(CASE WHEN delivery_status = 'DELIVERED' THEN response_time_ms END) as avg_response_time_ms
            FROM webhook_deliveries 
            WHERE created_at >= CURRENT_DATE - INTERVAL ':days days'
            GROUP BY DATE(created_at)
            ORDER BY delivery_date DESC
        """), {"days": days})
        
        trends = []
        for row in result:
            trends.append(DeliveryTrend(
                date=row[0].strftime("%Y-%m-%d"),
                total_deliveries=row[1],
                successful_deliveries=row[2],
                failed_deliveries=row[3],
                avg_response_time_ms=float(row[4]) if row[4] else None
            ))
        
        return trends
        
    finally:
        db.close()


# Management Endpoints

@router.post("/subscriptions/{subscription_id}/verify")
async def verify_subscription(subscription_id: int):
    """Manually verify a subscription."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        subscription.is_verified = True
        subscription.updated_at = datetime.utcnow()
        db.commit()
        
        return {"message": "Subscription verified successfully"}
        
    finally:
        db.close()


@router.post("/subscriptions/{subscription_id}/deactivate")
async def deactivate_subscription(subscription_id: int):
    """Deactivate a subscription."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    db = get_db_sync()
    try:
        subscription = db.query(WebhookSubscription).filter(
            WebhookSubscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        subscription.is_active = False
        subscription.updated_at = datetime.utcnow()
        db.commit()
        
        return {"message": "Subscription deactivated successfully"}
        
    finally:
        db.close()


@router.get("/system/status")
async def get_system_status():
    """Get webhook system status."""
    if not verify_admin_access():
        raise HTTPException(status_code=403, detail="Admin access required")
    
    webhook_stats = webhook_manager.get_stats()
    event_bus_stats = event_bus.get_stats()
    
    return {
        "webhook_manager": webhook_stats,
        "event_bus": event_bus_stats,
        "timestamp": datetime.utcnow().isoformat()
    }
