[workspace]
members = [
    "crates/blockchain-engine",
    "crates/api-server",
    "crates/data-models",
    "crates/database",
    "crates/analytics-engine",
    "crates/real-time-monitor",
    "crates/contract-analyzer",
    "crates/shared-utils",
    "crates/data-scraper",
    "standalone-api"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["Web3 Gaming Intelligence Team"]
license = "MIT"
repository = "https://github.com/your-org/web3-gaming-tracker"

[workspace.dependencies]
# Async runtime
tokio = { version = "1.35", features = ["full"] }
tokio-util = "0.7"

# Web framework and HTTP
axum = { version = "0.7", features = ["macros", "ws"] }
hyper = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }
tower = { version = "0.4", features = ["full", "limit", "timeout"] }
tower-http = { version = "0.5", features = ["cors", "trace", "compression-gzip", "compression-br", "compression-deflate"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json"] }
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# Blockchain
ethers = { version = "2.0", features = ["rustls", "ws"] }
web3 = "0.19"

# Solana - using HTTP client approach to avoid dependency conflicts
# We'll implement Solana support via JSON-RPC instead of the SDK
base64 = "0.21"
bs58 = "0.5"

# WebSocket
tokio-tungstenite = { version = "0.21", features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3"

# Crypto and security
jsonwebtoken = "9.2"
bcrypt = "0.15"
uuid = { version = "1.6", features = ["v4", "serde"] }

# Error handling
thiserror = "1.0"

# Logging and monitoring
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
metrics = "0.22"
metrics-exporter-prometheus = "0.13"

# Error handling
anyhow = "1.0"

# Time and dates
chrono = { version = "0.4", features = ["serde"] }

# Configuration
config = "0.14"
dotenvy = "0.15"

# Testing
mockall = "0.12"
wiremock = "0.5"

# Performance and memory
rayon = "1.8"
dashmap = "5.5"
parking_lot = "0.12"

# ML and data processing (removed for now due to dependency conflicts)
# candle-core = "0.3"
# candle-nn = "0.3"
# ndarray = "0.15"

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

[profile.release]
opt-level = 3
debug = false
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.bench]
opt-level = 3
debug = false
lto = true
