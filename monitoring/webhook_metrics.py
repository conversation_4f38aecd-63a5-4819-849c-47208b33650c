"""
Advanced webhook monitoring and Prometheus metrics.
"""

import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, generate_latest
import asyncio
from dataclasses import dataclass

from webhooks.enhanced_manager import enhanced_webhook_manager
from webhooks.performance_optimizer import performance_optimizer
from models.base import get_db_sync
from sqlalchemy import text

logger = logging.getLogger(__name__)


@dataclass
class AlertThreshold:
    """Alert threshold configuration"""
    metric_name: str
    threshold_value: float
    comparison: str  # 'gt', 'lt', 'eq'
    severity: str  # 'critical', 'warning', 'info'
    description: str


class WebhookMetricsCollector:
    """Advanced webhook metrics collector for Prometheus"""
    
    def __init__(self):
        # Create custom registry
        self.registry = CollectorRegistry()
        
        # Webhook delivery metrics
        self.webhook_deliveries_total = Counter(
            'webhook_deliveries_total',
            'Total number of webhook deliveries',
            ['status', 'event_type', 'gaming_category'],
            registry=self.registry
        )
        
        self.webhook_delivery_duration = Histogram(
            'webhook_delivery_duration_seconds',
            'Time spent delivering webhooks',
            ['status', 'event_type'],
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0],
            registry=self.registry
        )
        
        self.webhook_processing_duration = Histogram(
            'webhook_processing_duration_seconds',
            'Time spent processing webhook events',
            ['event_type', 'gaming_category'],
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        # System performance metrics
        self.webhook_queue_size = Gauge(
            'webhook_queue_size',
            'Number of webhooks in processing queue',
            registry=self.registry
        )
        
        self.webhook_active_subscriptions = Gauge(
            'webhook_active_subscriptions',
            'Number of active webhook subscriptions',
            registry=self.registry
        )
        
        self.webhook_cache_hit_rate = Gauge(
            'webhook_cache_hit_rate',
            'Cache hit rate for webhook operations',
            registry=self.registry
        )
        
        self.webhook_database_query_duration = Histogram(
            'webhook_database_query_duration_seconds',
            'Time spent on database queries',
            ['query_type'],
            buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0],
            registry=self.registry
        )
        
        # Gaming-specific metrics
        self.gaming_events_classified = Counter(
            'gaming_events_classified_total',
            'Total number of gaming events classified',
            ['category', 'action', 'blockchain'],
            registry=self.registry
        )
        
        self.gaming_significance_score = Histogram(
            'gaming_significance_score',
            'Distribution of gaming event significance scores',
            ['category', 'action'],
            buckets=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
            registry=self.registry
        )
        
        self.gaming_value_usd = Histogram(
            'gaming_value_usd',
            'Distribution of gaming event values in USD',
            ['category', 'blockchain'],
            buckets=[1, 10, 50, 100, 500, 1000, 5000, 10000, 50000],
            registry=self.registry
        )
        
        # Error tracking
        self.webhook_errors_total = Counter(
            'webhook_errors_total',
            'Total number of webhook errors',
            ['error_type', 'component'],
            registry=self.registry
        )
        
        # Rate limiting metrics
        self.webhook_rate_limited = Counter(
            'webhook_rate_limited_total',
            'Total number of rate limited requests',
            ['subscriber_url'],
            registry=self.registry
        )
        
        # System info
        self.webhook_system_info = Info(
            'webhook_system_info',
            'Webhook system information',
            registry=self.registry
        )
        
        # Alert thresholds
        self.alert_thresholds = [
            AlertThreshold(
                'webhook_delivery_failure_rate',
                0.05,  # 5% failure rate
                'gt',
                'warning',
                'Webhook delivery failure rate is above 5%'
            ),
            AlertThreshold(
                'webhook_delivery_failure_rate',
                0.15,  # 15% failure rate
                'gt',
                'critical',
                'Webhook delivery failure rate is above 15%'
            ),
            AlertThreshold(
                'webhook_average_processing_time',
                5.0,  # 5 seconds
                'gt',
                'warning',
                'Average webhook processing time is above 5 seconds'
            ),
            AlertThreshold(
                'webhook_queue_size',
                1000,
                'gt',
                'critical',
                'Webhook queue size is above 1000'
            ),
            AlertThreshold(
                'webhook_cache_hit_rate',
                0.8,  # 80%
                'lt',
                'warning',
                'Webhook cache hit rate is below 80%'
            )
        ]
        
        # Initialize system info
        self.webhook_system_info.info({
            'version': '1.0.0',
            'component': 'webhook_system',
            'environment': 'production'
        })
    
    def record_delivery(self, status: str, event_type: str, gaming_category: str, 
                       duration_seconds: float):
        """Record webhook delivery metrics"""
        self.webhook_deliveries_total.labels(
            status=status,
            event_type=event_type,
            gaming_category=gaming_category or 'unknown'
        ).inc()
        
        self.webhook_delivery_duration.labels(
            status=status,
            event_type=event_type
        ).observe(duration_seconds)
    
    def record_processing(self, event_type: str, gaming_category: str, 
                         duration_seconds: float):
        """Record webhook processing metrics"""
        self.webhook_processing_duration.labels(
            event_type=event_type,
            gaming_category=gaming_category or 'unknown'
        ).observe(duration_seconds)
    
    def record_gaming_classification(self, category: str, action: str, 
                                   blockchain: str, significance_score: float,
                                   value_usd: Optional[float] = None):
        """Record gaming event classification metrics"""
        self.gaming_events_classified.labels(
            category=category,
            action=action,
            blockchain=blockchain
        ).inc()
        
        self.gaming_significance_score.labels(
            category=category,
            action=action
        ).observe(significance_score)
        
        if value_usd is not None:
            self.gaming_value_usd.labels(
                category=category,
                blockchain=blockchain
            ).observe(value_usd)
    
    def record_error(self, error_type: str, component: str):
        """Record webhook error"""
        self.webhook_errors_total.labels(
            error_type=error_type,
            component=component
        ).inc()
    
    def record_rate_limit(self, subscriber_url: str):
        """Record rate limiting event"""
        self.webhook_rate_limited.labels(
            subscriber_url=subscriber_url
        ).inc()
    
    def record_database_query(self, query_type: str, duration_seconds: float):
        """Record database query metrics"""
        self.webhook_database_query_duration.labels(
            query_type=query_type
        ).observe(duration_seconds)
    
    async def update_system_metrics(self):
        """Update system-level metrics"""
        try:
            # Get performance metrics
            perf_metrics = await performance_optimizer.get_performance_metrics()
            
            # Update gauges
            self.webhook_queue_size.set(perf_metrics.queue_size)
            self.webhook_cache_hit_rate.set(perf_metrics.cache_hit_rate)
            
            # Get active subscriptions count
            db = get_db_sync()
            try:
                result = db.execute(text("""
                    SELECT COUNT(*) FROM webhook_subscriptions 
                    WHERE is_active = true AND is_verified = true
                """))
                active_subs = result.scalar() or 0
                self.webhook_active_subscriptions.set(active_subs)
            finally:
                db.close()
            
        except Exception as e:
            logger.error(f"❌ Failed to update system metrics: {e}")
            self.record_error('system_metrics_update', 'metrics_collector')
    
    async def check_alerts(self) -> List[Dict[str, Any]]:
        """Check alert thresholds and return active alerts"""
        alerts = []
        
        try:
            # Get current metrics
            perf_metrics = await performance_optimizer.get_performance_metrics()
            stats = await enhanced_webhook_manager.get_performance_stats()
            
            # Calculate derived metrics
            delivery_stats = stats.get('delivery_stats', {})
            total_attempts = delivery_stats.get('total_attempts', 0)
            failed_deliveries = delivery_stats.get('failed_deliveries', 0)
            
            failure_rate = (failed_deliveries / max(total_attempts, 1))
            avg_processing_time = perf_metrics.average_processing_time_ms / 1000.0  # Convert to seconds
            
            # Check thresholds
            current_metrics = {
                'webhook_delivery_failure_rate': failure_rate,
                'webhook_average_processing_time': avg_processing_time,
                'webhook_queue_size': perf_metrics.queue_size,
                'webhook_cache_hit_rate': perf_metrics.cache_hit_rate
            }
            
            for threshold in self.alert_thresholds:
                metric_value = current_metrics.get(threshold.metric_name)
                if metric_value is None:
                    continue
                
                alert_triggered = False
                if threshold.comparison == 'gt' and metric_value > threshold.threshold_value:
                    alert_triggered = True
                elif threshold.comparison == 'lt' and metric_value < threshold.threshold_value:
                    alert_triggered = True
                elif threshold.comparison == 'eq' and metric_value == threshold.threshold_value:
                    alert_triggered = True
                
                if alert_triggered:
                    alerts.append({
                        'metric': threshold.metric_name,
                        'current_value': metric_value,
                        'threshold_value': threshold.threshold_value,
                        'severity': threshold.severity,
                        'description': threshold.description,
                        'timestamp': datetime.utcnow().isoformat()
                    })
            
        except Exception as e:
            logger.error(f"❌ Failed to check alerts: {e}")
            alerts.append({
                'metric': 'alert_system',
                'current_value': 'error',
                'threshold_value': 'healthy',
                'severity': 'critical',
                'description': f'Alert system error: {str(e)}',
                'timestamp': datetime.utcnow().isoformat()
            })
        
        return alerts
    
    def get_metrics(self) -> str:
        """Get Prometheus metrics in text format"""
        return generate_latest(self.registry).decode('utf-8')
    
    async def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        try:
            # Get performance metrics
            perf_metrics = await performance_optimizer.get_performance_metrics()
            stats = await enhanced_webhook_manager.get_performance_stats()
            health = await enhanced_webhook_manager.health_check()
            alerts = await check_alerts()
            
            # Get database statistics
            db = get_db_sync()
            try:
                # Delivery statistics for last 24 hours
                delivery_stats_result = db.execute(text("""
                    SELECT 
                        event_type,
                        gaming_category,
                        COUNT(*) as total,
                        COUNT(*) FILTER (WHERE delivery_status = 'DELIVERED') as successful,
                        COUNT(*) FILTER (WHERE delivery_status = 'FAILED') as failed,
                        AVG(response_time_ms) as avg_response_time
                    FROM webhook_deliveries 
                    WHERE created_at >= NOW() - INTERVAL '24 hours'
                    GROUP BY event_type, gaming_category
                    ORDER BY total DESC
                """))
                
                delivery_stats = []
                for row in delivery_stats_result:
                    delivery_stats.append({
                        'event_type': row[0],
                        'gaming_category': row[1],
                        'total': row[2],
                        'successful': row[3],
                        'failed': row[4],
                        'success_rate': (row[3] / row[2]) if row[2] > 0 else 0,
                        'avg_response_time': float(row[5]) if row[5] else 0
                    })
                
                # Top gaming categories
                gaming_stats_result = db.execute(text("""
                    SELECT 
                        gaming_category,
                        COUNT(*) as event_count,
                        AVG(significance_score) as avg_significance
                    FROM webhook_deliveries 
                    WHERE gaming_category IS NOT NULL 
                        AND created_at >= NOW() - INTERVAL '24 hours'
                    GROUP BY gaming_category
                    ORDER BY event_count DESC
                    LIMIT 10
                """))
                
                gaming_stats = []
                for row in gaming_stats_result:
                    gaming_stats.append({
                        'category': row[0],
                        'event_count': row[1],
                        'avg_significance': float(row[2]) if row[2] else 0
                    })
                
            finally:
                db.close()
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'system_health': health,
                'performance_metrics': {
                    'total_events_processed': perf_metrics.total_events_processed,
                    'average_processing_time_ms': perf_metrics.average_processing_time_ms,
                    'average_delivery_time_ms': perf_metrics.average_delivery_time_ms,
                    'cache_hit_rate': perf_metrics.cache_hit_rate,
                    'queue_size': perf_metrics.queue_size,
                    'successful_deliveries': perf_metrics.successful_deliveries,
                    'failed_deliveries': perf_metrics.failed_deliveries
                },
                'delivery_statistics': delivery_stats,
                'gaming_statistics': gaming_stats,
                'alerts': alerts,
                'system_info': {
                    'uptime_seconds': time.time() - stats.get('start_time', time.time()),
                    'version': '1.0.0',
                    'environment': 'production'
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get dashboard data: {e}")
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e),
                'system_health': {'status': 'unhealthy'}
            }


# Global metrics collector instance
webhook_metrics = WebhookMetricsCollector()


async def check_alerts() -> List[Dict[str, Any]]:
    """Check webhook system alerts"""
    return await webhook_metrics.check_alerts()


async def update_metrics():
    """Update all webhook metrics"""
    await webhook_metrics.update_system_metrics()
