# API Endpoint Reference Guide

## Quick Reference

### Base URL
- **Development**: `http://localhost:8000`
- **Production**: `https://api.web3gaming-tracker.com`

### Authentication
- **Current**: No authentication required (development mode)
- **Production**: API key authentication via `X-API-Key` header

---

## Core System Endpoints

### Health & Status
```http
GET /health
GET /stats
```

### Articles & News
```http
GET /api/v1/articles
GET /api/v1/articles/{id}
```

### Gaming Projects
```http
GET /api/v1/gaming
GET /api/v1/gaming/{project_id}
```

### Blockchain Data
```http
GET /api/v1/blockchain/data/test-connections
GET /api/v1/blockchain/analytics/{protocol}
```

---

## Phase 7: Content Intelligence Endpoints

### Content Classification
```http
POST /api/v1/content-intelligence/classify
```
**Purpose**: Classify gaming content into 11 categories using advanced NLP
**Input**: Article title, content, summary, source
**Output**: Primary category, confidence scores, sentiment, entities

### Sentiment Analysis
```http
POST /api/v1/content-intelligence/sentiment
```
**Purpose**: Multi-dimensional sentiment analysis (community, market, technical)
**Input**: Article title and content
**Output**: Sentiment breakdown, confidence scores, emotional indicators

### Trend Detection
```http
POST /api/v1/content-intelligence/trends
```
**Purpose**: Identify emerging and declining gaming trends
**Input**: Multiple content items, timeframe
**Output**: Trend strength, growth rates, affected projects

### Market Intelligence
```http
POST /api/v1/content-intelligence/market-intelligence
```
**Purpose**: Generate investment signals and risk assessments
**Input**: Content items, analysis depth
**Output**: Market insights, investment signals, risk metrics

### Entity Recognition
```http
POST /api/v1/content-intelligence/entities
```
**Purpose**: Extract gaming projects, tokens, and blockchain entities
**Input**: Text content, relationship extraction flag
**Output**: Entities with confidence scores and relationships

---

## Phase 7: Market Analytics Endpoints

### Sector Analysis
```http
GET /api/v1/content-intelligence/market/sector-analysis
```
**Purpose**: Cross-protocol gaming sector performance analysis
**Parameters**: timeframe, protocols
**Output**: Performance scores, market cap changes, user growth

### Portfolio Tracking
```http
POST /api/v1/content-intelligence/market/portfolio-tracking
```
**Purpose**: Track gaming token portfolio performance
**Input**: Portfolio holdings, benchmark
**Output**: Performance metrics, risk analysis, asset breakdown

### Market Alerts
```http
GET /api/v1/content-intelligence/market/alerts
```
**Purpose**: Retrieve active market alerts and notifications
**Parameters**: alert_type, severity, active_only
**Output**: Alert list, severity breakdown, trigger details

---

## Phase 7: Competitive Analysis Endpoints

### Competitive Landscape
```http
GET /api/v1/content-intelligence/competitive/landscape
```
**Purpose**: 8-metric competitive analysis framework
**Parameters**: category, blockchain, timeframe
**Output**: Market share, innovation scores, competitive metrics

### Project Comparison
```http
GET /api/v1/content-intelligence/competitive/compare/{project_a}/{project_b}
```
**Purpose**: Head-to-head project comparison
**Parameters**: Two project names in URL path
**Output**: Metric-by-metric comparison, competitive advantages

### Market Rankings
```http
GET /api/v1/content-intelligence/competitive/rankings
```
**Purpose**: Gaming project rankings by various metrics
**Parameters**: metric_type, category, limit
**Output**: Ranked project list, scores, market positioning

---

## Request/Response Examples

### Content Classification Example
```bash
curl -X POST "http://localhost:8000/api/v1/content-intelligence/classify" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "New Gaming Project Launches on Solana",
    "content": "A revolutionary new gaming project has launched on Solana...",
    "source": "gaming-news-site"
  }'
```

**Response:**
```json
{
  "primary_category": "project_launch",
  "category_confidence": 0.92,
  "sentiment_score": 0.75,
  "gaming_entities": ["Solana", "NFT", "DeFi"],
  "blockchain_networks": ["solana"],
  "classification_timestamp": "2025-07-07T12:34:56.789Z"
}
```

### Sentiment Analysis Example
```bash
curl -X POST "http://localhost:8000/api/v1/content-intelligence/sentiment" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Gaming Community Reaction",
    "content": "The gaming community is excited about the new features..."
  }'
```

**Response:**
```json
{
  "overall_sentiment": "positive",
  "sentiment_score": 0.78,
  "sentiment_breakdown": {
    "community_sentiment": 0.82,
    "market_sentiment": 0.75,
    "technical_sentiment": 0.71
  },
  "confidence_score": 0.91
}
```

### Competitive Landscape Example
```bash
curl "http://localhost:8000/api/v1/content-intelligence/competitive/landscape?category=metaverse"
```

**Response:**
```json
{
  "competitive_metrics": {
    "The Sandbox": {
      "market_share": 0.18,
      "user_adoption_score": 0.75,
      "innovation_score": 0.88,
      "overall_score": 0.81
    },
    "Decentraland": {
      "market_share": 0.15,
      "user_adoption_score": 0.70,
      "innovation_score": 0.75,
      "overall_score": 0.73
    }
  },
  "market_leaders": ["The Sandbox", "Decentraland"]
}
```

---

## Error Handling

All endpoints use standardized error responses:

```json
{
  "error": true,
  "error_id": "ERR_20250707_123456_abc12345",
  "category": "validation",
  "severity": "low",
  "message": "User-friendly error message",
  "timestamp": "2025-07-07T12:34:56.789Z"
}
```

### Common Error Codes
- `400` - Bad Request (validation error)
- `401` - Unauthorized (missing/invalid API key)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

---

## Rate Limits

| Endpoint Category | Requests/Hour | Burst Limit |
|------------------|---------------|-------------|
| General API | 1000 | 100/min |
| Content Intelligence | 100 | 10/min |
| Market Analytics | 200 | 20/min |
| Blockchain Data | 500 | 50/min |
| Competitive Analysis | 150 | 15/min |

---

## Testing Tools

### Postman Collection
Import `docs/postman_collection.json` into Postman for interactive API testing.

### OpenAPI Specification
Use `docs/openapi.json` or `docs/openapi.yaml` for:
- Code generation
- API testing tools
- Documentation generation
- Client SDK development

### Interactive Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

---

## Phase 7 Feature Highlights

### Advanced NLP Classification
- **11 Categories**: project_launch, market_analysis, technical_update, partnership, funding, regulation, adoption, innovation, community, security, general
- **Confidence Scoring**: 0.0-1.0 confidence levels for all classifications
- **Multi-label Support**: Content can belong to multiple categories

### Multi-dimensional Sentiment
- **Community Sentiment**: Social media and forum reactions
- **Market Sentiment**: Price and trading activity sentiment
- **Technical Sentiment**: Development and technical progress sentiment
- **Adoption Sentiment**: User adoption and ecosystem growth sentiment

### Competitive Analysis Framework
- **8 Core Metrics**: Market share, user adoption, innovation, community strength, technical advancement, partnerships, tokenomics, development activity
- **Overall Scoring**: Weighted composite scores for project comparison
- **Market Positioning**: Leader/challenger/niche player classification

### Market Intelligence
- **Investment Signals**: Bullish/bearish/neutral indicators with reasoning
- **Risk Assessment**: Market, technical, and regulatory risk scoring
- **Trend Detection**: Momentum and volume analysis
- **Cross-chain Analysis**: Multi-blockchain ecosystem insights

---

## Support & Resources

- **Documentation**: This guide and `/docs` endpoint
- **Issues**: GitHub repository issue tracker
- **API Status**: Monitor at `/health` endpoint
- **Rate Limit Status**: Check response headers for current usage
