# Discord Web3 Gaming Server Scraper

## Overview

This tool discovers and monitors Discord servers related to web3, blockchain, and crypto gaming. Due to Discord API limitations, it uses multiple strategies to find relevant servers while respecting Discord's terms of service.

## Features

### 🔍 Server Discovery
- **Bot Monitoring**: Analyzes servers the bot is already member of
- **Invite Link Analysis**: Discovers new servers through invite links in messages
- **Third-party Listings**: Scrapes Discord server listing websites
- **Keyword Matching**: Identifies servers using gaming and blockchain keywords

### 🎯 Target Focus
- **Blockchain Networks**: Solana, Ethereum, Avalanche, Base, BSC, TON
- **Gaming Categories**: P2E, GameFi, NFT Gaming, Metaverse, Guilds
- **Relevance Scoring**: Intelligent scoring system to filter relevant servers

### 📊 Data Collection
- Server metadata (name, description, member count)
- Channel analysis for gaming/blockchain keywords
- Invite URLs and server features
- Verification and boost levels
- Discovery source tracking

## Discord API Limitations

### What Discord API Cannot Do:
- ❌ Search for public servers by keywords
- ❌ Browse Discord's server discovery without being a member
- ❌ Access servers the bot hasn't been invited to
- ❌ Scrape Discord's internal server directory

### What This Tool Does Instead:
- ✅ Monitors servers the bot is already in
- ✅ Analyzes invite links shared in monitored servers
- ✅ Scrapes third-party Discord listing sites
- ✅ Uses keyword analysis to identify relevant content
- ✅ Tracks server metadata and changes over time

## Setup Instructions

### 1. Create Discord Bot

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name
3. Go to "Bot" section and click "Add Bot"
4. Copy the bot token (keep this secret!)
5. Enable required intents:
   - Server Members Intent (if needed)
   - Message Content Intent

### 2. Install Dependencies

```bash
# Run the setup script
python scripts/setup_discord_bot.py

# Or install manually
pip install discord.py>=2.3.0 aiohttp>=3.8.0 python-dotenv>=1.0.0
```

### 3. Configure Environment

```bash
# Copy the template
cp .env.discord .env

# Edit .env with your bot token
DISCORD_BOT_TOKEN=your_bot_token_here
DISCORD_CLIENT_ID=your_client_id_here
```

### 4. Generate Bot Invite URL

```bash
python scripts/setup_discord_bot.py --invite
```

### 5. Invite Bot to Servers

Use the generated invite URL to add your bot to:
- Gaming Discord servers you're already in
- Web3/crypto community servers
- Blockchain project servers
- Gaming guild servers

## Usage

### Running the Bot

```bash
# Start the Discord scraper
python scripts/discord_scraper.py
```

### Bot Commands

The bot runs automatically but also supports commands:

- `!scan` - Manually trigger server analysis
- `!stats` - Show discovery statistics
- `!export` - Export discovered servers to JSON
- `!help` - Show available commands

### Data Output

Discovered servers are saved to:
- `data/discord/discovered_servers.json` - Main server database
- `data/discord/discovered_invites.json` - Invite links found
- `logs/discord/bot.log` - Bot activity logs

## Configuration

### Keyword Customization

Edit `config/discord/bot_config.json` to customize:

```json
{
  "keywords": {
    "gaming": ["p2e", "gamefi", "nft game", "metaverse"],
    "blockchain": ["solana", "ethereum", "web3", "defi"],
    "target_blockchains": ["solana", "ethereum", "avalanche"]
  }
}
```

### Monitoring Settings

```json
{
  "monitoring": {
    "scan_interval_hours": 6,
    "message_analysis_interval_minutes": 30,
    "relevance_threshold": 0.3
  }
}
```

## Data Schema

### ServerInfo Structure

```json
{
  "id": 123456789,
  "name": "Crypto Gaming Guild",
  "description": "Play-to-earn gaming community",
  "member_count": 5420,
  "created_at": "2023-01-15T10:30:00Z",
  "invite_url": "https://discord.gg/example",
  "categories": ["gaming", "trading"],
  "blockchain_mentions": ["solana", "ethereum"],
  "gaming_keywords": ["p2e", "gamefi"],
  "verification_level": "medium",
  "boost_level": 2,
  "features": ["COMMUNITY", "NEWS"],
  "discovered_at": "2024-01-15T10:30:00Z",
  "source": "bot_joined"
}
```

## Relevance Scoring

The bot calculates relevance scores based on:

- **Server Name** (0.3 points per gaming keyword, 0.2 per blockchain)
- **Description** (0.2 points per gaming keyword, 0.15 per blockchain)
- **Channel Names** (0.1 points per keyword found)
- **Target Blockchains** (0.2 bonus for Solana, Ethereum, etc.)
- **Member Count** (0.1 bonus for 1K+, 0.2 for 10K+)
- **Server Features** (0.1 bonus for verified/community servers)

Servers with scores > 0.3 are considered relevant.

## Rate Limiting & Ethics

### Discord API Limits
- Message history: 50 messages per channel per scan
- Invite creation: Limited to prevent spam
- Server analysis: 6-hour intervals between scans

### Ethical Guidelines
- Only analyzes public information
- Respects Discord's Terms of Service
- No spam or unwanted bot behavior
- Rate limiting to prevent API abuse

## Troubleshooting

### Common Issues

**Bot not responding:**
```bash
# Check bot token
echo $DISCORD_BOT_TOKEN

# Verify bot permissions
# Bot needs: Read Messages, Send Messages, Read Message History
```

**No servers discovered:**
- Invite bot to more gaming/crypto servers
- Lower relevance threshold in config
- Check keyword lists for your target communities

**Rate limiting errors:**
- Increase scan intervals in config
- Reduce message analysis frequency
- Check Discord API status

### Logs and Debugging

```bash
# View bot logs
tail -f logs/discord/bot.log

# Check discovered servers
cat data/discord/discovered_servers.json | jq '.total_servers'

# Validate configuration
python -c "import json; print(json.load(open('config/discord/bot_config.json')))"
```

## Integration with Main Project

The Discord scraper integrates with the main web3 gaming news tracker:

```python
# Import discovered servers into main database
from scripts.discord_scraper import Web3GamingDiscordBot
from models.crud import create_news_source

# Add Discord servers as news sources
for server in discovered_servers:
    create_news_source(
        name=server.name,
        url=server.invite_url,
        source_type="discord",
        blockchain_focus=server.blockchain_mentions
    )
```

## Future Enhancements

- **Message Content Analysis**: Analyze message content for trending topics
- **User Activity Tracking**: Monitor active gaming discussions
- **Automated Joining**: Smart bot invitation to relevant servers
- **Sentiment Analysis**: Track community sentiment around games/projects
- **Integration APIs**: Connect with gaming project APIs for validation

## Legal and Compliance

- Complies with Discord Terms of Service
- Only accesses public server information
- Respects user privacy and data protection
- No unauthorized data collection or storage
- Rate limiting prevents service abuse
