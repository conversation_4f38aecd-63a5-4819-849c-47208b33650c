# Database Migration Management Guide

## Overview

This guide provides comprehensive documentation for managing database migrations in the Web3 Gaming News Tracker project. Our migration system uses Alembic with enhanced tooling for safety, validation, and rollback capabilities.

## Migration Management Tools

### 1. Migration Manager (`scripts/migration_manager.py`)

Comprehensive migration management with safety features:

```bash
# Check migration status
python scripts/migration_manager.py status

# Migrate to latest (with automatic backup)
python scripts/migration_manager.py migrate head

# Migrate to specific revision
python scripts/migration_manager.py migrate abc123def

# Rollback with confirmation
python scripts/migration_manager.py rollback previous_revision --confirm

# Generate new migration
python scripts/migration_manager.py generate "Add user preferences table"

# Create manual backup
python scripts/migration_manager.py backup --name "pre_major_update"

# Validate specific migration
python scripts/migration_manager.py validate abc123def
```

### 2. Migration Validator (`scripts/migration_validator.py`)

Comprehensive validation and testing:

```bash
# Run full validation suite
python scripts/migration_validator.py

# Validate specific migration file
python scripts/migration_validator.py --migration-file alembic/versions/abc123_add_table.py

# Test rollback capability
python scripts/migration_validator.py --test-rollback abc123def

# Generate validation report
python scripts/migration_validator.py --output validation_report.json
```

## Migration Workflow

### 1. Development Workflow

```bash
# 1. Make model changes in models/
# 2. Generate migration
python scripts/migration_manager.py generate "Descriptive migration message"

# 3. Review generated migration file
# 4. Validate migration
python scripts/migration_validator.py --migration-file path/to/new/migration.py

# 5. Test migration locally
python scripts/migration_manager.py migrate head

# 6. Test rollback
python scripts/migration_manager.py rollback previous_revision --confirm
python scripts/migration_manager.py migrate head  # Migrate back up
```

### 2. Production Deployment Workflow

```bash
# 1. Create production backup
python scripts/migration_manager.py --env production backup --name "pre_deployment_$(date +%Y%m%d)"

# 2. Validate current state
python scripts/migration_validator.py --env production

# 3. Run migration with backup
python scripts/migration_manager.py --env production migrate head

# 4. Validate post-migration
python scripts/migration_validator.py --env production

# 5. Monitor application health
```

## Migration Best Practices

### 1. Migration File Structure

Every migration should follow this structure:

```python
"""Descriptive migration message

Revision ID: abc123def
Revises: previous_revision
Create Date: 2025-01-07 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = 'abc123def'
down_revision = 'previous_revision'
branch_labels = None
depends_on = None

def upgrade():
    """Apply migration changes"""
    # Add your upgrade logic here
    pass

def downgrade():
    """Revert migration changes"""
    # Add your downgrade logic here
    pass
```

### 2. Safe Migration Patterns

#### Adding Columns
```python
def upgrade():
    # Safe: Add nullable column first
    op.add_column('users', sa.Column('new_field', sa.String(255), nullable=True))
    
    # Optional: Populate with default values
    op.execute("UPDATE users SET new_field = 'default_value' WHERE new_field IS NULL")
    
    # Optional: Make non-nullable after population
    op.alter_column('users', 'new_field', nullable=False)

def downgrade():
    op.drop_column('users', 'new_field')
```

#### Dropping Columns
```python
def upgrade():
    # Safe: Drop column (ensure application doesn't use it first)
    op.drop_column('users', 'old_field')

def downgrade():
    # Restore column structure (data will be lost)
    op.add_column('users', sa.Column('old_field', sa.String(255), nullable=True))
```

#### Creating Indexes
```python
def upgrade():
    # Use CONCURRENTLY for large tables in production
    op.execute("CREATE INDEX CONCURRENTLY idx_users_email ON users(email)")

def downgrade():
    op.drop_index('idx_users_email', table_name='users')
```

### 3. Performance Considerations

#### Large Table Migrations
- Use `CREATE INDEX CONCURRENTLY` for index creation
- Consider batched updates for data migrations
- Test migration duration on production-sized data
- Plan maintenance windows for blocking operations

#### Data Migrations
```python
def upgrade():
    # Batch process large data updates
    connection = op.get_bind()
    
    batch_size = 1000
    offset = 0
    
    while True:
        result = connection.execute(f"""
            UPDATE users 
            SET processed = true 
            WHERE id IN (
                SELECT id FROM users 
                WHERE processed = false 
                LIMIT {batch_size} OFFSET {offset}
            )
        """)
        
        if result.rowcount == 0:
            break
            
        offset += batch_size
```

## Rollback Procedures

### 1. Automatic Rollback

```bash
# Rollback to previous revision
python scripts/migration_manager.py rollback -1 --confirm

# Rollback to specific revision
python scripts/migration_manager.py rollback abc123def --confirm
```

### 2. Manual Rollback

If automatic rollback fails:

```bash
# 1. Stop application
# 2. Restore from backup
pg_restore -h localhost -U username -d database_name backup_file.sql

# 3. Update alembic version table
psql -d database_name -c "UPDATE alembic_version SET version_num = 'target_revision';"

# 4. Restart application
```

## Backup and Recovery

### 1. Automated Backups

Backups are automatically created before migrations:
- Location: `backups/` directory
- Format: `backup_{environment}_{timestamp}.sql`
- Retention: Configure based on environment

### 2. Manual Backup Creation

```bash
# Create named backup
python scripts/migration_manager.py backup --name "before_major_feature"

# Create timestamped backup
python scripts/migration_manager.py backup
```

### 3. Backup Restoration

```bash
# Restore from backup file
pg_restore -h localhost -U username -d database_name -c backup_file.sql

# Update Alembic version to match restored state
python scripts/migration_manager.py status  # Check current state
```

## Validation and Testing

### 1. Pre-Migration Validation

```bash
# Validate current database state
python scripts/migration_validator.py

# Validate specific migration
python scripts/migration_validator.py --migration-file path/to/migration.py
```

### 2. Post-Migration Validation

```bash
# Comprehensive validation after migration
python scripts/migration_validator.py --output post_migration_report.json
```

### 3. Rollback Testing

```bash
# Test rollback capability
python scripts/migration_validator.py --test-rollback target_revision
```

## Environment-Specific Considerations

### Development Environment
- Frequent schema changes expected
- Fast iteration with minimal safety checks
- Local database can be reset if needed

### Staging Environment
- Mirror production migration process
- Full validation and testing
- Performance testing with production-sized data

### Production Environment
- Maximum safety measures
- Mandatory backups before migrations
- Comprehensive validation
- Rollback procedures tested
- Maintenance window planning

## Troubleshooting

### Common Issues

#### 1. Migration Conflicts
```bash
# Check for multiple heads
alembic heads

# Merge heads if needed
alembic merge -m "Merge conflicting migrations"
```

#### 2. Failed Migrations
```bash
# Check current state
python scripts/migration_manager.py status

# Rollback to last known good state
python scripts/migration_manager.py rollback last_good_revision --confirm

# Fix migration file and retry
```

#### 3. Schema Inconsistencies
```bash
# Validate schema
python scripts/migration_validator.py

# Generate corrective migration if needed
python scripts/migration_manager.py generate "Fix schema inconsistencies"
```

## Monitoring and Alerting

### 1. Migration Monitoring
- Track migration duration
- Monitor database performance during migrations
- Alert on failed migrations
- Log all migration activities

### 2. Health Checks
- Post-migration validation
- Application health verification
- Database performance monitoring
- Data integrity checks

## Security Considerations

### 1. Backup Security
- Encrypt backup files
- Secure backup storage
- Access control for backup files
- Regular backup testing

### 2. Migration Security
- Review migration files for security implications
- Validate data access patterns
- Ensure proper permissions
- Audit migration activities

---

**Last Updated**: 2025-01-07  
**Version**: 1.0  
**Maintainer**: Development Team
