# 🚀 Web3 Gaming Scraper - Quickstart Guide
*Created: 2025.07.15*

## 📋 Prerequisites

### Required Software
- **Docker & Docker Compose** - For database services
- **Python 3.8+** - For backend API
- **Node.js 16+** - For React dashboard
- **Git** - For version control

### Required Python Packages
```bash
pip install fastapi uvicorn sqlalchemy psycopg2-binary redis aioredis prometheus-client
```

## 🏃‍♂️ Quick Start Commands

### 1. Start Database Services
```bash
# Start PostgreSQL and Redis containers
docker-compose up -d postgres_gaming redis_gaming

# Verify containers are running
docker ps
```

### 2. Start Backend API
```bash
# Start the Python FastAPI backend
python start_minimal_api.py

# Verify API is running
curl http://localhost:8001/health
```

### 3. Start React Dashboard (Recommended)
```bash
# Navigate to React dashboard
cd dashboard/frontend

# Install dependencies (first time only)
npm install

# Start React development server
npm start

# Access dashboard at: http://localhost:3000
```

### 4. Alternative: HTML Monitoring Dashboard
```bash
# Open simple HTML dashboard in browser
open frontend/monitoring_dashboard.html
# Or navigate to: file:///path/to/frontend/monitoring_dashboard.html
```

## 🎯 Essential API Endpoints

### Health & Status
```bash
# Check API health
curl http://localhost:8001/health

# Get comprehensive system status
curl http://localhost:8001/status

# Get database table counts
curl http://localhost:8001/status | jq '.components.database.table_counts'
```

### Gaming Projects
```bash
# Get all gaming projects
curl http://localhost:8001/api/v1/gaming/projects

# Add a new gaming project
curl -X POST http://localhost:8001/api/v1/gaming/projects \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Test Game",
    "blockchain": "ethereum",
    "primary_genre": "Action",
    "project_website_link": "https://testgame.com"
  }'
```

### Blockchain Activity
```bash
# Get recent blockchain activity
curl http://localhost:8001/api/v1/blockchain/recent
```

### Webhooks
```bash
# Get webhook subscriptions
curl http://localhost:8001/api/v1/webhooks/subscriptions

# Get webhook deliveries
curl http://localhost:8001/api/v1/webhooks/deliveries
```

## 🎮 Dashboard Features

### React Dashboard (localhost:3000)
- **Professional Material-UI interface** with dark gaming theme
- **Multi-page navigation** with sidebar menu
- **Add New Game** - Comprehensive CBCC form integration
- **System Monitoring** - Real-time health and metrics
- **Gaming Analytics** - Project analytics and insights
- **Enhanced Analytics** - Advanced data visualization

### HTML Monitoring Dashboard
- **Quick Entry Form** - 5 essential fields for rapid game addition
- **Full CBCC Form** - Comprehensive 13-step gaming project form
- **Real-time Metrics** - Live system status and database counts
- **Auto-refresh** - Updates every 30 seconds

## 🔧 Development Commands

### Database Management
```bash
# Connect to PostgreSQL
docker exec -it postgres_gaming psql -U postgres -d gaming_tracker

# Check table structure
docker exec postgres_gaming psql -U postgres -d gaming_tracker -c "\dt"

# View gaming projects
docker exec postgres_gaming psql -U postgres -d gaming_tracker -c "SELECT project_name, blockchain, primary_genre FROM gaming_projects LIMIT 5;"
```

### Redis Management
```bash
# Connect to Redis
docker exec -it redis_gaming redis-cli

# Check Redis status
docker exec redis_gaming redis-cli ping
```

### Import Sample Data
```bash
# Import gaming projects from CSV
python import_gaming_csv_data.py
```

### Testing
```bash
# Test webhook functionality
python test_webhook_add_game.py

# Test API endpoints
python test_webhook_endpoints_simple.py
```

## 🚨 Troubleshooting

### Database Connection Issues
```bash
# Restart database containers
docker-compose restart postgres_gaming redis_gaming

# Check container logs
docker logs postgres_gaming
docker logs redis_gaming
```

### API Issues
```bash
# Check if port 8001 is in use
lsof -i :8001

# Restart API server
# Kill existing process and restart
python start_minimal_api.py
```

### React Dashboard Issues
```bash
# Clear npm cache and reinstall
cd dashboard/frontend
rm -rf node_modules package-lock.json
npm install
npm start
```

## 📊 Current System Status

### Database Contents
- **15 Gaming Projects** (11 real + 4 test projects)
- **20 Gaming Contracts** with token/NFT details
- **1 Webhook Subscription** for real-time updates
- **1 Blockchain Event** tracked

### Available Blockchains
- Ethereum, Solana, Polygon, Avalanche, BSC, Arbitrum, Base, Ronin, Immutable X, Flow, WAX

### Supported Genres
- Action, Strategy, RPG, Casual, Simulation, Shooter, Battle Royale, MMORPG, Racing, Sports, Puzzle, Adventure

## 🎯 Next Steps

1. **Explore React Dashboard** - Navigate through all pages and features
2. **Add Gaming Projects** - Use either quick form or comprehensive CBCC form
3. **Monitor System Health** - Check the System Monitoring page
4. **Test API Integration** - Try the various API endpoints
5. **Review Data** - Examine the imported gaming projects and contracts

## 🔗 Important URLs

- **React Dashboard**: http://localhost:3000
- **API Documentation**: http://localhost:8001/docs
- **API Health**: http://localhost:8001/health
- **System Status**: http://localhost:8001/status
- **HTML Dashboard**: file:///path/to/frontend/monitoring_dashboard.html

## 📝 Notes

- The system is **production-ready** with live data integration
- Both frontend interfaces work simultaneously
- All services auto-restart and include error handling
- Real-time monitoring provides system health visibility
- Database is persistent across container restarts
