# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/web3_gaming_news
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=web3_gaming_news
DATABASE_USER=user
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
DEBUG=true

# Blockchain RPC Endpoints
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/YOUR_PROJECT_ID
BSC_RPC_URL=https://bsc-dataseed.binance.org/
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
OPTIMISM_RPC_URL=https://mainnet.optimism.io
IMMUTABLE_RPC_URL=https://api.immutable.com
RONIN_RPC_URL=https://api.roninchain.com/rpc

# Web3 Configuration
WEB3_REQUEST_TIMEOUT=30
WEB3_MAX_RETRIES=3
BLOCK_CONFIRMATION_COUNT=12

# Social Media APIs
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret

REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=Web3GamingNewsBot/1.0

# News APIs
NEWS_API_KEY=your_news_api_key
GUARDIAN_API_KEY=your_guardian_api_key

# Gaming & NFT APIs
OPENSEA_API_KEY=your_opensea_api_key
COINGECKO_API_KEY=your_coingecko_api_key
DEFIPULSE_API_KEY=your_defipulse_api_key
DAPPRADAR_API_KEY=your_dappradar_api_key

# Blockchain Data APIs
FLIPSIDE_API_KEY=your_flipside_api_key
FLIPSIDE_BASE_URL=https://api.flipsidecrypto.com/api/v2

BITQUERY_API_KEY=rookietrader
BITQUERY_ACCESS_TOKEN=your_bitquery_access_token
BITQUERY_BASE_URL=https://graphql.bitquery.io

CRYPTORANK_API_KEY=your_cryptorank_api_key
CRYPTORANK_BASE_URL=https://api.cryptorank.io/v1

DEXTOOLS_API_KEY=your_dextools_api_key
DEXTOOLS_BASE_URL=https://api.dextools.io/v1

MORALIS_API_KEY=your_moralis_api_key
MORALIS_BASE_URL=https://deep-index.moralis.io/api/v2.2

# Blockchain Explorer APIs
ETHERSCAN_API_KEY=your_etherscan_api_key
ETHERSCAN_BASE_URL=https://api.etherscan.io/api

SOLSCAN_API_KEY=your_solscan_api_key
SOLSCAN_BASE_URL=https://pro-api.solscan.io/v2.0
SOLSCAN_PUBLIC_URL=https://public-api.solscan.io

# Scraping Configuration
SCRAPING_DELAY=1
MAX_CONCURRENT_REQUESTS=10
USER_AGENT=Web3GamingNewsBot/1.0 (+https://yoursite.com)
RESPECT_ROBOTS_TXT=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=UTC

# Monitoring
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
SENTRY_DSN=your_sentry_dsn

# Gaming Categories
GAMING_KEYWORDS=play-to-earn,P2E,NFT gaming,GameFi,metaverse,blockchain gaming,crypto gaming,gaming tokens,gaming NFTs,virtual worlds
P2E_GAMES=axie-infinity,splinterlands,gods-unchained,alien-worlds,the-sandbox,decentraland
GAMING_TOKENS=AXS,SLP,SAND,MANA,ENJ,GALA,ILV,ALICE,TLM,SKILL

# Blockchain Gaming Contracts (examples)
AXIE_INFINITY_CONTRACT=******************************************
SANDBOX_CONTRACT=******************************************
DECENTRALAND_CONTRACT=******************************************

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Environment
ENVIRONMENT=development
