#!/usr/bin/env python3
"""
Test script for the webhook system.
Demonstrates webhook functionality and integration with blockchain scraper.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.insert(0, '.')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_webhook_system():
    """Test the webhook system functionality."""
    
    print("🚀 Testing Web3 Gaming Webhook System")
    print("=" * 60)
    
    # Import webhook components
    from webhooks.manager import webhook_manager
    from webhooks.event_bus import event_bus, emit_blockchain_event, emit_gaming_event, emit_nft_event
    from webhooks.events import WebhookEventType
    
    # Start webhook system
    print("📡 Starting webhook system...")
    await event_bus.start()
    await webhook_manager.start()
    
    # Add a test internal handler
    def test_handler(event):
        print(f"🎯 Internal handler received: {event.event_type.value} on {event.blockchain}")
        print(f"   Event ID: {event.id}")
        print(f"   Timestamp: {event.timestamp}")
        if hasattr(event, 'contract_address') and event.contract_address:
            print(f"   Contract: {event.contract_address}")
        print()
    
    # Subscribe to events
    event_bus.subscribe("block_mined", test_handler)
    event_bus.subscribe("contract_deployed", test_handler)
    event_bus.subscribe("gaming_transaction", test_handler)
    event_bus.subscribe("nft_transferred", test_handler)
    
    print("✅ Webhook system started")
    print("📊 Initial stats:")
    print(f"   Event bus: {event_bus.get_stats()}")
    print(f"   Webhook manager: {webhook_manager.get_stats()}")
    print()
    
    # Test 1: Emit blockchain events
    print("🧪 Test 1: Emitting blockchain events...")
    await emit_blockchain_event(
        event_type=WebhookEventType.BLOCK_MINED,
        blockchain="ethereum",
        block_number=22923800,
        data={"blocks_processed": 100, "gaming_contracts_found": 3}
    )
    
    await emit_blockchain_event(
        event_type=WebhookEventType.CONTRACT_DEPLOYED,
        blockchain="polygon",
        contract_address="******************************************",
        block_number=73976000,
        data={"contract_type": "gaming", "game_name": "Test Game"}
    )
    
    # Test 2: Emit gaming events
    print("🎮 Test 2: Emitting gaming events...")
    await emit_gaming_event(
        event_type=WebhookEventType.GAMING_TRANSACTION,
        blockchain="ethereum",
        gaming_project="Axie Infinity",
        game_name="Axie Infinity",
        gaming_action="token_transfer",
        data={"amount": "1000", "token": "AXS"}
    )
    
    # Test 3: Emit NFT events
    print("🖼️ Test 3: Emitting NFT events...")
    await emit_nft_event(
        event_type=WebhookEventType.NFT_TRANSFERRED,
        blockchain="ethereum",
        contract_address="******************************************",
        token_id="12345",
        collection_name="Axie Infinity",
        from_address="0xabc123",
        to_address="0xdef456",
        price=0.5,
        currency="ETH",
        data={"marketplace": "OpenSea"}
    )
    
    # Wait for events to process
    await asyncio.sleep(2)
    
    # Show final stats
    print("📈 Final stats:")
    print(f"   Event bus: {event_bus.get_stats()}")
    print(f"   Webhook manager: {webhook_manager.get_stats()}")
    
    # Stop webhook system
    await webhook_manager.stop()
    await event_bus.stop()
    
    print("✅ Webhook system test completed!")


async def test_with_blockchain_scraper():
    """Test webhook integration with the actual blockchain scraper."""
    
    print("\n🔗 Testing Webhook Integration with Blockchain Scraper")
    print("=" * 60)
    
    # Set database URL
    os.environ['DATABASE_URL'] = 'postgresql://postgres:postgres@localhost:5432/gaming_tracker'
    
    from scrapers.blockchain.scraper_manager import blockchain_scraper_manager
    from webhooks.manager import webhook_manager
    from webhooks.event_bus import event_bus
    
    # Add webhook subscriber for testing
    def gaming_event_handler(event):
        print(f"🎮 Gaming webhook received: {event.event_type.value}")
        print(f"   Blockchain: {event.blockchain}")
        if hasattr(event, 'gaming_project') and event.gaming_project:
            print(f"   Gaming Project: {event.gaming_project}")
        if hasattr(event, 'contract_address') and event.contract_address:
            print(f"   Contract: {event.contract_address}")
        print()
    
    # Subscribe to gaming events
    event_bus.subscribe("gaming_transaction", gaming_event_handler)
    event_bus.subscribe("contract_deployed", gaming_event_handler)
    event_bus.subscribe("block_mined", gaming_event_handler)
    
    print("📡 Starting integrated test...")
    
    # Start the blockchain scraper (it will start webhooks automatically)
    scraper_task = asyncio.create_task(blockchain_scraper_manager.start_scraping())
    
    # Let it run for 30 seconds
    print("⏱️ Running blockchain scraper with webhooks for 30 seconds...")
    await asyncio.sleep(30)
    
    # Stop the scraper
    await blockchain_scraper_manager.stop_scraping()
    scraper_task.cancel()
    
    # Show webhook stats
    print("📊 Webhook integration stats:")
    print(f"   Event bus: {event_bus.get_stats()}")
    print(f"   Webhook manager: {webhook_manager.get_stats()}")
    
    print("✅ Blockchain scraper webhook integration test completed!")


async def main():
    """Main test function."""
    try:
        # Test 1: Basic webhook system
        await test_webhook_system()
        
        # Test 2: Integration with blockchain scraper
        await test_with_blockchain_scraper()
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test error")


if __name__ == "__main__":
    asyncio.run(main())
