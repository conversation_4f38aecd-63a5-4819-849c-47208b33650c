import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Games as GamesIcon,
  Webhook as WebhookIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { systemAPI, webhookAPI, liveBlockchainAPI, gamingAPI } from '../services/api';

const SystemMonitoring = () => {
  const [systemStatus, setSystemStatus] = useState(null);
  const [gamingProjects, setGamingProjects] = useState([]);
  const [webhookSubscriptions, setWebhookSubscriptions] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchSystemData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch system status
      const statusResponse = await systemAPI.getStatus();
      setSystemStatus(statusResponse);

      // Fetch gaming projects
      const projectsResponse = await gamingAPI.getProjects();
      setGamingProjects(projectsResponse.projects || []);

      // Fetch webhook subscriptions
      const webhooksResponse = await webhookAPI.getSubscriptions();
      setWebhookSubscriptions(webhooksResponse.subscriptions || []);

      // Fetch recent blockchain activity
      const activityResponse = await liveBlockchainAPI.getRecentActivity();
      setRecentActivity(activityResponse.activities || []);

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching system data:', err);
      setError('Failed to fetch system data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return <CheckCircleIcon />;
      case 'degraded':
        return <WarningIcon />;
      case 'error':
        return <ErrorIcon />;
      default:
        return <CircularProgress size={20} />;
    }
  };

  if (loading && !systemStatus) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={fetchSystemData}>
          Retry
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          System Monitoring
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchSystemData}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* System Status Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="System Status"
              avatar={<CheckCircleIcon color="success" />}
            />
            <CardContent>
              {systemStatus && (
                <>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Typography variant="body1" sx={{ mr: 2 }}>
                      Overall Status:
                    </Typography>
                    <Chip
                      icon={getStatusIcon(systemStatus.status)}
                      label={systemStatus.status}
                      color={getStatusColor(systemStatus.status)}
                      variant="outlined"
                    />
                  </Box>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <List dense>
                    <ListItem>
                      <ListItemIcon>
                        <StorageIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Database"
                        secondary={
                          <Chip
                            size="small"
                            label={systemStatus.components?.database?.status || 'Unknown'}
                            color={getStatusColor(systemStatus.components?.database?.status)}
                          />
                        }
                      />
                    </ListItem>
                    
                    <ListItem>
                      <ListItemIcon>
                        <MemoryIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Redis Cache"
                        secondary={
                          <Chip
                            size="small"
                            label={systemStatus.components?.redis?.status || 'Unknown'}
                            color={getStatusColor(systemStatus.components?.redis?.status)}
                          />
                        }
                      />
                    </ListItem>
                  </List>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Database Metrics Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader
              title="Database Metrics"
              avatar={<StorageIcon />}
            />
            <CardContent>
              {systemStatus?.components?.database?.table_counts && (
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <GamesIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Gaming Projects"
                      secondary={systemStatus.components.database.table_counts.gaming_projects}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <WebhookIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Webhook Subscriptions"
                      secondary={systemStatus.components.database.table_counts.webhook_subscriptions}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon>
                      <TimelineIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Blockchain Events"
                      secondary={systemStatus.components.database.table_counts.blockchain_data}
                    />
                  </ListItem>
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Gaming Projects */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Recent Gaming Projects" />
            <CardContent>
              {gamingProjects.length > 0 ? (
                <List dense>
                  {gamingProjects.slice(0, 5).map((project, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={project.project_name}
                        secondary={`${project.blockchain} • ${project.primary_genre}`}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No gaming projects found
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Blockchain Activity */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Recent Blockchain Activity" />
            <CardContent>
              {recentActivity.length > 0 ? (
                <List dense>
                  {recentActivity.slice(0, 5).map((activity, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={`${activity.event_type} on ${activity.blockchain}`}
                        secondary={
                          activity.token_price_usd 
                            ? `Value: $${activity.token_price_usd}` 
                            : activity.contract_address?.substring(0, 10) + '...'
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No recent activity found
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {lastUpdated && (
        <Box mt={3} textAlign="center">
          <Typography variant="caption" color="text.secondary">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default SystemMonitoring;
